import { Button, Empty, Input, Select, Space, Table, message } from "antd";
import { ColumnsType } from "antd/es/table";
import {
  forwardRef,
  ReactNode,
  useEffect,
  useState,
  useImperativeHandle,
  useRef,
} from "react";
import { DataSetStatus, DataSetType } from "../types";
import {
  CheckOutlined,
  CloseOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  GetDataSetsParams,
  changeDataSetsTags,
  getDataSets,
  renameDataSets,
} from "../api/dataset";
import { formattedTime } from "../utils/formatred";
import TagList from "./TagList";
import RelatedTasksDropdown from "./RelatedTasksDropdown";
import UploadErrorModal from "./UploadErrorModal";
import PreviewFolderModal from "./PreviewFolderModal";
import TablePagination from "./TablePagination";
import emptyLogo from "../assets/img/empty-logo.svg";

interface DatasetTableProp {
  ShowActionColumn?: boolean;
  Extra?: ReactNode;
  onSelectRows: (data: DataSetType[]) => void;
}
const InputComponent = (props: any) => {
  const {
    dataSet,
    datasets,
    index,
    renameRows,
    setRenameRows,
    setDatasets,
    handleRefresh,
  } = props;
  const [renameInput, setRenameInput] = useState<string>(dataSet.datasetName);
  return (
    <>
      <Input
        placeholder="请输入数据集名称"
        value={renameInput}
        onChange={(e) => {
          if (e.target.value.length > 0) {
            setRenameInput(e.target.value);
          } else {
            setRenameInput("");
          }
        }}
      ></Input>
      <Button
        icon={<CheckOutlined />}
        onClick={() => {
          const newName: string = renameInput?.trim() || "";
          if (newName && newName.length > 0) {
            const data = datasets;
            const name = (data[index].name = newName as any);
            // data[index].renameFlg = false;
            const updateRenameSets = renameRows;
            updateRenameSets.delete(dataSet.id);
            setRenameRows(updateRenameSets);
            setRenameInput("");
            setDatasets(data);
            const id = data[index].id;
            renameDataSets({ datasetId: id, datasetName: name }).then((res) => {
              handleRefresh();
            });
          } else {
            message.error("数据集名称不合法");
          }
        }}
      />
      <Button
        icon={<CloseOutlined />}
        onClick={() => {
          const updateRenameSets = renameRows;
          updateRenameSets.delete(dataSet.id);
          setRenameRows(updateRenameSets);
          setRenameInput("");
        }}
      />
    </>
  );
};
const DatasetTable = forwardRef((prop: DatasetTableProp, ref) => {
  const { ShowActionColumn, Extra, onSelectRows } = prop;
  useImperativeHandle(ref, () => ({
    onRefresh: handleRefresh,
    handleDeleteRows: handleDeleteRows,
    removeSelection: (datasetId: string) => {
      const updatedSelectedRows = selectedRows.filter(row => row.id !== datasetId);
      const updatedSelectedRowKeys = selectedRowKeys.filter(key => key !== datasetId);
      setSelectedRows(updatedSelectedRows);
      setSelectedRowKeys(updatedSelectedRowKeys);
      onSelectRows(updatedSelectedRows);
    },
  }));
  const [uploadErrorModal, setUploadErrorModal] = useState<boolean>(false);
  const [selectDataSet, setSelectDataSet] = useState<DataSetType>(
    new Object() as DataSetType
  );
  const [previewModal, setPreviewModal] = useState(false);
  // const [previewDataList, setPreviewDataList] = useState<ParseDatasetType[]>(
  //   []
  // );
  const [selectedRows, setSelectedRows] = useState<DataSetType[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [renameRows, setRenameRows] = useState<Set<string>>(new Set<string>());
  const [datasets, setDatasets] = useState<DataSetType[]>([]);
  // 定义分页数据
  const [pagination, setPagination] = useState({
    page: 1,
    size: 10,
    total: 0,
  });
  const [filterAttribute, setFilterAttribute] = useState("datasetName");
  const [sortAttribute, setSortAttribute] = useState("createTime");
  const [sortDirection, setSortDirection] = useState("desc");
  const [filterInput, setFilterInput] = useState("");
  const intervalRef = useRef<NodeJS.Timeout>();
  const [apiFlg, setApiFlg] = useState(false);

  const [tableIndex, setTableIndex] = useState(0);
  const [isonClick, setIsonClick] = useState(false);

  useEffect(() => {}, [selectDataSet]);

  function getProducts() {
    if (!apiFlg) {
      setApiFlg(true);
      const params: GetDataSetsParams = {
        ...pagination,
        filterAttribute,
        sortAttribute,
        sortDirection,
        datasetName: filterAttribute === "datasetName" ? filterInput : "",
        tag: filterAttribute !== "datasetName" ? filterInput : "",
        startTime: "",
        endTime: "",
      };
      getDataSets(params).then((res) => {
        setApiFlg(false);
        if (res.data?.code === 200) {
          setDatasets(res.data?.data);
          setPagination({ ...pagination, total: res.data?.totalCount });
          setTableIndex((pagination.page - 1) * pagination.size);
        }
      });
    }
  }

  const handleRefresh = async () => {
    const params: GetDataSetsParams = {
      ...pagination,
      filterAttribute,
      sortAttribute,
      sortDirection,
      datasetName: filterAttribute === "datasetName" ? filterInput : "",
      tag: filterAttribute !== "datasetName" ? filterInput : "",
      startTime: "",
      endTime: "",
    };
    const res = await getDataSets(params);
    if (res.data?.code === 200) {
      setDatasets(res.data?.data);
      setPagination({ ...pagination, total: res.data?.totalCount });
    }
  };

  const handleDeleteRows = (idList: string[]) => {
    setDatasets((pre) => pre.filter((item) => !idList.includes(item.id)));
  };

  // 请求数据
  useEffect(() => {
    getProducts();
  }, []);

  useEffect(() => {
    // clearInterval(intervalRef.current);
    getProducts();
    // intervalRef.current = setInterval(getProducts, 5000);
    // return () => {
    //   if (intervalRef.current) clearInterval(intervalRef.current); // 在组件卸载时清除定时器
    // };
  }, [
    pagination.page,
    pagination.size,
    sortDirection,
    filterAttribute,
    sortAttribute,
    filterInput,
  ]);

  const rowSelection = {
    columnWidth: "32px",
    selectedRowKeys: selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[], rows: DataSetType[]) => {
      console.log("selectedRowKeys", newSelectedRowKeys, "selectedRows: ", rows);
      setSelectedRowKeys(newSelectedRowKeys);
      setSelectedRows(rows);
      onSelectRows(rows);
    },
    getCheckboxProps: (record: DataSetType) => ({
      name: record.name,
      disabled: record.datasetStatus !== DataSetStatus.sucess,
    }),
  };

  const columns: ColumnsType<DataSetType> = [
    {
      key: "index",
      title: "序号",
      dataIndex: "index",
      render: (text, record, index) => <>{tableIndex + index + 1}</>,
      // width: "4rem",
      width: "4.6%",
    },
    {
      key: "name",
      title: "数据集名称",
      dataIndex: "datasetName",
      ellipsis: true,
      shouldCellUpdate: (record: DataSetType, prevRecord: DataSetType) => {
        return true;
      },
      render: (_, dataSet, index) => {
        let { id: dataSetId, datasetName, childFiles } = dataSet;
        return !renameRows.has(dataSetId) ? (
          <a
            className="dataset-name"
            onClick={(e) => {
              e.preventDefault();
              setSelectDataSet(dataSet);
              if (!previewModal) {
                setPreviewModal(true);
                // clearInterval(intervalRef.current);
              }
            }}
          >
            {datasetName}
          </a>
        ) : (
          <>
            <Space.Compact>
              <InputComponent
                dataSet={dataSet}
                datasets={datasets}
                index={index}
                renameRows={renameRows}
                setRenameRows={setRenameRows}
                setDatasets={setDatasets}
                handleRefresh={handleRefresh}
              ></InputComponent>
              {/* <Input
                placeholder="请输入数据集名称"
                value={renameInput || datasetName}
                onChange={(e) => {
                 
                  if (e.target.value.length > 0) {
                    setRenameInput(e.target.value);
                  }else{
                    setRenameInput("");
                  }
                }}
              ></Input>
              <Button
                icon={<CheckOutlined />}
                onClick={() => {
                  const newName: string = renameInput?.trim() || "";
                  if (newName && newName.length > 0) {
                    const data = datasets;
                    const name = (data[index].name = newName as any);
                    // data[index].renameFlg = false;
                    const updateRenameSets = renameRows;
                    updateRenameSets.delete(dataSetId);
                    setRenameRows(updateRenameSets);
                    setRenameInput("");
                    setDatasets(data);
                    const id = data[index].id;
                    renameDataSets({ datasetId: id, datasetName: name }).then(
                      (res) => {
                        handleRefresh();
                      }
                    );
                  } else {
                    message.error("数据集名称不合法");
                  }
                }}
              />
              <Button
                icon={<CloseOutlined />}
                onClick={() => {
                  const updateRenameSets = renameRows;
                  updateRenameSets.delete(dataSetId);
                  setRenameRows(updateRenameSets);
                  setRenameInput("");
                }}
              /> */}
            </Space.Compact>
          </>
        );
      },
      // width: "14rem",
      width: ShowActionColumn ? "14.2%" : "",
    },
    {
      key: "createTime",
      title: "上传时间",
      dataIndex: "createTime",
      // render: (_, { createTime }) => {
      //   const timestamp = createTime * 1000;
      //   return formattedTime(new Date(timestamp));
      // },
      // width: "10rem",
      width: ShowActionColumn ? "14.6%" : "",
    },
    {
      key: "datasetStatus",
      title: "解析状态",
      dataIndex: "datasetStatus",
      render: (_, { datasetStatus, total, complete, failedReason }) => {
        if (datasetStatus === DataSetStatus.sucess) {
          return (
            <label className={!failedReason ? undefined : "warning-info"}>
              解析完成 ({`${complete}/${total}`})
            </label>
          );
        } else if (datasetStatus === DataSetStatus.failed) {
          return <label className="error-info">解析失败</label>;
        } else if (datasetStatus === DataSetStatus.inProgress) {
          if (complete === 0 && total === 0) {
            return <label>待解析</label>;
          } else {
            return <label>解析中 ({`${complete}/${total}`})</label>;
          }
        }
      },
      // width: "10rem",
      width: ShowActionColumn ? "9.6%" : "",
    },
    {
      key: "tags",
      title: "标签",
      ellipsis: true,
      dataIndex: "tags",
      render: (_, rowData, index) => {
        let { tags, id: dataSetId } = rowData;
        return (
          <TagList
            tagList={tags}
            dataSetId={dataSetId}
            onChange={(updateTags) => {
              tags = updateTags;
              const updateDataSets = datasets;
              updateDataSets.forEach((dataSet) => {
                if (dataSet.id === dataSetId) {
                  dataSet.tags = updateTags;
                }
              });
              setDatasets(updateDataSets);
              changeDataSetsTags({ dataSetId, tags }).then(
                (res) => {
                  handleRefresh();
                },
                (error) => {
                  console.log("changeDataSetsTags", error);
                  message.error(error);
                }
              );
            }}
            showAddBtn={true}
            OnEdit={(edit) => {
              setApiFlg(edit);
            }}
          ></TagList>
        );
      },
      shouldCellUpdate: (record: DataSetType, prevRecord: DataSetType) => {
        return true;
      },
      //width: "16rem",
      width: ShowActionColumn ? "18.2%" : "",
    },
    {
      key: "sources",
      title: "来源",
      ellipsis: true,
      dataIndex: "sources",
      width: "10%",
      render: (_, dataSet, index) => {
        let { id: dataSetId, name } = dataSet;
        return !renameRows.has(dataSetId) ? (
          <div>本地上传</div>
        ) : (
          <div>GDP</div>
        );
      },
    },

    {
      key: "action",
      title: "操作",
      dataIndex: "datasetStatus",
      render: (_, dataSet) => {
        const {
          datasetStatus,
          name: datasetName,
          childFiles,
          failedReason,
        } = dataSet;
        if (datasetStatus === DataSetStatus.sucess && !failedReason) {
          return (
            <Button
              type="link"
              className="grid-link-btn"
              onClick={() => {
                setSelectDataSet(dataSet);
                if (!previewModal) {
                  setPreviewModal(true);
                  clearInterval(intervalRef.current);
                }
              }}
            >
              详情
            </Button>
          );
        } else if (datasetStatus === DataSetStatus.sucess && failedReason) {
          return (
            <Button
              type="link"
              className="grid-link-btn"
              onClick={() => {
                setUploadErrorModal(true);
              }}
            >
              问题详情
            </Button>
          );
        } else if (datasetStatus === DataSetStatus.failed) {
          return (
            <Button
              type="link"
              className="grid-link-btn"
              // disabled={isonClick}
              disabled={true}
              onClick={() => {
                // retryTask().then(res => {
                // })
                message.warning("暂不支持此功能，更多优化尽请期待");
                setIsonClick(true);
              }}
            >
              无法解析
              {/* {isonClick ? "无法解析" : "重新解析"} */}
            </Button>
          );
        } else if (datasetStatus === DataSetStatus.inProgress) {
          return null;
        }
      },
      width: "7.8%",
    },

    {
      key: "dropdown",
      title: " ",
      dataIndex: "dropdown",
      ellipsis: true,
      render: (_, rowData, index) => {
        let { tags, id: dataSetId } = rowData;
        return (
          <RelatedTasksDropdown
            key={index}
            tags={tags}
            rowData={rowData}
            OnEdit={() => {}}
            OnChange={(updateTags) => {
              const updateDataSets = datasets;
              updateDataSets.forEach((dataSet) => {
                if (dataSet.id === dataSetId) {
                  dataSet.tags = updateTags;
                }
              });
              setDatasets(updateDataSets);
              tags = updateTags;
              handleRefresh();
            }}
            OnDelete={async (idList: string[]) => {
              setApiFlg(true);
              handleDeleteRows(idList);
              await handleRefresh();
              setApiFlg(false);
            }}
            OnRename={() => {
              const updateRenameSets = renameRows;
              updateRenameSets.add(dataSetId);
              setRenameRows(updateRenameSets);
            }}
          ></RelatedTasksDropdown>
        );
      },
      width: "16%",
    },
  ];

  // const displayColumn = ShowActionColumn ? columns : columns.slice(0, 5);
  const displayColumn = columns;

  return (
    <div style={{ display: "flex", flexDirection: "column", flex: 1 }}>
      <div
        style={{
          textAlign: "start",
          justifyContent: "space-between",
          display: "inline-flex",
          marginBottom: "1rem",
          width: "100%",
        }}
      >
        <div>
          <Space size="small">
            <Space.Compact>
              <Select
                size="large"
                className="filter-select"
                value={filterAttribute}
                onChange={(val) => setFilterAttribute(val)}
                options={[
                  { value: "datasetName", label: "数据集" },
                  { value: "tag", label: "标签" },
                ]}
              />
              <Input
                size="large"
                className="filter-input"
                suffix={<SearchOutlined />}
                placeholder={`请输入${
                  filterAttribute === "datasetName" ? "数据集" : "标签"
                }名称`}
                value={filterInput}
                onChange={(e) => {
                  setFilterInput(e.target.value);
                }}
              />
            </Space.Compact>
            <Select
              size="large"
              className="filter-select"
              value={sortDirection}
              onChange={(e) => setSortDirection(e)}
              style={{ width: "9.75rem" }}
              options={[
                { value: "desc", label: "按时间倒序" },
                { value: "asc", label: "按时间正序" },
              ]}
            />
          </Space>
        </div>
        {Extra}
      </div>
      <Table
        // scroll={{ y: 490 }}
        style={{ flex: 1 }}
        loading={apiFlg}
        locale={{
          emptyText: (
            <Empty
              image={emptyLogo}
              description={
                <span className="dataset-table-empty-label">
                 {apiFlg?"请求中": "空空如也，去上传本地文件吧~"}
                </span>
              }
            />
          ),
        }}
        rowKey="id"
        rowSelection={{
          type: "checkbox",
          ...rowSelection,
        }}
        size="small"
        className="dataset-table"
        columns={displayColumn}
        dataSource={datasets}
        tableLayout={"fixed"}
        pagination={false}
      />
      <TablePagination
        total={pagination.total}
        pageSize={pagination.size}
        page={pagination.page}
        OnChange={(page, pageSize) => {
          setPagination({ page, size: pageSize, total: pagination.total });
        }}
      />

      <UploadErrorModal
        visible={uploadErrorModal}
        OnClose={(rows) => {
          console.log(rows);
          setUploadErrorModal(false);
        }}
      ></UploadErrorModal>
      <PreviewFolderModal
        visible={previewModal}
        dataSet={selectDataSet}
        OnClose={() => {
          setPreviewModal(false);
          getProducts();
          intervalRef.current = setInterval(getProducts, 5000);
        }}
      ></PreviewFolderModal>
    </div>
  );
});

export default DatasetTable;

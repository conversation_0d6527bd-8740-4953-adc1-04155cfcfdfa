import { But<PERSON>, <PERSON><PERSON>, <PERSON>, Space } from "antd";
import { useState } from "react";
import { TaskType } from "../types";
import { saveAs } from 'file-saver';
import JSZip from 'jszip';

interface DatasetExportModalProp {
  exportTaskData?: TaskType[],
  visible: boolean,
  OnClose: () => void,
  OnQADonload?:() => void,
}

const DatasetExportModal: React.FC<DatasetExportModalProp> = ({ exportTaskData, visible, OnClose, OnQADonload }) => {
  // const fileInputRef = useRef<HTMLInputElement>(null);
  const [exportType, setExportType] = useState<string>('json');
  const [exportPath, setExportPath] = useState<string>();
  const handleButtonClick = () => {

    const input = document.getElementById('exportPathInput');
    if (input) {
      input.click();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      const downloadUrl = URL.createObjectURL(selectedFile);
      // window.open(downloadUrl, '_blank');
      setExportPath(selectedFile.webkitRelativePath);
    }
  };

  // if (window.navigator.serviceWorker) {

  //   window.navigator.serviceWorker.ready
  //     .then(registration => {

  //       // 获取下载管理器
  //       (registration as any).downloads.getDefaultDownloadsDirectory()
  //         .then((dir: any) => {
  //           console.log(dir);
  //         })

  //     });

  // }
  return (
    <>
      <Modal
        centered
        title="数据导出"
        keyboard={false}
        maskClosable={false}
        width={"520px"}
        styles={{body:{ height: "102px"} }}
        open={visible}
        onOk={OnClose}
        onCancel={OnClose}
        footer={[
          <Button type="primary" className="primary-btn" style={{ width: "124px" }} onClick={() => {
            if(exportTaskData) {
              const zip = new JSZip();
              exportTaskData.forEach(taskData => {
                const fileContent = JSON.stringify(taskData);
                zip.file(`${taskData.taskName}.json`, fileContent);
              });
              zip.generateAsync({ type: "blob" }).then(content => {
                saveAs(content, "任务数据.zip");
              });
            }
            if(OnQADonload) {
              OnQADonload();
            }
            OnClose();
          }}>
            导出
          </Button>,
        ]}
      >
        <Space direction="vertical" size={20}>
          <Space direction="vertical" size={12}>
            <div>导出格式</div>
            <Select
              size="large"
              style={{ width: "156px" }}
              value={exportType}
              onChange={(val) => setExportType(val)}
              options={[{ value: "json", label: "json" }]}
            />
          </Space>
          {/* <Space direction="vertical" size={12}>
            <div>目标文件夹</div>

            <Space.Compact>
              <Input
                value={exportPath}
                readOnly={true}
                style={{ width: "480px", height: "40px" }}
              />
              <Button
                type="text"
                size="large"
                onClick={() => {
                  document.getElementById("exportPathInput")?.click();
                }}
              >
                <EllipsisOutlined />
              </Button>
            </Space.Compact>
            <input
              type="file"
              role="directory"
              // ref={fileInputRef}
              id="exportPathInput"
              style={{ display: "none" }}
              onChange={handleFileChange}
            />
          </Space> */}
        </Space>
      </Modal>
    </>
  );
}

export default DatasetExportModal
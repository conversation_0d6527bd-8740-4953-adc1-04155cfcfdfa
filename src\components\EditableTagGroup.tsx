import { Tag, Input, InputRef, Tooltip, message } from 'antd';
import { PlusOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { useState, useRef, useEffect } from 'react';

type EditableTagGroupProps = {
  tags: string[];
  onTagsChange: (tags: string[]) => void;
  availableTags?: string[]; // 可选标签列表
  onAvailableTagsChange?: (availableTags: string[]) => void; // 可选标签变更回调
  maxTags?: number; // 最大标签数量，默认3
  maxAvailableTags?: number; // 最大可选标签数量，默认20
};

const EditableTagGroup: React.FC<EditableTagGroupProps> = ({
  tags,
  onTagsChange,
  availableTags = [],
  onAvailableTagsChange,
  maxTags = 3,
  maxAvailableTags = 20
}) => {
  const [inputVisible, setInputVisible] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('');
  const [editInputIndex, setEditInputIndex] = useState<number>(-1);
  const [editInputValue, setEditInputValue] = useState<string>('');
  const [showAvailableTags, setShowAvailableTags] = useState<boolean>(false);
  const [customInputVisible, setCustomInputVisible] = useState<boolean>(false);
  const [customInputValue, setCustomInputValue] = useState<string>('');
  const inputRef = useRef<InputRef>(null);
  const editInputRef = useRef<InputRef>(null);
  const customInputRef = useRef<InputRef>(null);
  const [hoveredTagIndex, setHoveredTagIndex] = useState<number | null>(null);
  const [isHoveringTag, setIsHoveringTag] = useState<boolean>(false);

  // 使用 ref 来避免闭包问题
  const showAvailableTagsRef = useRef<boolean>(showAvailableTags);
  showAvailableTagsRef.current = showAvailableTags;

  // 使用传入的可选标签数据，并按字母顺序排序
  const currentAvailableTags = [...availableTags].sort((a, b) => a.localeCompare(b, 'zh-CN'));

  // 重置编辑状态当tags变化时
  useEffect(() => {
    setEditInputIndex(-1);
  }, [tags]);

  useEffect(() => {
    if (inputVisible && inputRef.current) {
      inputRef.current.focus();
    }
  }, [inputVisible]);

  useEffect(() => {
    if (editInputIndex !== -1 && editInputRef.current) {
      editInputRef.current.focus();
    }
  }, [editInputIndex]);

  useEffect(() => {
    if (customInputVisible && customInputRef.current) {
      customInputRef.current.focus();
    }
  }, [customInputVisible]);

  // 按字母顺序排序标签
  const sortedTags = [...tags].sort((a, b) => a.localeCompare(b, 'zh-CN'));

  const handleClose = (removedTag: string) => {
    const newTags = tags.filter(tag => tag !== removedTag);
    onTagsChange(newTags);

    // 如果可选标签面板正在显示，将删除的标签加入到可选标签列表中
    if (showAvailableTags && onAvailableTagsChange && !availableTags.includes(removedTag)) {
      const newAvailableTags = [...availableTags, removedTag].sort((a, b) => a.localeCompare(b, 'zh-CN'));
      onAvailableTagsChange(newAvailableTags);
    }
  };

  const handleTagSelect = (selectedTag: string) => {
    if (!tags.includes(selectedTag) && tags.length < maxTags) {
      const newTags = [...tags, selectedTag];
      onTagsChange(newTags);

      // 从可选标签列表中移除已选择的标签
      if (onAvailableTagsChange) {
        const newAvailableTags = availableTags.filter(tag => tag !== selectedTag);
        onAvailableTagsChange(newAvailableTags);
      }

      // 不关闭可选标签面板
      // setShowAvailableTags(false);
    }
  };

  const handleCustomInputConfirm = () => {
    if (customInputValue && !tags.includes(customInputValue) && tags.length < maxTags) {
      const newTags = [...tags, customInputValue];
      onTagsChange(newTags);

      // 如果自定义标签在可选标签列表中，将其移除
      if (onAvailableTagsChange && availableTags.includes(customInputValue)) {
        const newAvailableTags = availableTags.filter(tag => tag !== customInputValue);
        onAvailableTagsChange(newAvailableTags);
      }
    }
    setCustomInputVisible(false);
    setCustomInputValue('');
    // 不关闭可选标签面板
    // setShowAvailableTags(false);
  };

  const showInput = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setInputVisible(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputConfirm = () => {
    if (inputValue && !tags.includes(inputValue)) {
      onTagsChange([...tags, inputValue]);
    }
    setInputVisible(false);
    setInputValue('');
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value);
  };

  const handleEditInputConfirm = () => {
    if (editInputIndex === -1) return;

    const newTags = [...tags];
    newTags[editInputIndex] = editInputValue;
    onTagsChange(newTags);
    setEditInputIndex(-1);
  };

  const showAddTagsPanel = () => {
    if (tags.length >= maxTags) {
      message.warning('当前标签已满，请先删除标签再添加新标签');
      return;
    }
    setShowAvailableTags(true);
  };

  const hideAddTagsPanel = () => {
    setShowAvailableTags(false);
    setCustomInputVisible(false);
    setCustomInputValue('');
  };

  // 获取未使用的可选标签
  const getUnusedTags = () => {
    return currentAvailableTags
      .filter(tag => !tags.includes(tag))
      .slice(0, maxAvailableTags);
  };
  // 添加全局点击事件监听器，使用 ref 避免闭包问题
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as Element;
      const panel = document.querySelector('.available-tags-panel');
      const addButton = document.querySelector('.site-tag-plus');

      // 更精确地检查是否点击了标签的关闭按钮
      const isTagCloseButton =
        target.closest('.ant-tag-close-icon') ||
        target.closest('.anticon-close') ||
        target.closest('span[role="img"][aria-label="close"]') ||
        target.classList.contains('ant-tag-close-icon') ||
        target.classList.contains('anticon-close') ||
        // 检查是否是SVG图标或其子元素
        target.closest('svg') && target.closest('.ant-tag') ||
        // 检查是否在可关闭的标签内
        (target.closest('.ant-tag') && target.closest('.ant-tag')?.querySelector('.ant-tag-close-icon'));

      // 如果点击的是标签关闭按钮，不关闭面板
      if (isTagCloseButton) {
        return;
      }

      // 使用 ref 来获取当前的 showAvailableTags 状态，避免闭包问题
      const currentShowAvailableTags = showAvailableTagsRef.current;

      // 检查点击是否在面板外部和添加按钮外部
      if (
        currentShowAvailableTags &&
        panel &&
        !panel.contains(e.target as Node) &&
        addButton &&
        !addButton.contains(e.target as Node)
      ) {
        setShowAvailableTags(false);
        setCustomInputVisible(false);
        setCustomInputValue('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []); // 空依赖数组，只在组件挂载时添加一次事件监听器

  useEffect(() => {
    console.log('showAvailableTags', showAvailableTags);
  }, [showAvailableTags]);

  return (
    <div style={{ position: 'relative' }}>
      {/* 标签显示区域 */}
      <div
        className="qa-tags"
        onClick={(e) => e.stopPropagation()}
        onMouseEnter={() => setIsHoveringTag(true)}
        onMouseLeave={() => setIsHoveringTag(false)}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          flexWrap: 'wrap',
          minHeight: '32px',
          padding: '4px 0',
          transition: 'all 0.3s ease'
        }}
      >
        {/* 显示当前标签 */}
        {sortedTags.map((tag, index) => {
          // if (editInputIndex === tags.indexOf(tag)) {
          //   return (
          //     <Input
          //       ref={editInputRef}
          //       key={`edit-${index}`}
          //       size="small"
          //       className="tag-input"
          //       value={editInputValue}
          //       onChange={handleEditInputChange}
          //       onBlur={handleEditInputConfirm}
          //       onPressEnter={handleEditInputConfirm}
          //       onClick={(e) => e.stopPropagation()}
          //       style={{ width: '80px' }}
          //     />
          //   );
          // }

          return (
            <Tag
              className="edit-tag"
              key={`tag-${index}`}
              closable={hoveredTagIndex === tags.indexOf(tag)}
              color="#D8F2EF"
              onClose={(e) => {
                e.preventDefault();
                handleClose(tag);
              }}
              onClick={(e) => {
                e.stopPropagation();
                setEditInputIndex(tags.indexOf(tag));
                setEditInputValue(tag);
              }}
              onMouseEnter={() => setHoveredTagIndex(tags.indexOf(tag))}
              onMouseLeave={() => setHoveredTagIndex(null)}
            >
              {tag}
            </Tag>
          );
        })}
        {customInputVisible && (
          <Input
            ref={customInputRef}
            size="small"
            placeholder="输入自定义标签"
            value={customInputValue}
            onChange={(e) => setCustomInputValue(e.target.value)}
            onBlur={handleCustomInputConfirm}
            onPressEnter={handleCustomInputConfirm}
            style={{ width: '120px' }}
          />
        )}

        {/* 添加按钮 */}
        {(sortedTags.length === 0 || isHoveringTag) && !inputVisible && (
          <Tooltip title={tags.length >= maxTags ? '当前标签已满，请先删除标签再添加新标签' : '添加标签'}>
            <Tag
              className="site-tag-plus"
              onClick={showAddTagsPanel}
              style={{
                background: '#D8F2EF',
                borderStyle: 'dashed',
                color:'#00000066',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              <PlusOutlined/>
            </Tag>
          </Tooltip>
        )}
        {/* 可选标签面板 */}
        {showAvailableTags && (
          <div
            className="available-tags-panel"
            style={{ borderTop: '1px solid #F1F1F1', paddingTop: '8px', display: 'flex' }}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              // alignItems: 'center',
              marginBottom: '8px'
            }}>
              <span style={{ color: '#0FB698', flexShrink: 0 }}>可选标签：</span>
              {/* <span
                style={{ cursor: 'pointer', color: '#999' }}
                onClick={hideAddTagsPanel}
              >
                ✕
              </span> */}
            </div>

            {/* 可选标签列表 */}
            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              // gap: '4px',
              flex: 1
            }}>
              {getUnusedTags().map((tag) => (
                <Tag
                  className="select-tag"
                  color="#D8F2EF"
                  key={tag}
                  onClick={() => handleTagSelect(tag)}
                >
                  {tag}
                </Tag>
              ))}
              <Tag
                className="select-tag"
                color="#D8F2EF"
                onClick={() => setCustomInputVisible(true)}
              >
                自定义标签
              </Tag>
            </div>

            {/* 自定义标签输入 */}
            {/* <div style={{ borderTop: '1px solid #f0f0f0', paddingTop: '8px' }}>

              <Tag
                style={{
                  background: '#D8F2EF',
                  borderStyle: 'dashed',
                  cursor: 'pointer',
                  color: "#0FB698"
                }}
                onClick={() => setCustomInputVisible(true)}
              >
                <PlusOutlined /> 自定义标签
              </Tag>
            </div> */}
          </div>
        )}
      </div>
    </div>
  );
};



export default EditableTagGroup;
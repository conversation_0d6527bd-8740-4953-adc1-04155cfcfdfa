import { Tag, Input, InputRef, Tooltip, message } from 'antd';
import { PlusOutlined, EyeOutlined, EyeInvisibleOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';
import { useState, useRef, useEffect } from 'react';
import './EditableTagGroup.css';

// 全局状态管理，用于保持每个QA项的可选标签面板状态
const globalPanelStates = new Map<string, boolean>();

type EditableTagGroupProps = {
  tags: string[]; // 所有标签列表，前3个为当前标签，剩余为可选标签
  onTagsChange: (tags: string[]) => void; // 标签变更回调，传回所有标签
  maxTags?: number; // 最大当前标签数量，默认3
  qaId?: string; // QA项的唯一标识，用于保持状态
};

const EditableTagGroup: React.FC<EditableTagGroupProps> = ({
  tags,
  onTagsChange,
  maxTags = 3,
  qaId
}) => {
  const [inputVisible, setInputVisible] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('');
  const [editInputIndex, setEditInputIndex] = useState<number>(-1);
  const [editInputValue, setEditInputValue] = useState<string>('');
  const [customInputVisible, setCustomInputVisible] = useState<boolean>(false);
  const [customInputValue, setCustomInputValue] = useState<string>('');
  const inputRef = useRef<InputRef>(null);
  const editInputRef = useRef<InputRef>(null);
  const customInputRef = useRef<InputRef>(null);
  const [hoveredTagIndex, setHoveredTagIndex] = useState<number | null>(null);
  const [isHoveringTag, setIsHoveringTag] = useState<boolean>(false);
  const [expanded, setExpanded] = useState(false);

  // 动画状态管理
  const [animatingTags, setAnimatingTags] = useState<Set<string>>(new Set());
  const [tagAnimations, setTagAnimations] = useState<Map<string, string>>(new Map());
  // 使用全局状态管理可选标签面板的显示状态
  const panelKey = qaId || 'default';
  const [showAvailableTags, setShowAvailableTagsState] = useState<boolean>(
    globalPanelStates.get(panelKey) || false
  );

  // 更新全局状态
  const setShowAvailableTags = (show: boolean) => {
    setShowAvailableTagsState(show);
    globalPanelStates.set(panelKey, show);
  };

  // 使用 ref 来避免闭包问题
  const showAvailableTagsRef = useRef<boolean>(showAvailableTags);
  showAvailableTagsRef.current = showAvailableTags;

  // 将传入的标签分为当前标签和可选标签
  const currentTags = tags.slice(0, maxTags); // 前3个为当前标签
  const availableTags = tags.slice(maxTags); // 剩余为可选标签

  // 按字母顺序排序可选标签
  const sortedAvailableTags = [...availableTags].sort((a, b) => a.localeCompare(b, 'zh-CN'));

  // 重置编辑状态当tags变化时
  useEffect(() => {
    setEditInputIndex(-1);
  }, [tags]);

  useEffect(() => {
    if (inputVisible && inputRef.current) {
      inputRef.current.focus();
    }
  }, [inputVisible]);

  useEffect(() => {
    if (editInputIndex !== -1 && editInputRef.current) {
      editInputRef.current.focus();
    }
  }, [editInputIndex]);

  useEffect(() => {
    if (customInputVisible && customInputRef.current) {
      customInputRef.current.focus();
    }
  }, [customInputVisible]);

  // 按字母顺序排序当前标签
  const sortedTags = [...currentTags].sort((a, b) => a.localeCompare(b, 'zh-CN'));

  // 动画触发函数
  const triggerTagAnimation = (tagName: string, animationType: string) => {
    setAnimatingTags(prev => new Set(prev).add(tagName));
    setTagAnimations(prev => new Map(prev).set(tagName, animationType));

    // 动画结束后清除状态
    setTimeout(() => {
      setAnimatingTags(prev => {
        const newSet = new Set(prev);
        newSet.delete(tagName);
        return newSet;
      });
      setTagAnimations(prev => {
        const newMap = new Map(prev);
        newMap.delete(tagName);
        return newMap;
      });
    }, 600); // 动画持续时间
  };

  const handleClose = (removedTag: string) => {
    // 如果可选标签面板正在显示，触发向下移动动画
    if (showAvailableTags) {
      triggerTagAnimation(removedTag, 'tag-move-down');
    }

    // 延迟执行状态更新，让动画先播放
    setTimeout(() => {
      // 从当前标签中移除，如果面板显示则添加到可选标签末尾
      const newCurrentTags = currentTags.filter(tag => tag !== removedTag);
      let newAllTags: string[];

      if (showAvailableTags) {
        // 如果面板显示，将删除的标签添加到可选标签列表
        newAllTags = [...newCurrentTags, ...availableTags, removedTag];
      } else {
        // 如果面板不显示，直接从所有标签中移除
        newAllTags = tags.filter(tag => tag !== removedTag);
      }

      onTagsChange(newAllTags);
    }, showAvailableTags ? 300 : 0); // 如果面板显示则延迟，否则立即执行
  };

  const handleTagSelect = (selectedTag: string) => {
    if (!currentTags.includes(selectedTag) && currentTags.length < maxTags) {
      // 触发向上移动动画
      triggerTagAnimation(selectedTag, 'tag-move-up');

      // 延迟执行状态更新，让动画先播放
      setTimeout(() => {
        // 将选中的标签从可选标签移动到当前标签
        const newCurrentTags = [...currentTags, selectedTag];
        const newAvailableTags = availableTags.filter(tag => tag !== selectedTag);
        const newAllTags = [...newCurrentTags, ...newAvailableTags];

        onTagsChange(newAllTags);
      }, 300); // 延迟300ms执行状态更新
    } else if (currentTags.includes(selectedTag)) {
      message.warning('已选择该标签');
    } else if (currentTags.length >= maxTags) {
      message.warning('当前已存在3个标签，请先删除标签再添加新标签');
    }
  };

  const handleCustomInputConfirm = () => {
    if (customInputValue && !currentTags.includes(customInputValue) && currentTags.length < maxTags) {
      // 将自定义标签添加到当前标签
      const newCurrentTags = [...currentTags, customInputValue];
      // 如果自定义标签在可选标签中，将其移除
      const newAvailableTags = availableTags.filter(tag => tag !== customInputValue);
      const newAllTags = [...newCurrentTags, ...newAvailableTags];

      onTagsChange(newAllTags);
    }
    setCustomInputVisible(false);
    setCustomInputValue('');
  };

  const showInput = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setInputVisible(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputConfirm = () => {
    if (inputValue && !tags.includes(inputValue)) {
      onTagsChange([...tags, inputValue]);
    }
    setInputVisible(false);
    setInputValue('');
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value);
  };

  const handleEditInputConfirm = () => {
    if (editInputIndex === -1) return;

    const newTags = [...tags];
    newTags[editInputIndex] = editInputValue;
    onTagsChange(newTags);
    setEditInputIndex(-1);
  };

  const showAddTagsPanel = () => {
    if (tags.length >= maxTags) {
      message.warning('当前标签已满，请先删除标签再添加新标签');
      return;
    }
    setShowAvailableTags(true);
  };

  const hideAddTagsPanel = () => {
    setShowAvailableTags(false);
    setCustomInputVisible(false);
    setCustomInputValue('');
  };

  // 获取可选标签（已排序）
  const getUnusedTags = () => {
    return sortedAvailableTags;
  };
  // 添加全局点击事件监听器，使用 ref 避免闭包问题
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      e.stopPropagation();
      e.preventDefault();
      const target = e.target as Element;
      const panel = document.querySelector('.available-tags-panel');
      const addButton = document.querySelector('.site-tag-plus');

      // 更精确地检查是否点击了标签的关闭按钮
      const isTagCloseButton =
        target.closest('.ant-tag-close-icon') ||
        target.closest('.anticon-close') ||
        target.closest('span[role="img"][aria-label="close"]') ||
        target.classList.contains('ant-tag-close-icon') ||
        target.classList.contains('anticon-close') ||
        // 检查是否是SVG图标或其子元素
        target.closest('svg') && target.closest('.ant-tag') ||
        // 检查是否在可关闭的标签内
        (target.closest('.ant-tag') && target.closest('.ant-tag')?.querySelector('.ant-tag-close-icon'));

      // 如果点击的是标签关闭按钮，不关闭面板
      if (isTagCloseButton) {
        return;
      }

      // 使用 ref 来获取当前的 showAvailableTags 状态，避免闭包问题
      const currentShowAvailableTags = showAvailableTagsRef.current;

      // 检查点击是否在面板外部和添加按钮外部
      if (
        currentShowAvailableTags &&
        panel &&
        !panel.contains(e.target as Node) &&
        addButton &&
        !addButton.contains(e.target as Node)
      ) {
        setShowAvailableTags(false);
        setCustomInputVisible(false);
        setCustomInputValue('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div style={{ position: 'relative' }}>
      {/* 标签显示区域 */}
      <div
        className="qa-tags"
        onClick={(e) => e.stopPropagation()}
        onMouseEnter={() => setIsHoveringTag(true)}
        onMouseLeave={() => setIsHoveringTag(false)}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          flexWrap: 'wrap',
          minHeight: '32px',
          padding: '4px 0',
          transition: 'all 0.3s ease'
        }}
      >
        {/* 显示当前标签 */}
        {sortedTags.map((tag, index) => {
          // if (editInputIndex === tags.indexOf(tag)) {
          //   return (
          //     <Input
          //       ref={editInputRef}
          //       key={`edit-${index}`}
          //       size="small"
          //       className="tag-input"
          //       value={editInputValue}
          //       onChange={handleEditInputChange}
          //       onBlur={handleEditInputConfirm}
          //       onPressEnter={handleEditInputConfirm}
          //       onClick={(e) => e.stopPropagation()}
          //       style={{ width: '80px' }}
          //     />
          //   );
          // }

          const animationClass = tagAnimations.get(tag) || '';
          const isAnimating = animatingTags.has(tag);

          return (
            <Tag
              className={`edit-tag ${animationClass} ${isAnimating ? 'tag-moving' : ''}`}
              key={`tag-${index}`}
              closable={hoveredTagIndex === tags.indexOf(tag)}
              color="#D8F2EF"
              onClose={(e) => {
                e.preventDefault();
                handleClose(tag);
              }}
              onClick={(e) => {
                e.stopPropagation();
                setEditInputIndex(tags.indexOf(tag));
                setEditInputValue(tag);
              }}
              onMouseEnter={() => setHoveredTagIndex(tags.indexOf(tag))}
              onMouseLeave={() => setHoveredTagIndex(null)}
            >
              {tag}
            </Tag>
          );
        })}
        {customInputVisible && (
          <Input
            ref={customInputRef}
            size="small"
            placeholder="输入自定义标签"
            value={customInputValue}
            onChange={(e) => setCustomInputValue(e.target.value)}
            onBlur={handleCustomInputConfirm}
            onPressEnter={handleCustomInputConfirm}
            style={{ width: '120px' }}
          />
        )}

        {/* 添加按钮 */}
        {(sortedTags.length === 0 || isHoveringTag) && !inputVisible && (
          <Tooltip title={tags.length >= maxTags ? '当前标签已满，请先删除标签再添加新标签' : '添加标签'}>
            <Tag
              className="site-tag-plus"
              onClick={showAddTagsPanel}
              style={{
                background: '#D8F2EF',
                borderStyle: 'dashed',
                color: '#00000066',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              <PlusOutlined />
            </Tag>
          </Tooltip>
        )}
        {/* 可选标签面板 */}
        {showAvailableTags && (
          <div
            className="available-tags-panel"
            style={{ borderTop: '1px solid #F1F1F1', paddingTop: '8px', display: 'flex', alignItems: expanded ? 'flex-start' : 'center', }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginBottom: '8px',
            }}> */}
              <span style={{ color: '#0FB698', flexShrink: 0, marginTop: '4px' }}>可选标签：</span>
              {/* <span
                style={{ cursor: 'pointer', color: '#999' }}
                onClick={hideAddTagsPanel}
              >
                ✕
              </span> */}
            {/* </div> */}

            {/* 可选标签列表 */}
            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              maxHeight: expanded ? 'none' : '25px',
              overflow: 'hidden',
              flex: 1
            }}>
              {getUnusedTags().map((tag) => {
                const animationClass = tagAnimations.get(tag) || '';
                const isAnimating = animatingTags.has(tag);

                return (
                  <Tag
                    className={`select-tag ${animationClass} ${isAnimating ? 'tag-moving' : ''}`}
                    color="#D8F2EF"
                    key={tag}
                    onClick={() => handleTagSelect(tag)}
                  >
                    {tag}
                  </Tag>
                );
              })}
              <Tag
                className="select-tag"
                color="#D8F2EF"
                onClick={() => setCustomInputVisible(true)}
              >
                自定义标签
              </Tag>
            </div>
            <span
              style={{ cursor: 'pointer', color: '#999', marginLeft: '8px' }}
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? <UpOutlined /> : <DownOutlined />}
            </span>

            {/* 自定义标签输入 */}
            {/* <div style={{ borderTop: '1px solid #f0f0f0', paddingTop: '8px' }}>

              <Tag
                style={{
                  background: '#D8F2EF',
                  borderStyle: 'dashed',
                  cursor: 'pointer',
                  color: "#0FB698"
                }}
                onClick={() => setCustomInputVisible(true)}
              >
                <PlusOutlined /> 自定义标签
              </Tag>
            </div> */}
          </div>
        )}
      </div>
    </div>
  );
};



export default EditableTagGroup;
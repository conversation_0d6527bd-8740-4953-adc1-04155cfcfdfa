import { Tag, Input, InputRef, Tooltip, message } from 'antd';
import { PlusOutlined, EyeOutlined, EyeInvisibleOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';
import { useState, useRef, useEffect } from 'react';
import './EditableTagGroup.css';

// 全局状态管理，用于保持每个QA项的可选标签面板状态
const globalPanelStates = new Map<string, boolean>();

// 全局状态管理每个QA的标签状态
const globalQAStates = new Map<string, {
  currentTags: string[];
  availableTags: string[];
}>();

type EditableTagGroupProps = {
  tags: string[]; // 初始当前标签列表（前3个）
  availableTags?: string[]; // 初始可选标签列表（剩余部分）
  onTagsChange: (currentTags: string[], availableTags: string[]) => void; // 分别返回当前标签和可选标签
  maxTags?: number; // 最大当前标签数量，默认3
  qaId?: string; // QA项的唯一标识，用于保持状态
};

const EditableTagGroup: React.FC<EditableTagGroupProps> = ({
  tags,
  availableTags = [],
  onTagsChange,
  maxTags = 3,
  qaId
}) => {
  const [customInputVisible, setCustomInputVisible] = useState<boolean>(false);
  const [customInputValue, setCustomInputValue] = useState<string>('');
  const customInputRef = useRef<InputRef>(null);
  const [hoveredTagIndex, setHoveredTagIndex] = useState<number | null>(null);
  const [isHoveringTag, setIsHoveringTag] = useState<boolean>(false);
  const [expanded, setExpanded] = useState(false);

  // 动画状态管理
  const [animatingTags, setAnimatingTags] = useState<Set<string>>(new Set());
  const [tagAnimations, setTagAnimations] = useState<Map<string, string>>(new Map());

  // 使用全局Map来存储每个QA的标签状态，避免重新渲染时丢失状态
  const qaStateKey = qaId || 'default';

  // 获取或初始化该QA的状态
  const getQAState = () => {
    if (!globalQAStates.has(qaStateKey)) {
      globalQAStates.set(qaStateKey, {
        currentTags: [...tags],
        availableTags: [...availableTags]
      });
    }
    return globalQAStates.get(qaStateKey)!;
  };

  const [qaState, setQAState] = useState(() => getQAState());

  // 当前标签和可选标签从全局状态中获取
  const currentTags = qaState.currentTags;
  const internalAvailableTags = qaState.availableTags;
  // 使用全局状态管理可选标签面板的显示状态
  const panelKey = qaId || 'default';
  const [showAvailableTags, setShowAvailableTagsState] = useState<boolean>(
    globalPanelStates.get(panelKey) || false
  );

  // 添加容器引用，用于检测外部点击
  const containerRef = useRef<HTMLDivElement>(null);

  // 更新全局状态
  const setShowAvailableTags = (show: boolean) => {
    setShowAvailableTagsState(show);
    globalPanelStates.set(panelKey, show);
  };

  // 使用 ref 来避免闭包问题
  const showAvailableTagsRef = useRef<boolean>(showAvailableTags);
  showAvailableTagsRef.current = showAvailableTags;


  // 通知父组件标签变化，分别返回当前标签和可选标签
  const notifyParent = (newCurrentTags: string[], newAvailableTags: string[]) => {
    onTagsChange(newCurrentTags, newAvailableTags);
  };

  useEffect(() => {
    if (customInputVisible && customInputRef.current) {
      customInputRef.current.focus();
    }
  }, [customInputVisible]);

  // 更新QA状态的函数
  const updateQAState = (newCurrentTags: string[], newAvailableTags: string[]) => {
    const newState = {
      currentTags: newCurrentTags,
      availableTags: newAvailableTags
    };
    globalQAStates.set(qaStateKey, newState);
    setQAState(newState);
  };



  // 按字母顺序排序当前标签
  const sortedTags = [...currentTags].sort((a, b) => a.localeCompare(b, 'zh-CN'));

  // 动画触发函数
  const triggerTagAnimation = (tagName: string, animationType: string) => {
    setAnimatingTags(prev => new Set(prev).add(tagName));
    setTagAnimations(prev => new Map(prev).set(tagName, animationType));

    // 动画结束后清除状态
    setTimeout(() => {
      setAnimatingTags(prev => {
        const newSet = new Set(prev);
        newSet.delete(tagName);
        return newSet;
      });
      setTagAnimations(prev => {
        const newMap = new Map(prev);
        newMap.delete(tagName);
        return newMap;
      });
    }, 600); // 动画持续时间
  };

  const handleClose = (removedTag: string) => {
    // 如果可选标签面板正在显示，触发向下移动动画
    if (showAvailableTags) {
      triggerTagAnimation(removedTag, 'tag-move-down');
    }

    // 延迟执行状态更新，让动画先播放
    setTimeout(() => {
      // 从当前标签中移除
      const newCurrentTags = currentTags.filter(tag => tag !== removedTag);

      // 如果可选标签面板正在显示，将删除的标签加入到可选标签列表中
      let newAvailableTags = internalAvailableTags;
      if (showAvailableTags && !internalAvailableTags.includes(removedTag)) {
        newAvailableTags = [...internalAvailableTags, removedTag].sort((a, b) => a.localeCompare(b, 'zh-CN'));
      }

      // 更新QA状态
      updateQAState(newCurrentTags, newAvailableTags);

      // 通知父组件
      notifyParent(newCurrentTags, newAvailableTags);
    }, showAvailableTags ? 300 : 0); // 如果面板显示则延迟，否则立即执行
  };

  const handleTagSelect = (selectedTag: string) => {
    if (!currentTags.includes(selectedTag) && currentTags.length < maxTags) {
      // 触发向上移动动画
      triggerTagAnimation(selectedTag, 'tag-move-up');

      // 延迟执行状态更新，让动画先播放
      setTimeout(() => {
        // 添加到当前标签
        const newCurrentTags = [...currentTags, selectedTag];

        // 从可选标签列表中移除已选择的标签
        const newAvailableTags = internalAvailableTags.filter(tag => tag !== selectedTag);

        // 更新QA状态
        updateQAState(newCurrentTags, newAvailableTags);

        // 通知父组件
        notifyParent(newCurrentTags, newAvailableTags);
      }, 300); // 延迟300ms执行状态更新
    } else if (currentTags.includes(selectedTag)) {
      message.warning('已选择该标签');
    } else if (currentTags.length >= maxTags) {
      message.warning('当前已存在3个标签，请先删除标签再添加新标签');
    }
  };

  const handleCustomInputConfirm = () => {
    if (customInputValue && !currentTags.includes(customInputValue) && currentTags.length < maxTags) {
      // 添加到当前标签
      const newCurrentTags = [...currentTags, customInputValue];

      // 如果自定义标签在可选标签列表中，将其移除
      let newAvailableTags = internalAvailableTags;
      if (internalAvailableTags.includes(customInputValue)) {
        newAvailableTags = internalAvailableTags.filter(tag => tag !== customInputValue);
      }

      // 更新QA状态
      updateQAState(newCurrentTags, newAvailableTags);

      // 通知父组件
      notifyParent(newCurrentTags, newAvailableTags);
    }
    setCustomInputVisible(false);
    setCustomInputValue('');
  };



  const showAddTagsPanel = () => {
    if (currentTags.length >= maxTags) {
      message.warning('当前标签已满，请先删除标签再添加新标签');
      return;
    }
    setShowAvailableTags(true);
  };

  const hideAddTagsPanel = () => {
    setShowAvailableTags(false);
    setCustomInputVisible(false);
    setCustomInputValue('');
  };

  // 获取未使用的可选标签
  const getUnusedTags = () => {
    return internalAvailableTags
      .filter(tag => !currentTags.includes(tag))
      .sort((a, b) => a.localeCompare(b, 'zh-CN'));
  };
  const handleInputChange = (e: { target: { value: any; }; }) => {
    const value = e.target.value;
    const regex = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/;
    if (regex.test(value)) {
      setCustomInputValue(value);
    } else {
      // 过滤非法字符并提示
      const validValue = value.replace(/[^\w\u4e00-\u9fa5]/g, '');
      // setCustomInputValue(validValue);
      message.error('仅支持中英文和数字输入且长度不能超过12个字符');
      // setInputError('仅支持中英文和数字输入');
    }
  };

  // 添加全局点击事件监听器
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      // 只在可选标签面板显示时处理外部点击
      if (!showAvailableTags) {
        return;
      }

      const target = e.target as Element;

      // 检查点击是否在组件容器内
      if (containerRef.current && containerRef.current.contains(target)) {
        return;
      }

      // 点击在组件外部，关闭可选标签面板
      setShowAvailableTags(false);
      setCustomInputVisible(false);
      setCustomInputValue('');
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showAvailableTags]);

  return (
    <div ref={containerRef} style={{ position: 'relative', width: "100%"}}>
      {/* 标签显示区域 */}
      <div
        className="qa-tags"
        onClick={(e) => { e.stopPropagation(); e.stopPropagation() }}
        onMouseEnter={() => setIsHoveringTag(true)}
        onMouseLeave={() => setIsHoveringTag(false)}
      >
        <div className='tag-content'>


          {/* 显示当前标签 */}
          {sortedTags.map((tag, index) => {
            // if (editInputIndex === tags.indexOf(tag)) {
            //   return (
            //     <Input
            //       ref={editInputRef}
            //       key={`edit-${index}`}
            //       size="small"
            //       className="tag-input"
            //       value={editInputValue}
            //       onChange={handleEditInputChange}
            //       onBlur={handleEditInputConfirm}
            //       onPressEnter={handleEditInputConfirm}
            //       onClick={(e) => e.stopPropagation()}
            //       style={{ width: '80px' }}
            //     />
            //   );
            // }

            const animationClass = tagAnimations.get(tag) || '';
            const isAnimating = animatingTags.has(tag);

            return (
              <div className='tag-group'>
                <Tag
                  className={`edit-tag ${animationClass} ${isAnimating ? 'tag-moving' : ''}`}
                  key={`tag-${index}`}
                  // closable={hoveredTagIndex === index}
                    closable
                  color="#D8F2EF"
                  onClose={(e) => {
                    e.preventDefault();
                    handleClose(tag);
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onMouseEnter={() => setHoveredTagIndex(index)}
                  onMouseLeave={() => setHoveredTagIndex(null)}
                >
                  {tag}
                </Tag>

              </div>
            );
          })}
          {customInputVisible && (
            <Input
              ref={customInputRef}
              size="small"
              placeholder="输入自定义标签"
              value={customInputValue}
              onChange={(e) => setCustomInputValue(e.target.value)}
              //  onChange={handleInputChange}
              maxLength={10}
              onBlur={handleCustomInputConfirm}
              onPressEnter={handleCustomInputConfirm}
              style={{ width: '120px', marginRight: '8px' }}
            />
          )}

          {/* 添加按钮 */}
          {(sortedTags.length === 0 || isHoveringTag) && (
            <Tooltip title={sortedTags.length >= maxTags ? '当前标签已满，请先删除标签再添加新标签' : '添加标签'}>
              <Tag
                className="site-tag-plus"
                onClick={showAddTagsPanel}
                style={{
                  background: '#D8F2EF',
                  borderStyle: 'dashed',
                  color: '#00000066',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
              >
                <PlusOutlined />
              </Tag>
            </Tooltip>
          )}
        </div>
        {/* 可选标签面板 */}
        {showAvailableTags && (
          <div
            className="available-tags-panel"
            style={{ borderTop: '1px solid #F1F1F1', paddingTop: '8px', display: 'flex', alignItems: expanded ? 'flex-start' : 'center', }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginBottom: '8px',
            }}> */}
            <span style={{ color: '#0FB698', flexShrink: 0 }}>可选标签：</span>
            {/* <span
                style={{ cursor: 'pointer', color: '#999' }}
                onClick={hideAddTagsPanel}
              >
                ✕
              </span> */}
            {/* </div> */}

            {/* 可选标签列表 */}
            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              maxHeight: expanded ? 'none' : '25px',
              overflow: 'hidden',
              flex: 1,
              rowGap: '8px',
            }}>
              {getUnusedTags().map((tag) => {
                const animationClass = tagAnimations.get(tag) || '';
                const isAnimating = animatingTags.has(tag);

                return (
                  <Tag
                    className={`select-tag ${animationClass} ${isAnimating ? 'tag-moving' : ''}`}
                    color="#D8F2EF"
                    key={tag}
                    onClick={() => handleTagSelect(tag)}
                  >
                    {tag}
                  </Tag>
                );
              })}
              <Tag
                className="select-tag"
                color="#D8F2EF"
                onClick={() => setCustomInputVisible(true)}
              >
                自定义标签
              </Tag>
            </div>
            <span
              style={{ cursor: 'pointer', color: '#999', marginLeft: '8px' }}
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? <UpOutlined /> : <DownOutlined />}
            </span>

            {/* 自定义标签输入 */}
            {/* <div style={{ borderTop: '1px solid #f0f0f0', paddingTop: '8px' }}>

              <Tag
                style={{
                  background: '#D8F2EF',
                  borderStyle: 'dashed',
                  cursor: 'pointer',
                  color: "#0FB698"
                }}
                onClick={() => setCustomInputVisible(true)}
              >
                <PlusOutlined /> 自定义标签
              </Tag>
            </div> */}
          </div>
        )}
      </div>
    </div>
  );
};



export default EditableTagGroup;
import { Tag, Input, InputRef } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useState, useRef, useEffect } from 'react';

type EditableTagGroupProps = {
  tags: string[];
  onTagsChange: (tags: string[]) => void;
};

const EditableTagGroup: React.FC<EditableTagGroupProps> = ({ tags, onTagsChange }) => {
  const [inputVisible, setInputVisible] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('');
  const [editInputIndex, setEditInputIndex] = useState<number>(-1);
  const [editInputValue, setEditInputValue] = useState<string>('');
  const inputRef = useRef<InputRef>(null);
  const editInputRef = useRef<InputRef>(null);
  const [hoveredTagIndex, setHoveredTagIndex] = useState<number | null>(null);
  const [isHoveringTag, setIsHoveringTag] = useState<boolean>(false);

  // 重置编辑状态当tags变化时
  useEffect(() => {
    setEditInputIndex(-1);
  }, [tags]);

  useEffect(() => {
    if (inputVisible && inputRef.current) {
      inputRef.current.focus();
    }
  }, [inputVisible]);

  useEffect(() => {
    if (editInputIndex !== -1 && editInputRef.current) {
      editInputRef.current.focus();
    }
  }, [editInputIndex]);

  const handleClose = (removedTag: string) => {
    const newTags = tags.filter(tag => tag !== removedTag);
    onTagsChange(newTags);
  };

  const showInput = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setInputVisible(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputConfirm = () => {
    if (inputValue && !tags.includes(inputValue)) {
      onTagsChange([...tags, inputValue]);
    }
    setInputVisible(false);
    setInputValue('');
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value);
  };

  const handleEditInputConfirm = () => {
    if (editInputIndex === -1) return;

    const newTags = [...tags];
    newTags[editInputIndex] = editInputValue;
    onTagsChange(newTags);
    setEditInputIndex(-1);
  };

  return (
    <div className="qa-tag" onClick={(e) => e.stopPropagation()} onMouseEnter={() => setIsHoveringTag(true)}
      onMouseLeave={() => setIsHoveringTag(false)} >
      {tags.map((tag, index) => {
        if (editInputIndex === index) {
          return (
            <Input
              ref={editInputRef}
              key={`edit-${index}`}
              size="small"
              className="tag-input"
              value={editInputValue}
              onChange={handleEditInputChange}
              onBlur={handleEditInputConfirm}
              onPressEnter={handleEditInputConfirm}
              onClick={(e) => e.stopPropagation()}
            />
          );
        }

        return (
          <Tag
            className="edit-tag"
            key={`tag-${index}`}
            closable={hoveredTagIndex === index} // 只有当前 hover 的 Tag 才显示关闭按钮
            color="#D8F2EF"
            style={{ color: '#0FB698' }}
            onClose={(e) => {
              e.preventDefault();
              handleClose(tag);
            }}
            onClick={(e) => e.stopPropagation()}
            onMouseEnter={() => setHoveredTagIndex(index)} // 鼠标进入时记录索引
            onMouseLeave={() => setHoveredTagIndex(null)} // 鼠标离开时清空索引
          >
            <span
              onDoubleClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setEditInputIndex(index);
                setEditInputValue(tag);
              }}
            >
              {tag}
            </span>
          </Tag>
        );
      })}
      {inputVisible && (
        <Input
          ref={inputRef}
          type="text"
          size="small"
          className="tag-input"
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputConfirm}
          onPressEnter={handleInputConfirm}
          onClick={(e) => e.stopPropagation()}
        />
      )}
      {isHoveringTag&&!inputVisible && (
        <Tag
          className="site-tag-plus"
          onClick={showInput}
          onMouseEnter={() => setHoveredTagIndex(-1)}
        >
          <PlusOutlined />
        </Tag>
      )}
    </div>
  );
};



export default EditableTagGroup;
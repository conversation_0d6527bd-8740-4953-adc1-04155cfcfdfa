import{r as e,k as t,j as i,f as s,B as r,l as o,t as n}from"./react-core-BrQk45h1.js";/* empty css                        */import{q as a}from"./server-YuXc7UOr.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const l=()=>{const[l,p]=e.useState([]),d=t();return e.useEffect((()=>{(async()=>{try{const e=await a();p(e)}catch(e){n.error("获取服务器信息失败")}})()}),[]),i.jsxs("div",{className:"uploadBaseModel",children:[i.jsxs(s,{size:20,style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[i.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[i.jsx(r,{style:{fontSize:"12px",width:"36px",height:"36px"},shape:"circle",icon:i.jsx(o,{}),onClick:()=>d(-1)}),i.jsx("div",{className:"mediumText",style:{fontSize:"20px",lineHeight:"36px",fontWeight:"500"},children:"服务器管理"})]}),i.jsx("div",{children:i.jsx(r,{type:"primary",size:"large",shape:"round",style:{backgroundColor:"black",fontSize:"14px",fontWeight:"700"},className:"boldText",onClick:()=>{d("/main/finetune/serverManagement/newServer")},children:"添加服务器"})})]}),i.jsx("div",{className:"uploadModelArea",children:i.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"20px",marginTop:"20px"},children:l.length>0?l.map((e=>{var t,s;return i.jsxs("div",{style:{width:"calc(25% - 20px)",height:"185px",backgroundImage:"url(/assets/server-bg-Dc4VWc0V.svg)",backgroundSize:"cover",backgroundPosition:"center",position:"relative",borderRadius:"8px",overflow:"hidden",boxShadow:"0 4px 8px rgba(0, 0, 0, 0.1)"},children:[i.jsx("div",{style:{position:"absolute",top:"80px",left:"10px",color:"#fff",fontSize:"12px",fontWeight:"400",textShadow:"1px 1px 2px rgba(0, 0, 0, 0.5)"},children:(null==(t=e.description)?void 0:t.toString().length)>20?`${null==(s=e.description)?void 0:s.toString().substring(0,20)}...`:e.description}),i.jsx("div",{style:{position:"absolute",top:"10px",left:"10px",color:"#fff",fontSize:"16px",fontWeight:"bold",textShadow:"1px 1px 2px rgba(0, 0, 0, 0.5)"},children:e.serverName}),i.jsx("div",{style:{position:"absolute",bottom:"10px",right:"10px"},children:i.jsx("div",{style:{padding:"5px 10px",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"12px",color:"#fff"},onClick:()=>d(`/main/finetune/serverManagement/updateServer/${e.id}`),children:"配置"})})]},e.id)})):i.jsx("p",{children:"暂无服务器信息"})})})]})};export{l as default};

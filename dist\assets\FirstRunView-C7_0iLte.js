import{r as e,j as s,k as a,S as l,B as i,a3 as r}from"./react-core-BrQk45h1.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";const c=({images:a,scrollSpeed:l})=>{const i=e.useRef(null),[r,c]=e.useState(!0);return e.useEffect((()=>{const e=i.current,s=()=>{c(!1)},a=()=>{c(!0)};return e&&(e.addEventListener("mouseenter",s),e.addEventListener("mouseleave",a)),()=>{e&&(e.removeEventListener("mouseenter",s),e.removeEventListener("mouseleave",a))}}),[]),e.useEffect((()=>{let e;return r&&(e=setInterval((()=>{var e,s;if(i.current&&(i.current.scrollLeft+=1,i.current.scrollLeft>=i.current.scrollWidth/2)){const a=null==(e=i.current.parentNode)?void 0:e.firstChild;a&&(null==(s=i.current.parentNode)||s.appendChild(a),i.current.scrollLeft=0)}}),l)),()=>clearInterval(e)}),[r,l]),s.jsxs("div",{ref:i,style:{overflow:"hidden",whiteSpace:"nowrap",height:"440px"},children:[a.map(((e,a)=>s.jsx("img",{src:e,alt:`Image ${a}`,style:{display:"inline-block",width:"auto",height:"100%",marginRight:"16px"}},a))),a.map(((e,a)=>s.jsx("img",{src:e,alt:`Image ${a}`,style:{display:"inline-block",width:"auto",height:"100%",marginRight:"16px"}},a+6)))]})},t=()=>{const t=a(),[n,d]=e.useState(0),m=["/assets/banner-1--GQ6Lvu7.png","/assets/banner-2-DTntCmlA.png","/assets/banner-3--eLMJ5HA.png","/assets/banner-4-CTknI9SJ.png","/assets/banner-5-DKh2_s0s.png","/assets/banner-6-BNpbgniN.png"],h=e=>{d(e)};e.useEffect((()=>{const e=setInterval((()=>{d((e=>(e+1)%4))}),5e3);return()=>clearInterval(e)}),[]),e.useCallback((()=>{t("/")}),[t]),e.useCallback((()=>{t("/1")}),[t]);return s.jsxs(l,{className:"div648",children:[s.jsx("div",{className:"scroll-snap-item",style:{maxHeight:"calc(100vh - 4rem)",overflow:"hidden",minHeight:"849px"},children:s.jsxs("div",{className:"card-area group-parent",children:[s.jsx("div",{className:"div649 lightText",children:"企业级大模型训练数据生成工具"}),s.jsx("div",{className:"div650 boldText",children:"#行至智能"}),s.jsxs("div",{className:"logo-parent1",children:[s.jsx("img",{style:{width:"42px",height:"55.125px"},src:"/logo.png"}),s.jsx("b",{className:"one-click-upload boldText",children:"Pollux"})]})]})}),s.jsx("div",{className:"scroll-snap-item",style:{width:"100vw"},children:s.jsxs("div",{className:"card-area child81",children:[s.jsx(i,{className:"vector-parent2 div654 frame-child296 primary-btn boldText",type:"primary",style:{color:"white"},onClick:()=>{t("/main/sourcedata")},children:s.jsxs("div",{className:"upload-btn",children:["上传本地数据集",s.jsx("img",{className:"frame-child297",alt:"",src:"data:image/svg+xml,%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M2%202H14V14'%20stroke='white'%20stroke-width='3'%20stroke-linejoin='round'/%3e%3cpath%20d='M14%202L2%2014'%20stroke='white'%20stroke-width='3'/%3e%3c/svg%3e"})]})}),s.jsxs("div",{className:"frame-parent11",children:[s.jsxs("div",{className:"rectangle-parent58",children:[s.jsx("div",{className:"frame-child298"}),s.jsx("div",{className:"frame-child299"}),s.jsx("div",{className:"frame-child300"})]}),s.jsx("b",{className:"one-click-upload boldText",children:"One-click upload"})]}),s.jsxs("div",{className:"div652 lightText",children:[s.jsx("p",{className:"p",children:"支持多源异构数据集"}),s.jsx("p",{className:"p",children:"一键上传解析"})]})]})}),s.jsx("div",{className:"scroll-snap-item",style:{width:"100vw"},children:s.jsxs("div",{className:"card-area child87",children:[s.jsxs("div",{className:"frame-parent11",children:[s.jsxs("div",{className:"rectangle-parent58",children:[s.jsx("div",{className:"frame-child298"}),s.jsx("div",{className:"frame-child299"}),s.jsx("div",{className:"frame-child300"})]}),s.jsx("b",{className:"one-click-upload boldText",style:{color:"white"},children:"Purpose-built"})]}),s.jsxs("div",{className:"div653 lightText",children:[s.jsx("p",{className:"p",children:"专用问题推理"}),s.jsx("p",{className:"p",children:"大模型基座"})]}),s.jsx("div",{className:"inner3",children:s.jsx(c,{images:m,scrollSpeed:30})})]})}),s.jsx("div",{className:"scroll-snap-item",style:{width:"100vw"},children:s.jsxs("div",{className:"card-area child90",children:[s.jsxs("div",{className:"parent125",children:[s.jsxs("b",{onClick:()=>h(0),className:0===n?"b63":"div656",children:["任务创建 ",0===n?s.jsx(r,{}):null]}),s.jsxs("b",{onClick:()=>h(1),className:1===n?"b63":"div656",children:["任务管理",1===n?s.jsx(r,{}):null]}),s.jsxs("b",{onClick:()=>h(2),className:2===n?"b63":"div656",children:["数据审核",2===n?s.jsx(r,{}):null]}),s.jsxs("b",{onClick:()=>h(3),className:3===n?"b63":"div656",children:["数据导出",3===n?s.jsx(r,{}):null]})]}),s.jsxs("div",{className:"div655 lightText",children:[s.jsx("p",{className:"p",children:"高效任务管理&数据"}),s.jsx("p",{className:"p",children:"审核平台"})]}),s.jsx("img",{className:"carousel-img",src:0===n?"/assets/create-task-CAzdQJGG.png":1===n?"/assets/task-management-CSqM_yg9.png":2===n?"/assets/task-review-BROJQUEw.png":3===n?"/assets/data-export-BC0PPOS4.png":"",width:"100%"}),s.jsx(i,{type:"primary",className:"div654 frame-child296 primary-btn boldText",onClick:()=>{t("/main/task/create")},style:{backgroundColor:"#CCFFA3",border:"#CCFFA3"},children:s.jsxs("div",{className:"upload-btn boldText",children:["创建第一个任务",s.jsx("img",{className:"frame-child297",alt:"",src:"data:image/svg+xml,%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M2%202H14V14'%20stroke='%2301120F'%20stroke-width='3'%20stroke-linejoin='round'/%3e%3cpath%20d='M14%202L2%2014'%20stroke='%2301120F'%20stroke-width='3'/%3e%3c/svg%3e"})]})}),s.jsxs("div",{className:"frame-parent11",children:[s.jsxs("div",{className:"rectangle-parent58",children:[s.jsx("div",{className:"frame-child298"}),s.jsx("div",{className:"frame-child299"}),s.jsx("div",{className:"frame-child300"})]}),s.jsx("b",{className:"one-click-upload boldText",style:{color:"white"},children:"Efficient & Data Audit"})]})]})})]})};export{t as default};

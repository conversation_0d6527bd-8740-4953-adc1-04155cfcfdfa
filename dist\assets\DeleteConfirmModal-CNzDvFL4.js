import{r as e,j as t,K as s,aE as i,aO as l}from"./react-core-BrQk45h1.js";const{confirm:n}=s,c=({visible:n,onCancel:c,onDelete:a,title:r="提示",content:o="此操作将永久删除该条目，是否继续？",okText:x="确认删除",cancelText:d="取消"})=>{const[h,g]=e.useState(!1),[y,m]=e.useState("");return t.jsx(s,{open:n,onCancel:c,footer:[t.jsxs("div",{style:{width:"100%",display:"flex",justifyContent:"center",gap:"10px"},children:[" ",t.jsx("button",{onClick:c,className:"custom-button",disabled:h,children:d},"cancel"),t.jsx("button",{className:"custom-button",onClick:async()=>{try{g(!0),m(""),await a(),c()}catch(e){m("删除失败，请稍后重试")}finally{g(!1)}},disabled:h,children:x},"delete")]})],title:t.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[t.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='12'%20r='12'%20fill='%23E75252'/%3e%3crect%20x='10.5'%20y='15.75'%20width='3'%20height='3'%20rx='1'%20fill='white'/%3e%3crect%20x='10.5'%20y='5.25'%20width='3'%20height='9'%20rx='1'%20fill='white'/%3e%3c/svg%3e",alt:"警告",className:"warning"}),t.jsx("span",{style:{marginLeft:8},children:r})]}),children:t.jsxs(i,{spinning:h,children:[t.jsx("div",{style:{display:"flex",justifyContent:"center"},children:o}),y&&t.jsx(l,{message:y,type:"error",showIcon:!0})]})})};export{c as D};

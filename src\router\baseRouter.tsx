import React from "react";
import Private from "../utils/Private";
import DashboardView from "../views/admin/DashboardView";
import AdjustmentView from "../views/task/finetuntask/adjustment-view";
import ConfigureView from "../views/task/finetuntask/configure-view";


const LoginView = React.lazy(() => import("../views/login/LoginView"));
const RegisterView = React.lazy(() => import("../views/RegisterView"));
const MainView = React.lazy(() => import("../views/main/MainView"));
const CreateTaskView = React.lazy(() => import("../views/task/create-task-view"));
const SourceDatasetView = React.lazy(
  () => import("../views/dataset/SourceDatasetView")
);
const TaskView = React.lazy(() => import("../views/task/task-view"));
const TaskDetailView = React.lazy(() => import("../views/task/task-detail-view"));
const DefaultReviewView = React.lazy(
  () => import("../views/review/DefaultReviewView")
);
// const FineReviewView = React.lazy(() => import('../views/main/task/FineReviewView'));
const FirstRunView = React.lazy(() => import("../views/main/FirstRunView"));
const AdminView = React.lazy(() => import("../views/admin/AdminView"));
const TrainingDetailsView = React.lazy(
  () => import("../views/training/TrainingDetailsView")
);
const FineTuneView = React.lazy(
  () => import("../views/task/finetuntask/fine-tune-view")
);
const CreateTrainingView = React.lazy(
  () => import("../views/training/CreateTrainingView")
);
const UploadDatasetView = React.lazy(
  () => import("../views/task/finetuntask/UploadDataset")
);
const UploadBaseModelView = React.lazy(
  () => import("../views/task/finetuntask/UploadBaseModel")
)
const ServerManagement = React.lazy(
  () => import("../views/task/finetuntask/server-management")
)
const NewServer = React.lazy(
  () => import("../views/task/finetuntask/server-management/components/NewServer")
)
const UpDateServer = React.lazy(
  () => import("../views/task/finetuntask/server-management/components/UpdateServer")
)
const baseRouter = [
  { path: "/login", element: <LoginView /> },
  { path: "/register", element: <RegisterView /> },
  {
    path: "/admin",
    element: (
      <Private>
        <AdminView />
      </Private>
    ),
    children: [
      { path: "/admin/dashboard", element: <DashboardView /> },
      // { path: "/admin/feedback", element: <FeedbackView /> },
    ],
  },
  {
    path: "/",
    element: (
      <Private>
        <MainView />
      </Private>
    ),
    // element:<MainView/>,
    children: [
      { path: "/main/sourcedata", element: <SourceDatasetView /> },
      { path: "/overview", element: <FirstRunView /> },
      {
        path: "/main/task",
        children: [
          { path: "/main/task", element: <TaskView /> },
          { path: "/main/task/create", element: <CreateTaskView /> },
          { path: "/main/task/uploadDataset", element: <UploadDatasetView /> },
          { path: "/main/task/detail/:task_id", element: <TaskDetailView /> },
          {
            path: "/main/task/review/:task_id",
            element: <DefaultReviewView />,
          },
          // {
          //   path: "/main/task/finereview/:task_id",
          //   element: <FineReviewView />,
          // },
        ],
      },
      {
        path: "/main/finetune",
        children: [
          { path: "/main/finetune", element: <FineTuneView /> },
          {
            path: "/main/finetune/detail/:id/:name",
            element: <TrainingDetailsView />,
          },
          {
            path: "/main/finetune/adjustment/:task_id",
            element: <AdjustmentView />,
          },
          {
            path: "/main/finetune/configure/:task_id/:name/:status",
            element: <ConfigureView />,
          },
          { path: "/main/finetune/create", element: <CreateTrainingView /> },
          { path: "/main/finetune/uploadBaseModel", element: <UploadBaseModelView /> },
          { path: "/main/finetune/serverManagement", element: <ServerManagement /> },
          { path: "/main/finetune/serverManagement/newServer", element: <NewServer /> },
          { path: "/main/finetune/serverManagement/updateServer/:server_id", element: <UpDateServer /> },    
        ],
      },
    ],
  },
  //     ],
  //   },
];

export default baseRouter;

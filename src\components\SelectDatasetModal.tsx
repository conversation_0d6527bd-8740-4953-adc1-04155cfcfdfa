import { But<PERSON>, Modal } from "antd";

import { useState, useRef, forwardRef, useImperativeHandle } from "react";
import { DataSetType } from "../types";
import DatasetTable from "./DatasetTable";

interface SelectDatasetModalProp {
  visible: boolean;
  OnClose: (rows?: DataSetType[]) => void;
  onRemoveSelection?: (datasetId: string) => void; // 新增：移除选中项的回调
}

const SelectDatasetModal = forwardRef<any, SelectDatasetModalProp>(({
  OnClose,
  visible,
  onRemoveSelection,
}, ref) => {
  const [filterType, setFilterType] = useState("datasetName");
  const [filterInput, setFilterInput] = useState("");
  const [filterTags, setFilterTags] = useState<string[]>([]);
  const [dateOrder, setDateOrder] = useState("asc");
  const [selectedRows, setSelectedRows] = useState<DataSetType[]>([]);
  const datasetTableRef = useRef<any>(null);

  useImperativeHandle(ref, () => ({
    removeSelection: (datasetId: string) => {
      if (datasetTableRef.current) {
        datasetTableRef.current.removeSelection(datasetId);
      }
    },
  }));

  const onOk = () => {
    OnClose(selectedRows);
    initData();
  };

  const onCancel = () => {
    OnClose();
    initData();
  };

  const initData = () => {
    setFilterType("datasetName");
    setFilterInput("");
    setFilterTags([]);
    setDateOrder("asc");
    setSelectedRows([]);
  };

  return (
    <Modal
      centered
      title="选择源数据集"
      keyboard={false}
      maskClosable={false}
      width={"55%"}
      open={visible}
      onOk={onOk}
      onCancel={onCancel}
      footer={[
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <label className="info-label">只能选择解析完成的源数据集哦</label>
          <Button
            type="primary"
            onClick={onOk}
            shape="round"
            className="primary-btn boldText"
            style={{ width: 124 }}
          >
            确认选择
          </Button>
        </div>,
      ]}
    >
      <DatasetTable
        ref={datasetTableRef}
        key={'ModalTable'}
        ShowActionColumn={false}
        onSelectRows={(data: DataSetType[]) => setSelectedRows(data)}
      />
    </Modal>
  );
});

export default SelectDatasetModal;

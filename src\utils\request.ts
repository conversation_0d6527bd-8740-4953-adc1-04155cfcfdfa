import { message } from "antd";
import axios from "axios";
// 添加加载提示
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import Cookies from 'js-cookie';

NProgress.settings.showSpinner = false;

const request = axios.create({
  baseURL: '/api',
  timeout: 1200000,
});
request.interceptors.request.use((config) => {
  let token = Cookies.get('token');
  NProgress.start();
  if (token) {
    config.headers["ACCESS-KEY"] = token;
  }
  return config;
});
request.interceptors.response.use(
  (res) => {
    NProgress.done();
    if (res.status !== 200) {
      // 没有请求成功
      if (res.status === 401) {
        // 没有权限
        message.info("没有权限");
      } else if (res.status === 500 || res.status === 505) {
        message.info("服务器错误");
      } else if (res.status === 404) {
        message.info("404找不到请求地址");
      } else if (res.status === 403) {
        message.info("登录状态已失效,请重新登录");
      } else {
        message.info("请求错误");
      }
    }else if(res.status === 200) {
      if (res.data.code && res.data.code !== 200) {
        // if (res.data.code === 401) {
        //   message.error("没有权限");
        // } else if (res.data.code === 500 || res.data.code === 505) {
        //   message.error("服务器错误");
        // } else if (res.data.code === 404) {
        //   message.error("找不到请求地址");
        // } else 
        if (res.data.code === 403) {
          // message.error("登录状态已失效,请重新登录");
          Cookies.remove('token');
        }
        //  else {
        //   message.error("请求错误");
        // }
        // message.error(res.data.message);
      }
    }
    return res;
  },
  (err) => {
    NProgress.done();
    message.info("请求错误");
    console.error(err);
    return err;
  }
);
export default request;

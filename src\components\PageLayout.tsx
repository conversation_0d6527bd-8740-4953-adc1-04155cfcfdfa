import React from "react";
import { Button, Layout, Space } from "antd";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { DownOutlined } from "@ant-design/icons";
import PageHeader from "./PageHeader";
const { Header, Content, Footer } = Layout;

interface LayoutProps {
  children: React.ReactNode;
  isShowFooter?: boolean;
}

const contentStyle: React.CSSProperties = {
  textAlign: "center",
  minHeight: "calc(100vh - 52px)",
  color: "#fff",
  // backgroundColor: "#000000",
};

const PageLayout: React.FC<LayoutProps> = ({ children, isShowFooter }) => {
  // const navigate = useNavigate();
  // function BackHomeButton() {
  //   const location = useLocation();
  //   if (location.pathname !== "/") {
  //     return (
  //       <div
  //         style={{
  //           textAlign: "start",
  //           fontSize: "14px",
  //           lineHeight: "16px",
  //         }}
  //       >
  //         <Button
  //           type="link"
  //           size="small"
  //           style={{ color: "#fff" }}
  //           onClick={() => {
  //             // props.history.push("/");
  //             navigate("/");
  //           }}
  //         >
  //           <DownOutlined />
  //           返回主页面
  //         </Button>
  //       </div>
  //     );
  //   } else return null;
  // }

  function FooterLabel(props: any) {
    if (props.isShow) {
      return (
        <div className="licenseMessage">
          <div>
            <label>@2023 EverReach 使用行至前必读    增值电信业务经营许可证： B1.B2-12345678</label>
          </div>
          <div>
            <label>京ICP证123456号</label>
            <Link to={""}>隐私政策</Link>
          </div>
        </div>
      );
    } else return null;
  }

  return (
    <Space direction="vertical" style={{ width: "100%" }} size={[0, 48]}>
      <Layout className="pageLayout">
        <PageHeader></PageHeader>
        <Content style={contentStyle}>
          {/* <BackHomeButton /> */}
          {children}
        </Content>
        <FooterLabel isShow={isShowFooter}></FooterLabel>
      </Layout>
    </Space>
  );
};

export default PageLayout;

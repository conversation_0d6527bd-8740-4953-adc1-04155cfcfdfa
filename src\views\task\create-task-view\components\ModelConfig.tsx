import React from 'react';
import { Form, Segmented, Slider, Tooltip, Divider } from 'antd';
import { paragraph, questionDensity } from '@/utils/conts';
import group151 from '@/assets/img/group-151.svg';
import { ModelConfigProps, CreateTaskFormType } from '../types';
import {
  MODEL_CONFIG_OPTIONS,
  SLIDER_STYLES,
  PARAGRAPH_MARKS_TEXT,
  QUESTION_DENSITY_MARKS_TEXT,
  TOOLTIP_MESSAGES,
  CSS_CLASSES,
} from '../constants';

// 创建滑块标记的辅助函数
const createSliderMark = (tooltipText: string) => (
  <Tooltip title={tooltipText}>
    <div style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}></div>
  </Tooltip>
);

const ModelConfig: React.FC<ModelConfigProps> = ({
  modelConfig,
  splitLevel,
  questionDensity: questionDensityValue,
  onModelConfigChange,
  onSplitLevelChange,
  onQuestionDensityChange,
}) => {
  // 生成段落精细度滑块标记
  const paragraphMarks = {
    0: createSliderMark(PARAGRAPH_MARKS_TEXT[0]),
    25: createSliderMark(PARAGRAPH_MARKS_TEXT[25]),
    50: createSliderMark(PARAGRAPH_MARKS_TEXT[50]),
    75: createSliderMark(PARAGRAPH_MARKS_TEXT[75]),
    100: createSliderMark(PARAGRAPH_MARKS_TEXT[100]),
  };

  // 生成问题密度滑块标记
  const questionDensityMarks = {
    0: createSliderMark(QUESTION_DENSITY_MARKS_TEXT[0]),
    25: createSliderMark(QUESTION_DENSITY_MARKS_TEXT[25]),
    50: createSliderMark(QUESTION_DENSITY_MARKS_TEXT[50]),
    75: createSliderMark(QUESTION_DENSITY_MARKS_TEXT[75]),
    100: createSliderMark(QUESTION_DENSITY_MARKS_TEXT[100]),
  };

  return (
    <>
      <Form.Item<CreateTaskFormType> label="模型参数输入">
        <Segmented
          className={CSS_CLASSES.CREATE_TASK_SEGMENTED}
          size="large"
          options={MODEL_CONFIG_OPTIONS.map((option) => ({
            label: (
              <Tooltip title={option.tooltip}>
                <a
                  onClick={() => onModelConfigChange(option.value)}
                  className={
                    modelConfig === option.value
                      ? CSS_CLASSES.MODEL_CONFIG_ACTIVE
                      : CSS_CLASSES.MODEL_CONFIG
                  }
                >
                  {option.label}
                </a>
              </Tooltip>
            ),
            value: option.value,
          }))}
          value={modelConfig}
          onChange={onModelConfigChange}
        />
      </Form.Item>

      {modelConfig === '手动配置' && (
        <div>
          <div className={CSS_CLASSES.CREATE_TASK_ITEM}>
            <div className={CSS_CLASSES.CUSTOM_CONFIG_LABEL}>
              <label className={CSS_CLASSES.CREATE_TASK_DRAGGER_LABEL}>段落精细度</label>
              <Tooltip title={TOOLTIP_MESSAGES.SPLIT_LEVEL}>
                <img className={CSS_CLASSES.FRAME_CHILD179} src={group151} alt="info" />
              </Tooltip>
            </div>
            <Form.Item<CreateTaskFormType> name="splitLevel" noStyle>
              <Slider
                defaultValue={splitLevel}
                className={CSS_CLASSES.CREATE_TASK_SLIDER}
                dots
                tooltip={{
                  formatter: (val) => {
                    const q = val ? val : 0;
                    return paragraph[q / 25];
                  },
                }}
                step={25}
                marks={paragraphMarks}
                value={splitLevel}
                onChange={onSplitLevelChange}
                railStyle={SLIDER_STYLES.RAIL}
                trackStyle={SLIDER_STYLES.TRACK}
                handleStyle={{}}
                style={SLIDER_STYLES.CONTAINER}
              />
            </Form.Item>
            <label
              className={CSS_CLASSES.INFO_LABEL}
              style={{ display: 'inline-block', marginLeft: '1rem' }}
            >
              <div className={CSS_CLASSES.SLIDER_LABEL}>{paragraph[splitLevel / 25]}</div>
            </label>
          </div>

          <div className={CSS_CLASSES.CREATE_TASK_ITEM} style={{ margin: '1rem 0' }}>
            <div className={CSS_CLASSES.CUSTOM_CONFIG_LABEL}>
              <label className={CSS_CLASSES.CREATE_TASK_DRAGGER_LABEL}>提问密度</label>
              <Tooltip title={TOOLTIP_MESSAGES.QUESTION_DENSITY}>
                <img className={CSS_CLASSES.FRAME_CHILD179} src={group151} alt="info" />
              </Tooltip>
            </div>
            <Form.Item<CreateTaskFormType> name="questionDensity" noStyle>
              <Slider
                defaultValue={questionDensityValue}
                className={CSS_CLASSES.CREATE_TASK_SLIDER}
                dots
                tooltip={{
                  formatter: (val) => {
                    const q = val ? val : 0;
                    return questionDensity[q / 25];
                  },
                }}
                step={25}
                marks={questionDensityMarks}
                value={questionDensityValue}
                onChange={onQuestionDensityChange}
                railStyle={SLIDER_STYLES.RAIL}
                trackStyle={SLIDER_STYLES.TRACK}
                handleStyle={{}}
                style={SLIDER_STYLES.CONTAINER}
              />
            </Form.Item>
            <label
              className={CSS_CLASSES.INFO_LABEL}
              style={{ display: 'inline-block', marginLeft: '1rem' }}
            >
              <div className={CSS_CLASSES.SLIDER_LABEL}>
                {questionDensity[questionDensityValue / 25]}
              </div>
            </label>
          </div>
          <Divider />
        </div>
      )}
    </>
  );
};

export default ModelConfig;

import{K as e,r as s,t,j as l,aB as n,B as r,af as a}from"./react-core-BrQk45h1.js";import{t as o,v as i}from"./task-ClBf-SyM.js";const d=({rowData:d,visible:c,OnClose:h,OnChange:p})=>{const{confirm:y}=e,[u,f]=s.useState([]),[m,x]=s.useState([]),[k,j]=s.useState(),[g,v]=s.useState([]),[b,C]=t.useMessage(),[I,N]=s.useState(!1),S=()=>{h()},E=()=>{const e={taskId:k,fileIdList:g.map((e=>e.toString()))};o(e)};return s.useEffect((()=>{var e;d&&(j(d.taskId),null==(e=d.problems)||e.forEach((e=>{const s=e.fileTreeNodeForProblem;x([s])})))}),[d]),l.jsx(e,{centered:!0,title:"解析失败文档",keyboard:!1,maskClosable:!1,width:588,open:c,onOk:S,onCancel:()=>{h()},footer:[l.jsxs("div",{style:{display:"flex",justifyContent:"end",alignItems:"center"},children:[l.jsx(r,{type:"link",className:"boldText",style:{color:"#111111"},onClick:()=>{var e,s;const n=y({centered:!0,title:"删除提示",icon:l.jsx(a,{}),width:540,content:l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"default-info",style:{color:"#111111"},children:"确定要删除所选数据集吗？"}),d&&(null==(e=null==d?void 0:d.relatedTask)?void 0:e.length)>0?l.jsx("div",{className:"upload-error-label",style:{marginTop:"8px"},children:"删除源数据集则无法在相关推理任务审核中进行原文对照，且删除后无法恢复。"}):null]}),footer:[l.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",padding:"2rem 0 0 0",gap:"8px"},children:[l.jsx(r,{type:"text",onClick:()=>{n.destroy(),S()},shape:"round",children:"取消"}),l.jsx(r,{disabled:d&&(null==(s=null==d?void 0:d.relatedTask)?void 0:s.length)>0,type:"primary",onClick:()=>{0!==g.length?(n.destroy(),E(),v([])):(t.info("没有选择文件或文件为空"),n.destroy(),S())},shape:"round",className:"primary-btn",style:{width:"120px"},children:"确认删除"})]})],onOk(){d&&(E(),p&&p(),n.destroy())},onCancel(){n.destroy()}});S()},children:"删除"}),l.jsx(r,{type:"primary",shape:"round",className:"primary-btn boldText",onClick:()=>{0!==g.length?((()=>{const e={taskId:k,fileIdList:g.map((e=>e.toString()))};i(e)})(),v([])):t.info("没有选择文件或文件为空"),S()},children:"重新推理"})]})],children:l.jsxs("div",{children:[l.jsx("div",{className:"upload-error-label",children:"以下文档解析失败，请选择处理方式"}),l.jsx(n,{checkable:!0,virtual:!1,treeData:m,onCheck:(e,s)=>{let t=[];t=Array.isArray(e)?e:e.checked;const l=e=>{const s=[];return"FILE"===e.type?s.push(e.fileId):e.children&&e.children.length>0&&e.children.forEach((e=>{"FILE"===e.type?s.push(e.fileId):s.push(...l(e))})),s};let n=[...g];if(!t.includes(s.node.key)){const e=l(s.node);n=n.filter((s=>!e.includes(s)))}t&&t.forEach((e=>{const t=s.node.key===e?s.node:s.checkedNodes.find((s=>s.key===e));t&&n.push(...l(t))})),v(n)},fieldNames:{title:"name",key:"name",children:"children"}})]})})};export{d as U};

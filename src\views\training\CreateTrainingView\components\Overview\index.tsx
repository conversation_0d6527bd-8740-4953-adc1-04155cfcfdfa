import { <PERSON><PERSON>, Col, Form, Row, Select, Tooltip } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import classes from "./index.module.css";
import { TariningType, ServerDataType, ModelFailData } from "../../type";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import {
  getServerList,
  getPower,
  predicateTime,
  getModelSets,
  getModelServerInfo
} from "../../../../../api/modle";
import EchartsBarComponent from "./echarts";
import infoIcon from "../../../../../assets/img/info-icon.svg";
import { trainingSet } from "../../../../../utils/conts";
import { log } from "console";
interface resourceDistributionProp {
  trainingData: TariningType;
}
interface configFormData {
  framework: string; //训练框架
  serverSelection: string; //服务器选择
  arithmeticConfiguration: string; //算力配置
}
interface PowerDataType {
  name: string;
  data: {
    estimated_increased_rate: string;
    free_rate: string;
    usage_rate: string;
  };
}
interface MyObject {
  modelId: number;
  modelConfigData: ModelFailData;
}
const Overview = forwardRef((prop: resourceDistributionProp, ref) => {
  const {
    framework,
    batchValue,
    iterationLevel,
    dataset,
    tasks,
    modelId,
    srategy,
    serverSelection,
    arithmeticConfiguration,
  } = prop.trainingData;

  const [form] = Form.useForm();
  if (prop.trainingData.framework) {
    form.setFieldsValue({
      framework: framework,
    });
  }
  useImperativeHandle(ref, () => ({
    getOverviewData: () => {
      // const id = Array.isArray(serverData) ? (serverData[0].id as any) : "";
      return {
        Framework: form.getFieldValue("framework"),
        ServerSelection: form.getFieldValue("serverSelection"),
        ArithmeticConfiguration: arithmeticData,
        ServerConfigId:Number(selectedServerId),
      };
    },
    validateFields: () => {
      form.validateFields();
    },
  }));

  //   训练框架数据
  const [frameData, setFrameData] = useState(framework || []);
  // 服务器选择

  const [serverData, setServerData] = useState<any>();
  // 算力配置
  const [serverOptions, setServerOptions] = useState<ServerDataType[]>();

  const [arithmeticData, setArithmeticData] = useState<string>();
  const [arithmeticOptions, setArithmeticOptions] = useState<any>([]);
  // const [isServerSelected, setIsServerSelected] = useState(false);
  const [powerTip, setPowerTip] = useState("");
  const [selectedServerId, setSelectedServerId] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  // 传值
  const [powerData, setPowerData] = useState<PowerDataType[]>([]);
  const [powerValue, setPowerValue] = useState<PowerDataType[]>([]);
  // 预估时长
  const [estimatedTime, setEstimatedTime] = useState<any>({
    estimatedTime: "",
    unit: "",
  });
  const training: string = localStorage.getItem("trainingData") || "{}";
  const objectArray: MyObject[] = JSON.parse(training) as MyObject[];
  const [modelFailProp, setModelFailProp] = useState<ModelFailData>(
    objectArray[0]?.modelConfigData
  );
  const modelFailRequest = modelFailProp?.properties.basicConfigRequest;
  // 训练框架选项
  const frameOptions = [
    {
      label: "pytorch",
      value: "pytorch",
    },
  ];
  const serverOptionsRef = useRef<ServerDataType[]>();
  useEffect(() => {
    serverOptionsRef.current = serverOptions;
  }, [serverOptions]);
  const getServer = () => {
    getServerList().then((res: any) => {
      if (res.data?.code === 200) {
        const serverConfigs = res.data.data.server_config;
        if (Array.isArray(serverConfigs)) {
          setServerOptions(
            serverConfigs.map((item: any) => {
              return {
                label: item.server_name,
                value: item.server_name,
                id: item.id,
              };
            })
          );
        } else {
          console.error(
            "获取的服务器配置数据不是数组类型，无法更新 serverData"
          );
          setServerOptions([]);
        }
      }
    });
  };
  useEffect(() => {
    getServer();
  }, []);
  // useEffect(() => {
  //   if (selectedServerId) {
  //     getPowerList(selectedServerId);
  //   }
  // }, [selectedServerId]);
  const getPowerList = (id: string) => {
    getPower(id).then((res: any) => {
      if (res.data?.code && res.data?.code === 200) {
        const powerList = res.data.data.gpu_usage;
        const newPowerList: {
          [key: string]: {
            estimated_increased_rate: string;
            free_rate: string;
            usage_rate: string;
          };
        } = {};
        Object.keys(powerList).forEach((key) => {
          const newKey = `A100_${("0" + (Number(key) + 1)).slice(-2)}`;
          newPowerList[newKey] = powerList[key];
        });
        const powerArray = Object.entries(newPowerList).map(([key, value]) => ({
          name: key,
          data: value,
        }));
        // console.log("powerArray", powerArray);
        setPowerData(powerArray);
        console.log("powerData", powerData);

        const Options = powerArray.map((item: any, index: any) => {
          const ids = Object.keys(powerList);
          return {
            id: ids[index],
            label: item.name as string,
            value: item.name as string,
          };
        });
        setArithmeticOptions(Options);
      }
    });
  };

  const getPredictTime = () => {
    const params = {
      batchSize: Number(batchValue),
      deviceCount: 1,
      deviceType: serverData?.[0]?.value || modelFailRequest?.server,
      iterationRound: Number(iterationLevel),
      modelId: Number(modelId),
      taskId: tasks?.length > 0 ? tasks.map((task) => task.taskId) : [],
      trainStrategy: srategy,
    };
    // 预估训练时长
    if (params.deviceType) {
      predicateTime(params).then((res: any) => {
        if (res.data.code === 200) {
          setEstimatedTime(res.data.data);
        }
      });
    }
  };
  useEffect(() => {
    if (!arithmeticData) return;
    const filteredData = powerData.filter(item => item?.name === arithmeticData as any);
    setPowerValue(filteredData);
  },[arithmeticData]);

  return (
    <>
      <Row className={classes["step-container"]}>
        <Col span={3.5} className={classes["step-label"] + " boldText"}>
          {/* Step 4 */}
          资源配置
        </Col>
        <Col span={18.5} className={classes["step-content"]}>
          <div style={{ fontSize: "14px" }}>
            <Form
              form={form}
              initialValues={{
                framework:
                  frameData ||
                  (modelFailProp?.properties
                    ? modelFailRequest?.trainingFramework
                    : ""),
                serverSelection: serverData,
                arithmeticConfiguration: arithmeticData,
              }}
              onFinish={(e) => {
                console.log("finish");
              }}
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 22, offset: 2 }}
              className={classes["form-container"]}
            >
              <Form.Item<configFormData>
                name="framework"
                label="训练框架:"
                labelAlign="left"
                rules={[{ required: true, message: "请选择训练的框架" }]}
                style={{ width: "511px" }}
                className={classes["form-items"]}
              >
                <Select
                  size="large"
                  showSearch
                  placeholder="请选择训练的框架"
                  value={frameData}
                  onChange={(e) => {
                    setFrameData(e);
                  }}
                  options={frameOptions}
                ></Select>
              </Form.Item>
              <Form.Item<configFormData>
                name="serverSelection"
                label="服务器选择:"
                labelAlign="left"
                rules={[{ required: true, message: "请选择服务器" }]}
                style={{ width: "511px" }}
              >
                <Select
                  size="large"
                  showSearch
                  placeholder="请选择服务器"
                  // defaultValue={""}
                  value={serverData}
                  onChange={(e) => {
                    const serverId = String(
                      serverOptionsRef.current?.find(item => item.value === e)?.id
                    );
                    setSelectedServerId(serverId);
                    getPowerList(serverId);
                    setArithmeticData("");
                    setServerData(e);
                  }}
                  style={{ flex: 1 }}
                  options={serverOptions}
                ></Select>
              </Form.Item>
              <Form.Item<configFormData>
                name="arithmeticConfiguration"
                label="算力配置:"
                labelAlign="left"
                rules={[{ required: true, message: "请选择算力配置" }]}
                style={{ width: "511px" }}
              >
                <Select
                  size="large"
                  showSearch
                  key={selectedServerId}
                  placeholder="请选择算力配置"
                  value={arithmeticData}
                  onChange={(e) => {
                    setArithmeticData(e);
                  }}
                  style={{ flex: 1 }}
                  options={arithmeticOptions}
                ></Select>
              </Form.Item>
              {/* {isLoading ? (
                <LoadingOutlined spin style={{ fontSize: "104px" }} /> // 此处替换为真实的加载动画组件或样式
              ) : ( */}
                {powerData.length > 0 && (
                  <div className={classes["chart"]}>
                    <EchartsBarComponent powerData={powerValue} />
                  </div>
                )}
              {/* )} */}
            </Form>
          </div>
          <div className={classes["estimatetime"]}>
            <p className={classes["text"]}>
              预估训练时长:&emsp;
              {estimatedTime && estimatedTime.estimatedTime
                ? estimatedTime.estimatedTime >= 3600
                  ? Math.floor(estimatedTime.estimatedTime / 3600) +
                    " 小时 " +
                    Math.floor((estimatedTime.estimatedTime % 3600) / 60) +
                    " 分钟 " +
                    (estimatedTime.estimatedTime % 60) +
                    " 秒 "
                  : estimatedTime.estimatedTime > 60
                  ? Math.floor(estimatedTime.estimatedTime / 60) +
                    " 分钟 " +
                    (estimatedTime.estimatedTime % 60) +
                    " 秒 "
                  : estimatedTime.estimatedTime + " 秒 "
                : "未知"}
            </p>
          </div>
        </Col>
      </Row>
    </>
  );
});
export default Overview;

import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import "../css/PageHeader.css";
import {
  Avatar,
  Drawer,
  Dropdown,
  MenuProps,
  Modal,
  Input,
  Upload,
  Button,
  Form,
  message,
} from "antd";
import { getLogoInfo, updateLogoInfo } from "../api/auth";
import whiteLogo from "../assets/img/white-logo.svg";
import UserView from "../views/UserView";
import avatar1 from "../assets/img/avatar-1.png";
import { queryUserInfo } from "../views/login/service";
import Cookies from 'js-cookie';
import { UploadOutlined } from "@ant-design/icons";
import type { UploadFile, UploadProps } from 'antd';
interface InputFormProps {
  inputData: string;
  logoUrl: File | null;
}
const normFile = (e: any) => {
  if (Array.isArray(e)) {
    return e;
  }
  return e?.fileList;
};

const InputForm = (props: any) => {
  const { inputContent } = props;
  const [renameInput, setRenameInput] = useState<string>();
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      inputData: inputContent,
      logoUrl: [] 
    });
  }, [inputContent]);

  const handleSubmit = (values: any) => {
    if (!values.logoUrl?.length) {
      message.error("请上传图片");
      return;
    }

    const params = {
      logoName: values.inputData,
      file: values.logoUrl[0].originFileObj,
    };
    
    updateLogoInfo(params)
      .then((res: any) => {
        if (res?.status === 200) {
          message.success("修改成功");
          props.fetchAgain();
        }
      })
      .catch((err: any) => {
        message.error("修改失败");
      });
    handleCancel();
  };

  const handleCancel = () => {
    form.resetFields();
    props.onClose();
  };

  return (
    <div className="rename-input">
      <Form
        form={form}
        onFinish={handleSubmit}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18, offset: 2 }}
      >
        <Form.Item label="重命名" name="inputData">
          <Input
            value={renameInput}
            onChange={(e) => setRenameInput(e.target.value)}
          />
        </Form.Item>

        <Form.Item
          label="上传图片"
          name="logoUrl"
          valuePropName="fileList"
          getValueFromEvent={normFile}
          rules={[{ required: true, message: '请选择上传文件' }]}
        >
          <Upload
            name="logo"
            listType="picture"
            maxCount={1}
            accept=".png,.jpg,.jpeg"
            beforeUpload={() => false}
          >
            <Button icon={<UploadOutlined />}>选择文件</Button>
          </Upload>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 15 }}>
          <Button type="primary" htmlType="submit" style={{ marginRight: 14 }}>
            提交
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </Form.Item>
      </Form>
    </div>
  );
};
const PageHeader: React.FC = () => {
  const [openIput, setOpenIput] = useState(false);
  const [inputContent, setInputContent] =
    useState<string>("EverReachAI Pollux");
  const [logo, setLogo] = useState(whiteLogo || "");
  const [localFileReceived, setLocalFileReceived] = useState<File | null>();
  const [activeTab, setActiveTab] = useState(0);
  const [userInfo, setUserInfo] = useState<any>();
  const [avatar, setAvatar] = useState(avatar1);
  const navigate = useNavigate();
  const location = useLocation();
  const tabs: string[] = [
    "/overview",
    "/main/sourcedata",
    "/main/task",
    "/main/finetune",
  ];
  const [userCompOpen, setUserCompOpen] = useState(false);
  const items: MenuProps["items"] = [
    // {
    //   key: 'userCenter',
    //   label: '用户中心'
    // },
    {
      key: "logout",
      label: "退出登录",
    },
  ];
  useEffect(() => {
    const userInfoStr = sessionStorage.getItem("userInfo");
    if (userInfoStr) {
      setUserInfo(JSON.parse(userInfoStr));
    }
  }, []);

  useEffect(() => {
    if (userInfo) {
      setAvatar(userInfo.avatar);
    }
  }, [userInfo]);
  useEffect(() => {
    // 在路由变化时执行的操作
    console.log("路由已变化:", location.pathname);
    tabs.forEach((tab, index) => {
      // if (tab === location.pathname) {
      //   setActiveTab(index + 1);
      // }
      if (location.pathname.indexOf(tab) > -1) {
        setActiveTab(index + 1);
      }
    });
  }, [location]);

  const onClick: MenuProps["onClick"] = ({ key }) => {
    if (key === "logout") {
      navigate("/login");
      Cookies.remove('token');
    } else if (key === 'userCenter') {
      // setUserCompOpen(true)
    }
  };
  const UserName = () => {
    const [name, setName] = useState("");

    useEffect(() => {
      const fetchUserName = async () => {
        const res = await queryUserInfo();
        if (res && res.data) {
          setName(res.data.userName);
        }
      };

      fetchUserName();
    }, []);

    return <b className="hello16">Hello, {name}</b>;
  };

  // 获取logo信息
  const fetchLogoInfo = () => {
    getLogoInfo().then((res) => {
      if (res && res?.data?.code === 200) {
        const { logoName, logoUrl } = res.data.data;
        setInputContent(logoName);
        setLogo(logoUrl);
      }
    });
  };
  useEffect(() => {
    fetchLogoInfo();
  }, [inputContent, logo]);
  // 修改logo信息

  const closeModal = () => {
    setOpenIput(false);
  };

  return (
    <div
      className={
        // location.pathname === "/overview"
        // "hello-parent14 headerFirst"
        "hello-parent14 headerDefault"
      }
    >
      <img src={logo} className="logo37" />
      <div
        className="everreachai-pollux20 enText"
        onClick={() => setOpenIput(true)}
      >
        {inputContent}
      </div>
      <div className="parent131">
        <div
          className={activeTab === 1 ? "div691 active-tab" : "div691"}
          onClick={() => {
            if (userCompOpen) {
              setUserCompOpen(false);
            }
            setActiveTab(1);
            navigate("/overview");
          }}
        >
          概览
        </div>
        <div
          className={activeTab === 2 ? "div691 active-tab" : "div691"}
          onClick={() => {
            if (userCompOpen) {
              setUserCompOpen(false);
            }
            setActiveTab(2);
            navigate("/main/sourcedata");
          }}
        >
          源数据集
        </div>
        <div
          className={activeTab === 3 ? "div691 active-tab" : "div691"}
          onClick={() => {
            if (userCompOpen) {
              setUserCompOpen(false);
            }
            setActiveTab(3);
            navigate("/main/task");
          }}
        >
          {/* 推理任务 */}
          训练数据
        </div>
        <div
          className={activeTab === 4 ? "div691 active-tab" : "div691"}
          onClick={() => {
            if (userCompOpen) {
              setUserCompOpen(false);
            }
            setActiveTab(4);
            navigate("/main/finetune");
          }}
        >
          {/* 微调训练 */}
          模型训练
        </div>
      </div>
      <div
        className="frame-child320"
        style={
          location.pathname === "/main/user"
            ? { display: "inherit" }
            : { display: "none" }
        }
      />

      <>{UserName()}</>
      <Dropdown menu={{ items, onClick }}>
        <Avatar
          className="ellipse-parent29"
          //  onClick={
          // () =>
          // setUserCompOpen(!userCompOpen)
          // }
          src={avatar}
        />
      </Dropdown>
      <Drawer
        mask={false}
        height={"calc(100% - 4rem)"}
        placement={"bottom"}
        closable={false}
        onClose={() => setUserCompOpen(false)}
        open={userCompOpen}
        style={{
          backgroundColor: "transparent",
        }}
        contentWrapperStyle={{
          boxShadow: "unset",
        }}
        styles={{
          body: {
            borderRadius: "24px 24px 0px 0px",
            background:
              "linear-gradient(180deg, #E1F3FB 0%, #F3F7FB 13.54%, #F8FAFC 100%)",
          },
        }}
      >
        <UserView OnClose={() => setUserCompOpen(false)} />
      </Drawer>
      <Modal
        title="修改"
        open={openIput}
        okButtonProps={{ style: { display: "none" } }}
        cancelButtonProps={{ style: { display: "none" } }}
        onCancel={closeModal}
      >
        <InputForm inputContent={inputContent} onClose={closeModal}  fetchAgain={fetchLogoInfo}></InputForm>
      </Modal>
    </div>
  );
};

export default PageHeader;

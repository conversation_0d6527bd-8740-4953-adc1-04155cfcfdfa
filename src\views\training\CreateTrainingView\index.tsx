import Scrollbar from "react-scrollbars-custom";
import { Button, Form, Space, Steps, Tooltip, message } from "antd";
import { LeftOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useEffect, useRef, useState, CSSProperties } from "react";
import ModelSelect from "./components/ModelSelect";
import TaskSelect from "./components/TaskSelect";
import classes from "./index.module.css";
import Overview from "./components/Overview";
import { ModelSelectType, TariningType, ModelFailData } from "./type";
import TrainingResult from "./components/TrainingResult";
import TrainingSetSelect from "./components/TrainingSetSelect";
import {
  CreateTrainingParams,
  createModel,
  createTraining,
  startTrain,
  deleteModel,
} from "../../../api/modle";
import ResultView from "../../../views/training/CreateTrainingView/components/resultView";
import { loadAsync } from "jszip";

const ChildComponents = ({ showResult, stepIndex, trainingData, trainingSetSelectRef, resourceSetSelectRef, modelSelectRef, taskSelectRef }: any) => {
  if (showResult) {
    return <TrainingResult />;
  }
  if (stepIndex === 0) {
    return <ModelSelect ref={modelSelectRef} modelData={trainingData} />;
  }
  if (stepIndex === 1) {
    return <TaskSelect ref={taskSelectRef} modelData={trainingData} />;
  }
  if (stepIndex === 2) {
    return (
      <TrainingSetSelect
        ref={trainingSetSelectRef}
        modelData={trainingData}
      />
    );
  }
  if (stepIndex === 3) {
    return (
      <Overview ref={resourceSetSelectRef} trainingData={trainingData} />
    );
  }
  return <></>;
};
interface MyObject {
  modelId: number;
  modelConfigData: ModelFailData;
}

const CreateTrainingView: React.FC = () => {
  const navigate = useNavigate();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [scrollBasedStepIndex, setScrollBasedStepIndex] = useState(0); // 新增：基于滚动位置的步骤索引
  const [maxReachedStepIndex, setMaxReachedStepIndex] = useState(0); // Tracks the furthest step reached
  const [isSticky, setIsSticky] = useState(false); // 新增状态用于跟踪吸顶状态

  const modelSelectRef = useRef<any>(null);
  const taskSelectRef = useRef<any>(null);
  const trainingSetSelectRef = useRef<any>(null);
  const resourceSetSelectRef = useRef<any>(null);

  const modelSectionRef = useRef<HTMLDivElement>(null);
  const taskSectionRef = useRef<HTMLDivElement>(null);
  const trainingSetSectionRef = useRef<HTMLDivElement>(null);
  const overviewSectionRef = useRef<HTMLDivElement>(null);
  const sectionRefs = [modelSectionRef, taskSectionRef, trainingSetSectionRef, overviewSectionRef];

  const scrollerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null); // 新增ref用于吸顶元素

  const [showResult, setShowResult] = useState(false);
  const [showTraining, setShowTraining] = useState(false); // 新增状态用于控制TrainingResult的显示
  const [isOnline, setIsOnline] = useState("");
  const [trainingData, setTrainingData] = useState<TariningType>(
    new Object() as TariningType
  );
  const training: string = localStorage.getItem("trainingData") || "{}";

  useEffect(() => {
    // console.log(trainingData, "updated trainingData");
  }, [trainingData]);

  useEffect(() => {
    // 延迟设置观察器，确保组件已完全挂载
    const timer = setTimeout(() => {
      const observers: IntersectionObserver[] = [];
      const intersectingEntries = new Map<number, IntersectionObserverEntry>();

      const scrollElement = document.querySelector('.createTaskArea');

      if (scrollElement) {
        const observerOptions = {
          root: scrollElement,
          rootMargin: '-80px 0px -60% 0px', // 优化触发区域
          threshold: [0.1, 0.3, 0.5], // 多个阈值，提高检测精度
        };

        //确定最可见的区域
        const updateCurrentStep = () => {
          if (intersectingEntries.size === 0) return;

          // 找到交集比例最大的区域
          let maxRatio = 0;
          let mostVisibleIndex = 0;

          intersectingEntries.forEach((entry, index) => {
            if (entry.intersectionRatio > maxRatio) {
              maxRatio = entry.intersectionRatio;
              mostVisibleIndex = index;
            }
          });

          setScrollBasedStepIndex(mostVisibleIndex);
          setMaxReachedStepIndex((prevMax) => Math.max(prevMax, mostVisibleIndex));
        };

        sectionRefs.forEach((sectionRef, index) => {
          if (sectionRef.current) {
            const observer = new IntersectionObserver((entries) => {
              entries.forEach((entry) => {
                if (entry.isIntersecting && entry.intersectionRatio >= 0.1) {
                  intersectingEntries.set(index, entry);
                } else {
                  intersectingEntries.delete(index);
                }
              });
              updateCurrentStep();
            }, observerOptions);
            observer.observe(sectionRef.current);
            observers.push(observer);
          }
        });
      }

      return () => {
        observers.forEach(observer => observer.disconnect());
      };
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // 添加检测吸顶状态的效果
  useEffect(() => {
    // 防抖函数，减少状态更新频率
    const debounce = (func: Function, wait: number) => {
      let timeout: NodeJS.Timeout | null = null;
      return (...args: any[]) => {
        if (timeout) clearTimeout(timeout);
        timeout = setTimeout(() => {
          func(...args);
        }, wait);
      };
    };

    // 检查是否处于吸顶状态
    const checkIfSticky = () => {
      if (showResult && !showTraining) {
        setIsSticky(false);
        return;
      }

      const scrollElement = document.querySelector('.ScrollbarsCustom-Scroller');
      if (scrollElement) {
        const scrollTop = scrollElement.scrollTop || 0;
        setIsSticky(scrollTop > 170);
      }
    };

    const debouncedCheckIfSticky = debounce(checkIfSticky, 5);

    const timer = setTimeout(() => {

      const scrollElement = document.querySelector('.ScrollbarsCustom-Scroller');
      if (scrollElement) {
        scrollElement.addEventListener('scroll', debouncedCheckIfSticky, { passive: true });
        checkIfSticky();
      }
    }, 100);

    // 组件卸载时清理
    return () => {
      clearTimeout(timer);
      const scrollElement = document.querySelector('.ScrollbarsCustom-Scroller');
      if (scrollElement) {
        scrollElement.removeEventListener('scroll', debouncedCheckIfSticky);
      }
    };
  }, [showResult, showTraining]);


  const collectAllTrainingData = () => {
    let collectedData = { ...trainingData };

    if (modelSelectRef.current) {
      const modelSelectData = modelSelectRef.current.getModelSelectData();
      collectedData = {
        ...collectedData,
        dataset: modelSelectData.Dataset,
        modelIntro: modelSelectData.Description,
        modelName: modelSelectData.Name,
        testSetRatio: modelSelectData.ModelType,
        modelId: modelSelectData.Id,
      };
    }
    if (taskSelectRef.current) {
      const taskSelectData = (taskSelectRef.current as any).getTaskSelectData();
      collectedData = {
        ...collectedData,
        tasks: taskSelectData.Task,
        testSet: taskSelectData.TestSet,
        testSetConfig: taskSelectData.TestSetConfig,
      };
    }
    if (trainingSetSelectRef.current) {
      const trainingConfigData = (trainingSetSelectRef.current as any).getTaskSelectData();
      collectedData = {
        ...collectedData,
        trainSetConfig: trainingConfigData.TrainSetConfig,
        srategy: trainingConfigData.Srategy,
        iterationLevel: trainingConfigData.IterationLevel,
        learningValue: trainingConfigData.LearningValue,
        batchValue: trainingConfigData.BatchValue,
      };
    }
    if (resourceSetSelectRef.current) {
      const overviewConfigData = resourceSetSelectRef.current.getOverviewData();
      collectedData = {
        ...collectedData,
        framework: overviewConfigData.Framework,
        serverSelection: overviewConfigData.ServerSelection,
        arithmeticConfiguration: overviewConfigData.ArithmeticConfiguration,
        serverConfigId: overviewConfigData.ServerConfigId,
      };
    }
    setTrainingData(collectedData);
    return collectedData;
  };

  const handleStepClick = (index: number) => {
    setCurrentStepIndex(index);
    const headerHeight = 50;
    setTimeout(() => {
      const targetSection = sectionRefs[index]?.current;
      if (targetSection) {
        const scrollElement = document.querySelector('.createTaskArea');
        if (scrollElement) {
          let targetPosition = targetSection.offsetTop - headerHeight;
          // 确保滚动位置不会超出容器范围
          const maxScrollTop = scrollElement.scrollHeight - scrollElement.clientHeight;
          targetPosition = Math.min(targetPosition, maxScrollTop);
          targetPosition = Math.max(targetPosition, 0);

          scrollElement.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });
        }
      }
    }, 0);
  };

  const validateAllSteps = async (): Promise<{ isValid: boolean; firstInvalidStepIndex?: number }> => {
    // Step 0: ModelSelect
    if (modelSelectRef.current) {
      const modelData = modelSelectRef.current.getModelSelectData();
      if (!modelData.Name || !modelData.Description || !modelData.Dataset) {
        modelSelectRef.current.validateFields?.();
        return { isValid: false, firstInvalidStepIndex: 0 };
      }
    }
    // Step 1: TaskSelect
    if (taskSelectRef.current) {
      const taskData = (taskSelectRef.current as any).getTaskSelectData();
      if (!taskData.Task || taskData.Task.length === 0) {
        return { isValid: false, firstInvalidStepIndex: 1 };
      }
    }
    // Step 2: TrainingSetSelect
    if (trainingSetSelectRef.current) {
      const trainingSetData = (trainingSetSelectRef.current as any).getTaskSelectData();
      if (!trainingSetData.Srategy || trainingSetData.Srategy.length === 0) {
        return { isValid: false, firstInvalidStepIndex: 2 };
      }
    }
    // Step 3: Overview (ResourceSetSelect)
    if (resourceSetSelectRef.current) {
      const overviewData = resourceSetSelectRef.current.getOverviewData();
      if (
        !overviewData.Framework || overviewData.Framework.length === 0 ||
        overviewData.ServerSelection === undefined ||
        !overviewData.ArithmeticConfiguration || overviewData.ArithmeticConfiguration.length === 0 ||
        !Number.isInteger(overviewData.ServerConfigId)
      ) {
        resourceSetSelectRef.current.validateFields?.();
        return { isValid: false, firstInvalidStepIndex: 3 };
      }
    }
    return { isValid: true };
  };

  useEffect(() => {
    const online: any = localStorage.getItem("isOnline");
    setIsOnline(online);
  }, [localStorage.getItem("isOnline")]);


  const convertPercentageToDecimal = (percentageStr: string) => {
    if (!percentageStr) return 0;
    const numericValue = parseFloat(percentageStr.replace("%", ""));
    return numericValue / 100;
  };

  const handleNextBtnClick = async () => {
    const validationResult = await validateAllSteps();
    if (!validationResult.isValid) {
      message.error("请将信息填写完整");
      if (validationResult.firstInvalidStepIndex !== undefined) {
        setCurrentStepIndex(validationResult.firstInvalidStepIndex);
        setScrollBasedStepIndex(validationResult.firstInvalidStepIndex); // 同时更新滚动基础的步骤索引

        const headerHeight = 50;
        const targetSection = sectionRefs[validationResult.firstInvalidStepIndex]?.current;
        if (targetSection) {
          const scrollElement = document.querySelector('.createTaskArea');
          if (scrollElement) {
            let targetPosition = targetSection.offsetTop - headerHeight;
            // 确保滚动位置不会超出容器范围
            const maxScrollTop = scrollElement.scrollHeight - scrollElement.clientHeight;
            targetPosition = Math.min(targetPosition, maxScrollTop);
            targetPosition = Math.max(targetPosition, 0);

            scrollElement.scrollTo({
              top: targetPosition,
              behavior: 'smooth'
            });
          }
        }
      }
      return;
    }
    collectAllTrainingData();
    setShowResult(true);
    setIsSticky(false);
  };

  const handleTariningBtnClick = async () => {
    const currentAllData = collectAllTrainingData();

    const params = {
      basicConfigRequest: {
        computeResource: Array.isArray(currentAllData.arithmeticConfiguration) ? currentAllData.arithmeticConfiguration : [currentAllData.arithmeticConfiguration],
        server: currentAllData.serverSelection,
        serverConfigId: currentAllData.serverConfigId,
        trainingFramework: currentAllData.framework,
      },
      category: currentAllData.testSetRatio === 1 ? 0 : 1,
      modelName: currentAllData.modelName,
      modelId: currentAllData.modelId,
      introduction: currentAllData.modelIntro,
      // scene: "",
      trainConfig: {
        id:
          currentAllData.tasks?.length > 0
            ? currentAllData.tasks.map((task: any) => task.taskId) // 确保任务有taskId
            : ["86fdfeef55b6bcf4768ab9b0bd7a0cba"], // 默认值或确保这个被处理
        datasetRadio: convertPercentageToDecimal(currentAllData.testSet),
        trainStrategy: currentAllData.srategy,
        interationNumber: Number(currentAllData.iterationLevel),
        batchSize: Number(currentAllData.batchValue),
        learnRate: currentAllData.learningValue,
        baseModelId: currentAllData.modelId,
      },
    };

    const trainingFromStorageString = localStorage.getItem('trainingData');
    if (trainingFromStorageString) {
      localStorage.removeItem('trainingData');
      try {
        const trainingFromStorageArray: MyObject[] = JSON.parse(trainingFromStorageString);
        if (trainingFromStorageArray.length > 0 && trainingFromStorageArray[0].modelId) {
          console.log('clear', trainingFromStorageArray[0].modelId);
          deleteModel(trainingFromStorageArray[0].modelId);
        }
      } catch (e) {
        console.error("Failed to parse trainingData from localStorage", e);
      }
    }

    createModel(params).then((res) => {
      if (res?.data?.code === 200) {
        setShowTraining(true);
        startTrain(res.data?.data.modelId);
      } else if (res?.data?.code === 50001) {
        message.error(res?.data?.message)
      }
    });
  };

  const customProgressDot = (_dot: React.ReactNode, { index }: { status: string; index: number }) => {
    let style: CSSProperties = {
      width: '8px',
      height: '8px',
      borderRadius: '50%',
      boxSizing: 'border-box',
      backgroundColor: '#7792B9'
    };

    // 当前步骤：空心圆（白色填充，绿色边框）
    if (index === scrollBasedStepIndex || index < scrollBasedStepIndex) {
      style = {
        ...style,
        width: '16px',
        height: '16px',
        backgroundColor: 'white',
        border: '4px solid #0FB698'
      };
    }

    return <div style={style} />;
  };


  const stepItems = [
    { title: "模型选择" },
    { title: "数据配置" },
    { title: "训练配置" },
    { title: "资源配置" },
  ];


  return (
    <>
      <div
        style={{ height: '90vh' }}
      // scrollerProps={{ ref: scrollerRef }} // Pass ref to the scroller element
      >
        <div className="createTaskContent">
          <div
            ref={headerRef}
            className={classes["training-header"]}
            style={{
              position: 'sticky',
              top: 0,
              background: isSticky ? 'white' : 'transparent',
              zIndex: 1000,
              height: isSticky ? '88px' : '44px',
              paddingTop: isSticky ? '44px' : '0',
              boxShadow: isSticky ? "0px 4px 4px 0px #7792B91A" : 'none',
              transition: 'height 0.08s linear, padding-top 0.08s linear, background-color 0s linear'
            }}>
            <Space
              size={20}
              style={{ display: "inline-flex", alignItems: "center" }}
            >
              <Button
                style={{ fontSize: "12px", width: "36px", height: "36px" }}
                shape="circle"
                icon={<LeftOutlined />}
                onClick={() => {
                  navigate("/main/finetune")
                  localStorage.removeItem('trainingData')
                }}
              />
              <div
                className="mediumText"
                style={{
                  fontSize: "28px",
                  lineHeight: "36px",
                  fontWeight: "500",
                }}
              >
                微调训练
              </div>
            </Space>
            <div className={classes["step-div"]}>
              {!showResult ? (
                <Steps
                  progressDot={customProgressDot}
                  size="small"
                  current={scrollBasedStepIndex}
                  onChange={handleStepClick}
                  items={stepItems}
                  style={{ height: '4px' }}
                />
              ) : null}
            </div>
          </div>

          {!showResult ? (
            <div className="createTaskArea" style={{
              position: "relative", overflowY: "auto",
              height: "680px"
            }}>
              <Form>
                <div id="model-select-section" ref={modelSectionRef} style={{ marginBottom: '60px', borderBottom: "1px solid #D9D9D9", padding: '20px' }}>
                  <ModelSelect ref={modelSelectRef} modelData={trainingData} />
                </div>
                <div id="task-select-section" ref={taskSectionRef} style={{ marginBottom: '60px', borderBottom: "1px solid #D9D9D9", padding: '20px' }}>
                  <TaskSelect ref={taskSelectRef} modelData={trainingData} />
                </div>
                <div id="training-set-section" ref={trainingSetSectionRef} style={{ marginBottom: '60px', borderBottom: "1px solid #D9D9D9", padding: '20px 20px 72px 20px' }}>
                  <TrainingSetSelect
                    ref={trainingSetSelectRef}
                    modelData={trainingData}
                  />
                </div>
                <div id="overview-section" ref={overviewSectionRef} style={{ marginBottom: '60px', padding: '20px' }}>
                  <Overview ref={resourceSetSelectRef} trainingData={trainingData} />
                </div>

                <div className={classes["pre-next-div"]}>
                  <Button
                    type="primary"
                    className={`primary-btn ${classes["next-btn"]}`}
                    onClick={handleNextBtnClick}
                  // disabled={isOnline === "1"} // 如果需要保留
                  >
                    下一步
                  </Button>
                </div>
              </Form>
            </div>
          ) : (
            <div className="createTaskArea">
              {showTraining ? (
                <TrainingResult />
              ) : (
                <div style={{ marginTop: '50px' }}>
                  <ResultView trainingData={trainingData} />
                </div>

              )}
              {!showTraining && (
                <div className={classes["pre-next-div"]}>
                  <Button
                    type="primary"
                    className={`primary-btn ${classes["next-btn"]}`}
                    onClick={handleTariningBtnClick}
                  // disabled={isOnline === "1"} // 如果需要保留
                  >
                    开始训练
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};
export default CreateTrainingView;

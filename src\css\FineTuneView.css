.createModelContent {
  display: flex;
  flex-direction: column;
  left: 50%;
  margin: 0 auto;
  max-width: 1920px;
  padding-top: 2.5rem;
  text-align: start;
  width: 92.1%;
}

.ant-drawer-content-wrapper {
  position: absolute !important;
  top: 64px !important;
}

.scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(131, 129, 129, 0.3);
  border-radius: 10px;
  background-color: #F5F5F5;
}

.scrollbar::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(131, 129, 129, 0.3);
  background-color: #939292;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #707070;
}

.purpleBlue {
  background: linear-gradient(117.27deg, #903EF9 3.96%, #2753C6 36.38%, #088FB5 66.3%, #26B49B 93.73%);
  box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 12px 12px 12px 12px;
}

.otherColor {
  background: linear-gradient(143deg, #D0D428 0%, #54B418 36%, #0D9199 69%, #225EB7 100%);
  box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 12px 12px 12px 12px;
}

.descriptions .ant-descriptions-item {
  padding-bottom: 30px !important;
}

.descriptions .ant-descriptions-item-label {
  min-width: 6.7rem !important;
}

.descriptions {
  max-height: 400px;
  overflow: auto;
  margin: 20px 0 0 0;
}

.descriptions::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.descriptions::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(131, 129, 129, 0.3);
  border-radius: 10px;
  background-color: #F5F5F5;
}

.descriptions::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(131, 129, 129, 0.3);
  background-color: #939292;
}

.descriptions::-webkit-scrollbar-thumb:hover {
  background-color: #707070;
}

@media (min-height: 920px) {
  .descriptions {
    max-height: none;
    /* 取消最大高度限制 */
  }
}

.pedestal-info {
  background: linear-gradient(360deg, #FFFFFF 0%, rgba(255, 255, 255, 0.4) 100%);
  box-shadow: 0px 4px 4px rgba(119, 146, 185, 0.1);
  border-radius: 24px;
  padding: 1.8rem 2.5rem 1.5rem 2.5rem;
  margin: 0.8rem 0 1.25rem 0;
}

.myModelArea {
  background: linear-gradient(360deg, #FFFFFF 0%, rgba(255, 255, 255, 0.4) 100%);
  box-shadow: 0px 4px 4px rgba(119, 146, 185, 0.1);
  border-radius: 24px;
  border-radius: var(--br-5xl);
  padding: 2rem;
  margin: 2rem 0;
  background-color: var(--color-white);
  height: max-content;
  display: flex;
  min-height: 484px;
  flex-direction: column;
}
.pedestal-info .ant-col-6,
.myModelArea .ant-col-6 {
  flex: 0 0 20%;
}

.modelStatus {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-family: 'HarmonyOS Sans SC, HarmonyOS Sans SC';
  font-weight: 400;
  color: #000000;
  -webkit-background-clip: text;
}

.otherStatus {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-family: 'HarmonyOS Sans SC, HarmonyOS Sans SC';
  font-weight: 400;
  color: #000000;
  -webkit-background-clip: text;
}
.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.islive {
  background: radial-gradient(155.12% 149.19% at 7.39% -12.16%, rgba(15, 182, 152, 0.41) 0%, rgba(95, 229, 205, 0.41) 28.69%, rgba(217, 217, 217, 0) 100%);
  box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 12px 12px 12px 12px;
}

.goingLive {
  background: linear-gradient(141deg, rgba(15, 182, 152, 0.41) 0%, rgba(95, 229, 205, 0.41) 29%, rgba(217, 217, 217, 0) 100%);
  box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 12px 12px 12px 12px;
}

.done {
  background: radial-gradient(155.12% 149.19% at 7.39% -12.16%, rgba(142, 152, 167, 0.44) 0%, rgba(217, 217, 217, 1) 28.69%, rgba(217, 217, 217, 0) 100%);
  border-radius: 12px 12px 12px 12px;
}

.training {
  background: linear-gradient(141deg, rgba(251, 191, 120, 0.63) 0%, rgba(251, 191, 120, 0.88) 0%, rgba(251, 226, 164, 0.63) 29%, rgba(217, 217, 217, 0) 100%);
  box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 12px 12px 12px 12px;
}

 /* 训练中和停止训练 使用 training*/
.interrupted {
  background: radial-gradient(155.12% 149.19% at 7.39% -12.16%, #C8CCD2 0%, #D9D9D9 28.69%, rgba(217, 217, 217, 0) 100%);
  box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 12px 12px 12px 12px;
}
 /* 离线和训练失败 使用 interrupted*/

.online {
  background: linear-gradient(198deg, rgba(15, 182, 152, 0.41) 0%, rgba(95, 229, 205, 0.41) 29%, rgba(217, 217, 217, 0) 100%);
  box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 12px 12px 12px 12px;
}
 /* 上线中和在线 使用 online*/

.statusInfo {
  flex: 1;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: end;
}

.actionInfo {
  display: flex;
}

.prolong,
.online,
.Interrupted,
.training1,
.donetext {
  display: flex;
  justify-content: start;
  color: #000000;
  font-size: 13px;
  margin-right: -10px;
  /* position: absolute; */
  /* top: 15px;
  left: -15px; */
}

/* .Interrupted {
  top: 24px;
  left: -15px;
}

.donetext,
.training1 {
  top: 24px;
  left: -20px;
}

.prolong {
  top: 24px;
  left: 50px;
} */

.liveround,
.goliveround {
  margin-right: 5px;
  width: 7px;
  height: 7px;
  background: #4FD7BF;
  border-radius: 50%;
  right: 50px;
  bottom: 8px;
}

.doneround {
  margin-right: 5px;
  width: 7px;
  height: 7px;
  background: #325DF4;
  border-radius: 50%;
  right: 50px;
  bottom: 8px;
}

.traninground {
  margin-right: 5px;
  width: 7px;
  height: 7px;
  background: #FFC60A;
  border-radius: 50%;
  right: 50px;
  bottom: 8px;
}

.interruptedround {
  margin-right: 5px;
  width: 7px;
  height: 7px;
  background: #FF0000;
  border-radius: 50%;
  right: 50px;
  bottom: 8px;
}

.timebox {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  right: 11px;
}

.timebox2 {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  right: 60px;
}

.buttonblock {
  width: 124px;
  height: 40px;
  position: absolute;
  bottom: 34px;
  background: rgb(17, 17, 17);
  border-radius: 59px;
  font-weight: bold;
  color: rgb(255, 255, 255);
}

.buttongay {
  width: 124px;
  height: 40px;
  position: absolute;
  bottom: 34px;
  background: #FFFFFF;
  border-radius: 59px;
  font-weight: bold;
  color: rgb(255, 255, 255);
}

.offmodel {
  height: 145px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: 500;
  color: #111111;
}

.offmodelButton {
  height: 60px;
  display: flex;
  justify-content: space-around;
}

.offmodelCancel {
  width: 124px;
  height: 40px;
  background: #8E98A7;
  border-radius: 59px 59px 59px 59px;
  opacity: 1;
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
}

.offmodeltrue {
  width: 124px;
  height: 40px;
  background: #111111;
  border-radius: 59px 59px 59px 59px;
  opacity: 1;
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
}

.btdStyle.ant-btn {
  padding: 0 !important;
}

.purpleBlue .ant-card-body,
.otherColor .ant-card-body {
  padding: 18px 18px 10px !important;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.islive .ant-card-body,
.goingLive .ant-card-body ,
.done .ant-card-body,
.training .ant-card-body,
.interrupted .ant-card-body {
  padding: 18px 18px 10px!important;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.interrupted .ant-card-body {
  min-height: 131px;
}

.modalTitle {
  display: flex;
  align-items: center;
}

.lightWeightModal {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: 24px 0 49px 0;
}
.origion {
  margin-bottom: 20px;
}

.lightWeightContent {
  width: 50%;
  margin-left: 57px;
}

.introduction {
  color: #000000;
  font-weight: 500;
  height: 70px;
  width: 100%;
  overflow: hidden;
  /* 隐藏超出容器的内容 */
  display: -webkit-box;
  /* 必须配合 */
  -webkit-box-orient: vertical;
  /* 设置块方向为垂直 */
  -webkit-line-clamp: 3;
  /* 显示2行，超出部分省略 */
}

.modelNameTitle,
.baseModelNameTitle {
  height: 30px;
  width: 100%;
  color: #111111;
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 20px;
  overflow: hidden;
  /* 隐藏超出容器的内容 */
  display: -webkit-box;
  /* 必须配合 */
  -webkit-box-orient: vertical;
  /* 设置块方向为垂直 */
  -webkit-line-clamp: 1;
  /* 显示2行，超出部分省略 */
}

.baseModelNameTitle {
  color: #FFFFFF;
}

.baseModelIntroduction {
  color: #FFFFFF;
  height: 66px;
  width: 100%;
  overflow: hidden;
  /* 隐藏超出容器的内容 */
  display: -webkit-box;
  /* 必须配合 */
  -webkit-box-orient: vertical;
  /* 设置块方向为垂直 */
  -webkit-line-clamp: 3;
  /* 显示2行，超出部分省略 */
}
.interrupted .text {
  text-align: center;
  color: #000000;
  font-weight: 600;
}
.interrupted .btn {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}
.interrupted .btn>button{
  background-color: #000000;
  color: #FFFFFF;
  border-radius: 10px;
}
.custom-modal{
  backdrop-filter: blur(8px);
  background: #47768099;
}
.delete-model .ant-modal-content {
  height: 226px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.delete-model .ant-modal-content .ant-modal-body {
  display: flex;
  justify-content: center;
  align-items: center;
}
.delete-model .ant-modal-content .ant-modal-footer {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 100px
}
.modal-btn.ant-btn-default:not(:disabled):not(.ant-btn-disabled):hover {
  background: #111111 !important;
  width: 82px;
  height: 40px;
  border-radius: 59px;
  color: #FFFFFF;
  font-weight: 700;
  border-radius: 59px;
  border: none;
}
.modal-btn.ant-btn-default {
   border: none;
   width: 82px;
   height: 40px;
}
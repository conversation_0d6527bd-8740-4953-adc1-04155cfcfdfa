import React from 'react';
import { Form, Input, Button, Space } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import { FormHeaderProps, CreateTaskFormType } from '../types';
import { CSS_CLASSES, DEFAULT_VALUES, VALIDATION_RULES, MESSAGES } from '../constants';

const FormHeader: React.FC<FormHeaderProps> = ({
  taskName,
  onTaskNameChange,
  onBack,
}) => {
  return (
    <>
      <Space size={20} style={{ display: 'inline-flex', alignItems: 'center' }}>
        <Button
          style={{ fontSize: '12px', width: '36px', height: '36px' }}
          shape="circle"
          icon={<LeftOutlined />}
          onClick={onBack}
        />
        <div
          className={CSS_CLASSES.MEDIUM_TEXT}
          style={{ fontSize: '28px', lineHeight: '36px', fontWeight: '500' }}
        >
          生成训练数据
        </div>
      </Space>

      <Form.Item<CreateTaskFormType>
        name="taskName"
        label="任务名称"
        rules={[
          {
            max: VALIDATION_RULES.TASK_NAME.MAX_LENGTH,
            message: `任务名称不能超过${VALIDATION_RULES.TASK_NAME.MAX_LENGTH}个字符`,
          },
          {
            pattern: VALIDATION_RULES.TASK_NAME.PATTERN,
            message: VALIDATION_RULES.TASK_NAME.MESSAGE,
          },
        ]}
      >
        <Input
          placeholder={MESSAGES.PLACEHOLDER.TASK_NAME}
          value={taskName}
          onChange={(e) => onTaskNameChange(e.target.value)}
          style={{ width: '30rem', height: '2.5rem' }}
        />
      </Form.Item>
    </>
  );
};

export default FormHeader;

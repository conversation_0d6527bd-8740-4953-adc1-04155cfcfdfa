import React from 'react';

interface ImageParserProps {
  text: string;
}

const ImageParser: React.FC<ImageParserProps> = ({ text }) => {
  // 定义正则表达式，匹配 [IMG](url)
  const imgRegex = /\[IMG\]\((.*?)\)/g;

  // 使用正则表达式替换文本中的图片标记为React组件
  const parsedText = text.replace(imgRegex, (match, url) => (
    `${<img key={url} src={url} alt="图片" />}`
  ));

  return <div>{parsedText}</div>;
};

export default ImageParser;

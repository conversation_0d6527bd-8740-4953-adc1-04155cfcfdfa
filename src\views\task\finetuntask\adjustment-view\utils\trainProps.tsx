import {
  maxInfo,
  repeatitionInfo,
  split,
  temperature,
  topk,
  topkInfo,
  topp,
  toppInfo,
} from "../../../../../utils/conts";
export const iteratSetValueMap = (testSetStr: string) => {
  const index = split.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
export const topkValueMap = (testSetStr: string) => {
  const index = topkInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
export const toppValueMap = (testSetStr: string) => {
  const index = toppInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
export const maxLenMap = (testSetStr: string) => {
  const index = maxInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
export const repeatValueMap = (testSetStr: string) => {
  const index = repeatitionInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
export const evaluate = (label: string, input: any) => {
  const mappings: Record<string, Record<any, number>> = {
    temperature: {
      0.5: 0,
      0.7: 25,
      1.0: 50,
      1.3: 75,
      1.5: 100,
    },
    topK: {
      5: 0,
      20: 25,
      50: 50,
      100: 75,
      200: 100,
    },
    topP: {
      0.6: 0,
      0.7: 25,
      0.8: 50,
      0.9: 75,
      0.95: 100,
    },
    maxTokens: {
      50: 0,
      100: 25,
      200: 50,
      300: 75,
      500: 100,
    },
    frequencyPenalty: {
      1.0: 0,
      1.2: 25,
      1.5: 50,
      1.8: 75,
      2.0: 100,
    },
  };
  return mappings[label]?.[input] ?? null;
};

  

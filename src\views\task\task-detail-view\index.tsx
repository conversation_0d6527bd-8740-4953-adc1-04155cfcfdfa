import { CloseCircleOutlined, LeftOutlined } from "@ant-design/icons";
import { useNavigate, useParams } from "react-router-dom";
import {
  Button,
  Checkbox,
  Col,
  Collapse,
  Descriptions,
  DescriptionsProps,
  Dropdown,
  Input,
  List,
  message,
  Modal,
  Row,
  Space,
  Tooltip,
  Tree,
  Avatar,
  TreeSelect,
  Segmented,
  Select,
  theme,
  Tag,
} from "antd";
import type { CollapseProps, MenuProps } from "antd";
import {
  ExclamationCircleFilled,
  SearchOutlined,
  CaretDownOutlined,
  CloseOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import type { DataNode, TreeProps } from "antd/es/tree";
import {
  QADocument,
  FileContent,
  HighlightIdx,
  TaskDetailType,
  DocumentContentPairType,
  TaskStatus,
  DataSetType,
  DataSetTreeType,
} from "../../../types";
import { Key, SetStateAction, useEffect, useRef, useState } from "react";
import { formattedTime } from "../../../utils/formatred";
import { Scrollbar } from "react-scrollbars-custom";
import TablePagination from "../../../components/TablePagination";
import {
  FileTreeNode,
  deleteTask,
  flattenFileTree,
  getQATaskFileTree,
  getReviewConfigInfo,
  getTaskDetail,
  isSetReviewConfigInfo,
  setReviewConfigInfo,
} from "../../../api/task";
import { paragraph, questionDensity } from "../../../utils/conts";
import HighlightedText from "../../../components/HighlightedText";
import infoIcon from "../../assets/img/info-icon.svg";
import q from "../../../assets/img/q.svg";
import a from "../../../assets/img/a.svg";
import warningIcon from "../../../assets/img/warningIcon.svg";
import {
  deleteQA,
  exportQA,
  getAllocateQA,
  getFileContent,
  getQAList,
  getTaglList,
  QaDeleteInfo,
  QaExportParams,
  QAInfoType,
} from "../../../api/qa";
import { saveAs } from "file-saver";
import HighlightText from "../../../components/HighlightText";
import DatasetExportModal from "../../../components/DatasetExportModal";
import { getAllUserByTaskId } from "../../../api/review";
import { UserType } from "../../../api/user";
import { copyToClipboard } from "../../../utils/copytext";
import avatar1 from "../../../assets/img/avatar-1.png";
import CheckProgress from "../../../components/CheckProgress";
import { queryUserInfo } from "../../login/service";
import { ReviewConfigBtnType } from "../../../components/ReviewConfigModal/components/ReviewConfigBtn/type";
import { itemsEqual } from "@dnd-kit/sortable/dist/utilities";
import React from "react";
import EditableTagGroup from "../../../components/EditableTagGroup";
enum QAFilter {
  All,
  Reviewed,
  Unreviewed,
}

const TaskDetailView: React.FC = () => {
  const { task_id } = useParams<string>();
  const navigate = useNavigate();
  if (!task_id) {
    navigate(`/main/task`);
  }
  const [sourceData, setSourceData] = useState<string>("");
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [taskDetail, setTaskDetail] = useState<TaskDetailType>();
  const [qaList, setQaList] = useState<QADocument[]>([]);
  const [fileTree, setFileTree] = useState<any[]>([]);
  const [flattenTree, setFlattenTree] = useState<any[]>([]);
  const [displayQaList, setDisplayQaList] = useState<QADocument[]>([]);
  const [expendList, setExpendList] = useState<number>(-1);

  const [highlightIdxList, setHighlightIdxList] = useState<HighlightIdx[]>([]);
  const [QATags, setQATags] = useState<QADocument[]>([]);
  const [fileContent, setFileContent] = useState<FileContent[]>([]);
  const [fileName, setFileName] = useState<string>();
  const [treeData, setTreeData] = useState<DataNode[]>([]);

  // 点击QA checkbox 选中的QA对象数组
  const [checkedQASet, setCheckedQASet] = useState<QAInfoType[]>([]);

  // 点击选中文件树文件ID数组
  const [checkedFileSet, setCheckedFileSet] = useState<Set<string>>(
    new Set<string>()
  );

  // 选中文件树后，点击QA checkbox 反选的QA对象数组
  const [excludeQASet, setExcludeQASet] = useState<QAInfoType[]>([]);

  // 全局选中的QA ID集合，用于跨页面保持选择状态
  const [globalSelectedQAIds, setGlobalSelectedQAIds] = useState<Set<string>>(new Set());

  // 全局选中的QA详细信息，包含fileId映射
  const [globalSelectedQADetails, setGlobalSelectedQADetails] = useState<Map<string, {fileId: string, id: string}>>(new Map());

  // 当前页面的全选状态
  const [currentPageSelectAll, setCurrentPageSelectAll] = useState(false);
  const [qaFileMap, setQaFileMap] = useState<Map<string, string>>(
    new Map<string, string>()
  );

  //标签选中状态管理
  const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set());
  const [deleteQaDialog, setDeleteQalDialog] = useState(false);
  const [searchText, setSearchText] = useState<string>('');
  const [open, setOpen] = useState(false);
  const { confirm } = Modal;
  const [activeKey, setActiveKey] = useState<string[] | string>("1");
  const [treeCheckKeys, setTreeCheckKeys] = useState<React.Key[]>([]);
  const intervalRef = useRef<NodeJS.Timer>();
  const [apiFlg, setApiFlg] = useState(false);
  const [exportModal, setExportModal] = useState(false);
  const [exportAll, setExportAll] = useState(true);
  const scrollbarRef = useRef<any>();
  const [verifyUsers, setVerifyUser] = useState<
    { label: string; key: string; value: string }[]
  >([]);
  const [reviewOptions, setReviewOptions] = useState<
    { label: string; key: string; value: string }[]
  >([]);

  //用户头像
  const [avatar, setAvatar] = useState(avatar1);
  //弹窗
  const [selectDataSet, setSelectDataSet] = useState(true);
  const [previewModal, setPreviewModal] = useState(false);

  // QA Filter
  const [fileList, setFileList] = useState<string[]>([]);
  const [filterVal, setFilterVal] = useState<string>();
  const [pagination, setPagination] = useState({
    page: 1,
    size: 10,
    total: 0,
  });
  const [reviewFilterType, setReviewFilterType] = useState<any>();
  const [approvedItems, setApprovedItems] = useState<any>();
  const [allocatedUser, setAllocatedUser] = useState<string>();
  const [scoreFilter, setScoreFilter] = useState<string>();
  const [selectText, setSelectText] = useState("关键词检索");
  const [selectReview, setSelectReview] = useState("");
  const [isSetReview, setIsSetReview] = useState<boolean>(false);
  const [filterType, setFilterType] = useState<string>("keyWord");
  const [scoreButtonInfo, setScoreButtonInfo] = useState<ReviewConfigBtnType[]>(
    []
  );
  const [tagList, setTagList] = useState<any[]>([]);
  const [segmented, setSegmented] = useState<string>("文件视图");
  const [searchTagValue, setSearchTagValue] = useState('');
  const DefaultBtnConfig = [
    {
      icon: "DislikeSvg",
      value: "较差",
      color: "#B4B4B4",
    },
    {
      icon: "ChecKSvg",
      value: "一般",
      color: "#FBE2A4",
    },
    {
      icon: "LikeSvg",
      value: "良好",
      color: "#0FB698",
    },
  ];
  const UserName = () => {
    const [name, setName] = useState("");

    useEffect(() => {
      const fetchUserName = async () => {
        const res = await queryUserInfo();
        if (res && res.data) {
          setName(res.data.userName);
        }
      };

      fetchUserName();
    }, []);

    return <b className="namesytle">{name}</b>;
  };

  const taskStatusMap = (taskDetail?: TaskDetailType) => {
    if (taskDetail?.taskStatus === TaskStatus.error) {
      if (taskDetail.complete === taskDetail.total) {
        return <label style={{ color: "orange" }}>任务完成</label>;
      } else if (taskDetail.complete < taskDetail.total) {
        return <label style={{ color: "orange" }}>进行中</label>;
      }
    } else if (taskDetail?.taskStatus === TaskStatus.inProgress) {
      return "进行中";
    } else if (taskDetail?.taskStatus === TaskStatus.success) {
      return "任务完成";
    } else if (taskDetail?.taskStatus === TaskStatus.failed) {
      return <label style={{ color: "red" }}> 任务失败</label>;
    }
  };

  const filterDropdownItems: MenuProps["items"] = [
    {
      key: "reviewed",
      label: "已审核项",
    },
    {
      key: "verifyUser",
      label: "按审核人",
      children: verifyUsers,
    },
    {
      key: "reviewOption",
      label: "按审核选项",
      children: reviewOptions,
    },
    {
      key: "unreviewed",
      label: "未审核项",
    },
  ];
  const filterItems: MenuProps["items"] = [
    {
      key: "keyWord",
      label: "关键词检索",
    },
    {
      key: "reviewedSearch",
      label: "审核检索",
    },
    // {
    //   key: "labelSearch",
    //   label: "标签检索",
    // },
  ];

  const descItems: DescriptionsProps["items"] = [
    {
      key: "1",
      label: "任务ID",
      children: taskDetail?.id ? taskDetail.id : "NULL",
    },

    {
      key: "2",
      label: "创建用户",
      children: (
        <>
          <Avatar className="ellipse-parent29-1" src={avatar} />
          {UserName()}
        </>
      ),
    },

    {
      key: "3",
      label: "段落精细度",
      children: taskDetail?.splitLevel
        ? paragraph[taskDetail?.splitLevel - 1]
        : "自动",
    },

    {
      key: "4",
      label: "任务状态",
      children: (
        <>
          {taskStatusMap(taskDetail)}（
          {taskDetail?.complete + " / " + taskDetail?.total}）
        </>
      ),
    },

    {
      key: "5",
      label: "创建时间",
      children: taskDetail?.createTime
        ? formattedTime(new Date(taskDetail?.createTime))
        : "NULL",
      // children: <>{taskDetail?.createTime}</>,
    },

    {
      key: "6",
      label: "提问密度",
      children: taskDetail?.densityLevel
        ? questionDensity[taskDetail?.densityLevel - 1]
        : "自动",
    },

    {
      key: "7",
      label: "需求描述",
      children: (
        <Tooltip title={taskDetail?.description}>
          <div
            style={{
              maxWidth: 230,
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              overflow: "hidden",
            }}
          >
            {taskDetail?.description}
          </div>
        </Tooltip>
      ),
    },
    {
      key: "8",
      label: "审核进度",
      children:
        taskDetail && taskDetail?.taskStatus !== "FAILED" ? (
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <label>{taskDetail.reviewCount + " / " + taskDetail.qaCount}</label>
            {/* <Tooltip title="为保证导出数据质量，请在导出前进行人工审核">
              <img src={infoIcon} style={{ width: "16px", height: "16px" }} />
            </Tooltip> */}
          </div>
        ) : (
          <label>-</label>
        ),
    },
  ];
  //复制
  const textToCopy = descItems[0].children;
  const textId: string = textToCopy as string;
  const [messageApi, contextHolder] = message.useMessage();

  const info = () => {
    messageApi.info("复制成功");
  };

  const onSelect: TreeProps["onSelect"] = (selectedKeys, info) => {
    console.log(treeData, "treeData");

    console.log("selected", selectedKeys, info);
    if (info.selected) {
      if (!info.node.children || info.node.children.length === 0) {
        // 选中子节点
        const docuList = flattenTree.filter(
          (qa) => qa.fileId === info.node.key
        );
        setFileList(docuList.map((item) => item.fileId));
      } else {
        // 选中父节点
        const children = fileTree.filter(
          (item) => item.fileTreeNodeForTask.fileId === info.node.key
        );
        if (children && children.length > 0)
          setFileList(
            children[0].fileTreeNodeForTask.children.map(
              (item: { fileId: any }) => item.fileId
            )
          );
      }
    }
  };
  function isChecked(fileId: any) {
    return checkedFileSet.has(fileId);
  }
  //   function findFileIds(node: any): any[] {
  //     let fileIds: any[] = [];
  //     if (node.children) {
  //         node.children.forEach((item: { fileId: any; children: any[]; }) => {
  //             fileIds.push(item.fileId);
  //             if (item.children) {
  //                 fileIds = fileIds.concat(findFileIds(item));
  //             }
  //         });
  //     }
  //     return fileIds;
  // }
  function findFileIds(node: any): any[] {
    let fileIds: any[] = [];
    if (node.children) {
      node.children.forEach((item: { fileId: any; children: any[] }) => {
        fileIds.push(item.fileId);
        if (item.children) {
          fileIds = fileIds.concat(findFileIds(item));
        }
      });
    }
    return fileIds;
  }

  const onCheck: TreeProps["onCheck"] = (checkedKeys, info) => {
    console.log(info, "2");

    console.log("checkedKeys", checkedKeys, "treeCheckKeys", treeCheckKeys);
    setTreeCheckKeys(checkedKeys as any);
    if (!info.node.children || info.node.children.length === 0) {
      // 选中子节点
      const node = flattenTree.filter((qa) => qa.fileId === info.node.key);
      // if (isChecked(info.node.key)) {
      if (!info.checked) {
        // 已选中时执行取消选中操作
        setCheckedFileSet(new Set(null));
      } else {
        // 未选中时执行选中操作
        setCheckedFileSet(new Set(node.map((item) => item.fileId)));
        setExcludeQASet([]);
        setCheckedQASet([]);
        if (checkedQASet.length !== 0) {
          setCheckedFileSet(new Set(node.map((item) => item.fileId)));
        }
      }
    } else {
      // 选中父节点
      const children = fileTree.filter(
        (item) => item.fileTreeNodeForTask.fileId === info.node.key
      );
      const childrendata = fileTree.map(
        (item) => item.fileTreeNodeForTask.children[0]
      );
      const child = childrendata.map((item) => item.children);
      if (Array.isArray(checkedKeys)) {
        // 如果checkedKeys是数组类型
        if (checkedKeys.length === 0) {
          // 取消选中操作
          setCheckedFileSet(new Set(null));
          setExcludeQASet([]);
          setCheckedQASet([]);
        } else if (children && children.length > 0) {
          const allFileIds = findFileIds(children[0].fileTreeNodeForTask);
          // setCheckedFileSet(new Set(children[0].fileTreeNodeForTask.children.map((item: { fileId: any; }) => item.fileId)));
          setCheckedFileSet(new Set(allFileIds));
        } else {
          setCheckedFileSet(
            new Set(child[0].map((item: { fileId: any }) => item.fileId))
          );
        }
      }
    }
  };

  const collapseItems: CollapseProps["items"] = [
    {
      key: "1",
      label: (
       <></>
      ),
      showArrow: false,
      children: (
        <>
          <Tree
            // height={100}
            defaultExpandAll
            // selectedKeys={selectedKeys}
            autoExpandParent={true}
            checkedKeys={treeCheckKeys}
            checkable={showCheckbox}
            onSelect={onSelect}
            onCheck={onCheck}
            treeData={treeData}
            fieldNames={{
              title: "name",
              key: "fileId",
              children: "children",
            }}
            className="tree-file"
            titleRender={(nodeData) => {
              const { name, qaCount, reviewedCount } = nodeData as any;
              return (
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    whiteSpace: "nowrap",
                  }}
                >
                  <span
                    style={{
                      flex: 4,
                      whiteSpace: "nowrap",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                      maxWidth: "125px",
                      width: "125px",
                    }}
                  >
                    {name}
                  </span>
                  {/* <label>（{qaCount}）</label> */}
                  <label style={{ color: "#6D7279", flex: 1 }}>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    {"已审核: " + reviewedCount + " / " + qaCount}
                  </label>
                </div>
              );
            }}
          />
        </>
      ),
    },
  ];

  const handleFilter: MenuProps["onClick"] = (e) => {
    if (e.key === "keyWord") {
      setFilterType("keyWord");
      setSelectText("关键词检索");
    } else if (e.key === "reviewedSearch") {
      setFilterType("reviewedSearch");
      setSelectText("审核检索");
    } else if (e.key === "labelSearch") {
      setFilterType("labelSearch");
      setSelectText("标签检索");
    }
  };

  const handleFilterMenuClick: MenuProps["onClick"] = (e) => {
    if (e.key.indexOf("verifyUser_") > -1) {
      const id = e.key.split("verifyUser_")[1];
      console.log(id);
      setAllocatedUser(id);
      setScoreFilter("");
      setReviewFilterType(undefined);
      setApprovedItems(undefined);
      setSelectReview("按审核人");
    } else if (e.key.indexOf("reviewOption_") > -1) {
      const value = e.key.split("reviewOption_")[1];
      console.log("1", value);
      setScoreFilter(value);
      setReviewFilterType(undefined);
      setApprovedItems(undefined);
      setSelectReview("按审核选项");
    } else if (e.key === "unreviewed") {
      setReviewFilterType(false);
      setScoreFilter("");
      setSelectReview("未审核项");
    } else if (e.key === "reviewed") {
      setReviewFilterType(undefined);
      setApprovedItems(true);
      setScoreFilter("");
      setSelectReview("已审核项");
    }
  };
  const initSelect = () => {
    setReviewFilterType(undefined);
    setApprovedItems(undefined);
    setAllocatedUser("");
    setScoreFilter("");
    setSelectReview("");
    getQAs(task_id as string);
  };

  useEffect(() => {
    if (task_id) {
      getQAs(task_id);
    }
  }, [
    pagination.page,
    pagination.size,
    fileList,
    filterVal,
    reviewFilterType,
    allocatedUser,
    scoreFilter,
    selectedTags,
  ]);

  // 新增：监听页面变化，更新当前页面全选状态
  useEffect(() => {
    setCurrentPageSelectAll(isCurrentPageAllSelected());
  }, [displayQaList, globalSelectedQAIds]);

  const QADocumentItem: React.FC<{
    QAItem: QADocument;
    index: number;
  }> = ({ QAItem, index }) => {
    const getCheckState = () => {
      // 优先检查全局选中状态
      if (globalSelectedQAIds.has(QAItem.id)) {
        return true;
      }

      if (
        checkedQASet.some(
          (item) =>
            item.fileId === QAItem.fileId && item.ids.includes(QAItem.id)
        )
      ) {
        return true;
      } else if (checkedFileSet.has(QAItem.fileId)) {
        if (
          excludeQASet.some(
            (item) =>
              item.fileId === QAItem.fileId && item.ids.includes(QAItem.id)
          )
        ) {
          return false;
        }
        return true;
      }
      return false;
    };
    const [expanded, setExpanded] = useState<boolean>(expendList === index);
    const [checked, setChecked] = useState<boolean>(getCheckState());

    const getBorderStyle = (item: QADocument) => {
      let color = "white";
      scoreButtonInfo?.forEach((info) => {
        if (info.value === item.score) {
          color = info.color;
        }
      });
      return `3px solid ${color}`;
    };

    return (
      <List.Item
        key={QAItem.id}
        className={expendList === index ? "task-list-active" : "task-list-item"}
        style={{
          borderLeft: getBorderStyle(QAItem),
        }}
      >
        <div className={"qa-item-container"}>
          {showCheckbox ? (
            <Checkbox
              checked={checked}
              onChange={(e) => {
                const _checked = e.target.checked;
                setChecked(_checked);

                // 更新全局选中状态
                const newGlobalSelected = new Set(globalSelectedQAIds);
                const newGlobalDetails = new Map(globalSelectedQADetails);

                if (_checked) {
                  newGlobalSelected.add(QAItem.id);
                  newGlobalDetails.set(QAItem.id, { fileId: QAItem.fileId, id: QAItem.id });
                } else {
                  newGlobalSelected.delete(QAItem.id);
                  newGlobalDetails.delete(QAItem.id);
                }

                setGlobalSelectedQAIds(newGlobalSelected);
                setGlobalSelectedQADetails(newGlobalDetails);

                if (_checked) {
                  // 选中
                  setTreeCheckKeys((pre) => {
                    if (pre.includes(QAItem.fileId)) {
                      return pre;
                    }
                    return [...pre, QAItem.fileId];
                  });
                  if (checkedFileSet.has(QAItem.fileId)) {
                    // 当前QA所属文件树被选中
                    // 从反选数组里删除当前QA
                    setExcludeQASet((prevExcludeQASet) => {
                      const updatedExcludeQASet = prevExcludeQASet
                        .map((qaInfo) => {
                          if (qaInfo.fileId === QAItem.fileId) {
                            // 找到对应 fileId 的记录
                            const updatedIds = qaInfo.ids.filter(
                              (id) => id !== QAItem.id
                            );

                            if (updatedIds.length === 0) {
                              // 如果删除后 ids 为空，则不包含该记录
                              return null;
                            }

                            // 更新 ids
                            return { ...qaInfo, ids: updatedIds };
                          }

                          return qaInfo;
                        })
                        .filter(Boolean); // 过滤掉为 null 的记录

                      return updatedExcludeQASet as QAInfoType[];
                    });
                  } else {
                    // 当前QA所属文件树未被选中
                    // 将QA数据添加到单选数组
                    setCheckedQASet((pre) => {
                      let newSet = pre;
                      if (pre.some((item) => item.fileId === QAItem.fileId)) {
                        // 当前QA所属文件树有其他单选QA
                        newSet = pre.map((item) => {
                          if (item.fileId === QAItem.fileId) {
                            item.ids.push(QAItem.id);
                          }
                          return item;
                        });
                      } else {
                        // 当前QA所属文件树没有其他单选QA
                        newSet.push({
                          fileId: QAItem.fileId,
                          ids: [QAItem.id],
                        });
                      }
                      return newSet;
                    });
                  }
                } else {
                  // 取消选中
                  if (checkedFileSet.has(QAItem.fileId)) {
                    // 当前QA所属文件树被选中
                    // 当前QA加入反选数组
                    setExcludeQASet((pre) => {
                      let newSet = pre;
                      if (pre.some((item) => item.fileId === QAItem.fileId)) {
                        // 当前QA所属文件树有其他单选QA
                        newSet = pre.map((item) => {
                          if (item.fileId === QAItem.fileId) {
                            item.ids.push(QAItem.id);
                          }
                          return item;
                        });
                      } else {
                        // 当前QA所属文件树没有其他单选QA
                        newSet.push({
                          fileId: QAItem.fileId,
                          ids: [QAItem.id],
                        });
                      }
                      return newSet;
                    });
                  } else {
                    // 如何当前QA所属文件树的所有子节点都未选中，那么取消选择当前文件树
                    const flg = checkedQASet.some(
                      (item) =>
                        item.fileId === QAItem.fileId &&
                        item.ids.filter((id) => id !== QAItem.id).length === 0
                    );
                    if (flg) {
                      setTreeCheckKeys((pre) => {
                        return pre.filter((item) => item !== QAItem.fileId);
                      });
                    }
                    // 当前QA所属文件树未被选中
                    // 将QA从单选数组删除
                    setCheckedQASet((prevCheckedQASet) => {
                      const updatedExcludeQASet = prevCheckedQASet
                        .map((qaInfo) => {
                          if (qaInfo.fileId === QAItem.fileId) {
                            // 找到对应 fileId 的记录
                            const updatedIds = qaInfo.ids.filter(
                              (id) => id !== QAItem.id
                            );

                            if (updatedIds.length === 0) {
                              // 如果删除后 ids 为空，则不包含该记录
                              return null;
                            }

                            // 更新 ids
                            return { ...qaInfo, ids: updatedIds };
                          }

                          return qaInfo;
                        })
                        .filter(Boolean); // 过滤掉为 null 的记录

                      return updatedExcludeQASet as QAInfoType[];
                    });
                  }
                }
              }}
            />
          ) : null}
          <div
            className="qaListItemDiv"
            onClick={() => {
              const file = fileContent.filter(
                (file) => file.id === QAItem.fileContentId
              )[0];
              // setFileName(file.fileName);
              // setSourceData(file.content);
              setHighlightIdxList(QAItem.highlightIdxList);
              // setQATags(QAItem.tags)
              setExpendList((preIndex) => (preIndex === index ? -1 : index));
              setExpanded(true);
              getFileContent(task_id!, QAItem.id).then((res) => {
                if (res.data?.code === 200) {
                  setSourceData(res.data.data.content);
                }
              });
            }}
          >
            <div>
              <div
                style={{ marginBottom: "0.5rem", width: "90%" }}
                className="qa-list-item"
              >
                <img src={q} alt="q" />
                <div className={expanded ? "" : "qa-list-label"}>
                  <HighlightText
                    text={QAItem.question}
                    searchKeyword={filterVal || ""}
                  />
                </div>
              </div>
              <div className="qa-list-item">
                <img src={a} alt="a" />
                <div className={expanded ? "" : "qa-list-label"}>
                  <HighlightText
                    text={QAItem.answer}
                    searchKeyword={filterVal || ""}
                  />
                </div>
              </div>
            </div>
           <div className="qa-tag">
              <EditableTagGroup 
                tags={QAItem.tags} 
                onTagsChange={(newTags) => {
                  setQATags(prevItems => prevItems.map(item => 
                    item.id === QAItem.id ? { ...item, tags: newTags } : item
                  ));
                }} 
              />
            </div>
          </div>
          
        </div>
       
      </List.Item>
    );
  };

  const getQAs = (taskId: string) => {
    const qaParams = {
      taskId,
      page: pagination.page - 1,
      pageSize: pagination.size,
      fileIdList: fileList,
      keyword: filterVal,
      isReview:
        reviewFilterType === undefined
          ? approvedItems === true
            ? true
            : undefined
          : false,
      allocateUserId: allocatedUser,
      score: scoreFilter,
      tags: Array.from(selectedTags)?.join(","),
    };
    getQAList(qaParams).then((res) => {
      if (res.data?.code === 200) {
        setQaList(res.data.data.qaDocumentPage);
        //updateDisplayQaList(res.data.data.qaDocumentPage, filterVal);
        setDisplayQaList(res.data.data.qaDocumentPage);
        setPagination({ ...pagination, total: res.data.data.total });
        // setTagList(res.data.data.tagStatistics);
        console.log("scoreFilter", scoreFilter);
      }
    });
  };

  const onGetTaskDetail = (taskId: string) => {
    if (!apiFlg) {
      setApiFlg(true);
      getTaskDetail(taskId).then((res) => {
        setApiFlg(false);
        if (res.data?.code === 200) {
          const data = res.data.data;
          setTaskDetail(data);
        }
      });

      getQATaskFileTree(taskId).then((res) => {
        if (res.data?.code === 200) {
          const data = res.data.data;
          if (data) {
            // const fileTrees = data.map((item: any, index: number) => {
            //   item.fileTreeNodeForTask.fileId = index.toString();
            //   return item.fileTreeNodeForTask;
            // });
            const fileTrees = data.map((item: any, index: number) => ({
              ...item.fileTreeNodeForTask,
              fileId: item.fileTreeNodeForTask.fileId,
            }));
     
            const flattenTree = flattenFileTree(fileTrees);
            setFileTree(
              data.map((item: any, index: number) => {
                item.fileTreeNodeForTask.fileId = index.toString();
                return item;
              })
            );
            setFlattenTree(flattenTree);
            console.log(flattenTree);
            setTreeData(fileTrees as any[]);
          }
        }
      });
      getTaglList(taskId).then((res) => {
        if (res.data?.code === 200) {
          setTagList(res.data.data.tagS);
        } else {
          message.error(res.data?.message);
        }
      });
      getQAs(taskId);
    }
  };

  const resetCheck = () => {
    setExcludeQASet([]);
    setCheckedFileSet(new Set<string>());
    setCheckedQASet([]);
    setGlobalSelectedQAIds(new Set());
    setGlobalSelectedQADetails(new Map());
    setCurrentPageSelectAll(false);
  };

  // 新增：处理当前页面全选/取消全选
  const handleCurrentPageSelectAll = (checked: boolean) => {
    setCurrentPageSelectAll(checked);

    if (checked) {
      // 全选当前页面的所有QA
      const newGlobalSelected = new Set(globalSelectedQAIds);
      const newGlobalDetails = new Map(globalSelectedQADetails);

      displayQaList.forEach(qa => {
        newGlobalSelected.add(qa.id);
        newGlobalDetails.set(qa.id, { fileId: qa.fileId, id: qa.id });
      });

      setGlobalSelectedQAIds(newGlobalSelected);
      setGlobalSelectedQADetails(newGlobalDetails);
    } else {
      // 取消选择当前页面的所有QA
      const currentPageQAIds = new Set(displayQaList.map(qa => qa.id));
      const newGlobalSelected = new Set(globalSelectedQAIds);
      const newGlobalDetails = new Map(globalSelectedQADetails);

      currentPageQAIds.forEach(id => {
        newGlobalSelected.delete(id);
        newGlobalDetails.delete(id);
      });

      setGlobalSelectedQAIds(newGlobalSelected);
      setGlobalSelectedQADetails(newGlobalDetails);
    }
  };

  // 新增：检查当前页面是否全选
  const isCurrentPageAllSelected = () => {
    if (displayQaList.length === 0) return false;
    return displayQaList.every(qa => globalSelectedQAIds.has(qa.id));
  };

  // 新增：检查当前页面是否部分选中
  const isCurrentPageIndeterminate = () => {
    if (displayQaList.length === 0) return false;
    const selectedCount = displayQaList.filter(qa => globalSelectedQAIds.has(qa.id)).length;
    return selectedCount > 0 && selectedCount < displayQaList.length;
  };

  // 新增：处理标签点击
  const handleTagClick = (tag: string) => {
    const newSelectedTags = new Set(selectedTags);
    if (newSelectedTags.has(tag)) {
      newSelectedTags.delete(tag);
    } else {
      newSelectedTags.add(tag);
    }
    setSelectedTags(newSelectedTags);
  };

  // 新增：递归搜索菜单项
  const filterMenuItems = (items: any[], searchValue: string): any[] => {
    if (!searchValue) return items;

    const filtered: any[] = [];

    items.forEach(item => {
      // 检查当前项是否匹配
      const currentMatches = item.label && item.label.toLowerCase().includes(searchValue.toLowerCase());

      // 如果有子项，递归搜索
      let filteredChildren: any[] = [];
      if (item.children && item.children.length > 0) {
        filteredChildren = filterMenuItems(item.children, searchValue);
      }

      // 如果当前项匹配或有匹配的子项，则包含此项
      if (currentMatches || filteredChildren.length > 0) {
        filtered.push({
          ...item,
          children: filteredChildren.length > 0 ? filteredChildren : item.children
        });
      }
    });

    return filtered;
  };
  const { useToken } = theme;
    const { token } = useToken();

  const contentStyle: React.CSSProperties = {
    backgroundColor: token.colorBgElevated,
    borderRadius: token.borderRadiusLG,
    boxShadow: token.boxShadowSecondary,
  };

  const menuStyle: React.CSSProperties = {
    boxShadow: 'none',
  };

  const getDetail = () => {
    if (task_id) {
      onGetTaskDetail(task_id);
      getAllUserByTaskId(task_id).then((res: any) => {
        if (res?.data?.code === 200) {
          const userList: UserType[] = res.data.data;
          setVerifyUser(
            userList.map((item) => {
              return {
                label: item.userName,
                value: item.id,
                key: `verifyUser_${item.id}`,
              };
            })
          );
        }
      });
      getReviewConfigInfo(task_id).then((res) => {
        if (res?.data?.code === 200 && res.data.data) {
          setReviewOptions(
            res.data?.data?.scoreButtonInfo.map((item: any) => {
              return {
                label: item.value,
                value: item.value,
                key: `reviewOption_${item.value}`,
              };
            })
          );
          setScoreButtonInfo(res.data.data.scoreButtonInfo);
        }
      });
      isSetReviewConfigInfo(task_id).then((res) => {
        if (res?.data?.code === 200) {
          setIsSetReview(!res.data.data);
        }
      });
    }
  };

  useEffect(() => {
    getDetail();

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current as NodeJS.Timeout); // 在组件卸载时清除定时器
    };
  }, []);

  // useEffect(() => {
  //   clearInterval(intervalRef.current as NodeJS.Timeout);
  //   if (taskStatusMap(taskDetail) === "进行中") {
  //     clearInterval(intervalRef.current as NodeJS.Timeout);
  //     intervalRef.current = setInterval(getDetail, 5000);
  //   }
  // }, [taskStatusMap(taskDetail), pagination]);

  function getAllChildren(node: any, result: any[] = []): any[] {
    // 递归遍历所有子节点
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        getAllChildren(child, result);
      }
    } else {
      // 将当前节点添加到结果数组
      result.push(node);
    }
    return result;
  }



  const handleDeleteMenuClick = () => {
    // 构建包含全局选中QA的删除参数
    const globalSelectedQAMap = new Map<string, string[]>();

    Array.from(globalSelectedQADetails.values()).forEach(qaDetail => {
      if (!globalSelectedQAMap.has(qaDetail.fileId)) {
        globalSelectedQAMap.set(qaDetail.fileId, []);
      }
      globalSelectedQAMap.get(qaDetail.fileId)!.push(qaDetail.id);
    });

    const globalSelectedQAList: QAInfoType[] = Array.from(globalSelectedQAMap.entries()).map(([fileId, ids]) => ({
      fileId,
      ids
    }));

    const params: QaDeleteInfo = {
      excludeInfoList: excludeQASet,
      fileIdList: Array.from(checkedFileSet),
      qaDeleteInfoList: [...checkedQASet, ...globalSelectedQAList],
      taskId: task_id!,
    };
    deleteQA(params).then((res: any) => {
      if (res.data.code === 200 && task_id) {
        onGetTaskDetail(task_id);
        resetCheck();
      } else {
        message.error(res.data.message);
      }
    });
  };

  const exportAllQA = () => {
    const params: QaExportParams = {
      taskId: task_id ?? "",
      all: true,
      excludeInfoList: [],
      fileIdList: [],
    };
    exportQA(params).then((res: any) => {
      if (res.data) {
        const jsonData = JSON.stringify(res.data);
        const blob = new Blob([jsonData], { type: "application/json" });
        saveAs(blob, `${taskDetail?.taskName ?? ""}QA数据.json`);
      }
    });
  };

  const exportCheckedQA = () => {
    // 构建包含全局选中QA的导出参数
    if (globalSelectedQAIds.size > 0) {
      // 将全局选中的QA按fileId分组
      const globalSelectedQAMap = new Map<string, string[]>();

      Array.from(globalSelectedQADetails.values()).forEach(qaDetail => {
        if (!globalSelectedQAMap.has(qaDetail.fileId)) {
          globalSelectedQAMap.set(qaDetail.fileId, []);
        }
        globalSelectedQAMap.get(qaDetail.fileId)!.push(qaDetail.id);
      });

      const globalSelectedQAList: QAInfoType[] = Array.from(globalSelectedQAMap.entries()).map(([fileId, ids]) => ({
        fileId,
        ids
      }));

      const params: QaExportParams = {
        taskId: task_id ?? "",
        all: false,
        qaExportInfoList: [...checkedQASet, ...globalSelectedQAList],
        excludeInfoList: excludeQASet,
        fileIdList: checkedFileSet ? Array.from(checkedFileSet) : undefined,
      };
      exportQA(params).then((res: any) => {
        if (res.data) {
          const jsonData = JSON.stringify(res.data);
          const blob = new Blob([jsonData], { type: "application/json" });
          saveAs(blob, `${taskDetail?.taskName ?? ""}QA数据.json`);
        }
      });
    } else {
      // 如果没有全局选中的QA，使用原有的逻辑
      const params: QaExportParams = {
        taskId: task_id ?? "",
        all: false,
        qaExportInfoList: checkedQASet,
        excludeInfoList: excludeQASet,
        fileIdList: checkedFileSet ? Array.from(checkedFileSet) : undefined,
      };
      exportQA(params).then((res: any) => {
        if (res.data) {
          const jsonData = JSON.stringify(res.data);
          const blob = new Blob([jsonData], { type: "application/json" });
          saveAs(blob, `${taskDetail?.taskName ?? ""}QA数据.json`);
        }
      });
    }
  };
  const handleReviewConfig = (id: string) => {
    const params = {
      areviewCriteria:
        "请审核生成的答案内容是否正确，如不正确请对其进行编辑修改",
      qreviewCriteria:
        "请审核所提出的问题是否具有价值，如不具有实际应用价值，应将其删除",
      scoreReviewCriteria:
        "请将生成数据的字符总长度、语言自然度作为主要指标进行综合打分评价",
      taskId: id,
      isStepTwo: true,
      scoreButtonInfoList: DefaultBtnConfig,
    };
    setReviewConfigInfo(params);
    const userId = sessionStorage.getItem("id");
    if (userId) {
      getAllocateQA(id, userId);
    }
  };
  const handleApply = (e: React.MouseEvent<HTMLElement>) => {
    // 阻止默认
    e.preventDefault();
    isSetReviewConfigInfo(task_id as string).then((res) => {
      if (res?.data?.code !== 200 || !res?.data?.data) {
        // 如果创建者审核完了一次，则直接跳转到下一步审核页面

      handleReviewConfig(task_id as string);
      }
    });
    const userId = sessionStorage.getItem("id");
    if (userId && task_id) {
      getAllocateQA(task_id, userId!);
    }
    navigate(`/main/task/review/${task_id}`);
    // 新需求，不需要配置直接开发权限

    // 创建者没有审核完成的话，协作者不能点击人工审核按钮
    // 创建者审核过的话，协作者就能点击人工审核按钮跳转到下一页面
  };
  // const AllocateQA = () => {
  //   const userId = sessionStorage.getItem("id");
  //   if (task_id && userId) {
  //     getAllocateQA(task_id, userId!);
  //   }
  // };

  return (
    <Scrollbar>
      <div className="createTaskContent">
        <Space
          size={20}
          style={{ display: "inline-flex", alignItems: "center" }}
        >
          <Button
            style={{ fontSize: "12px", width: "36px", height: "36px" }}
            shape="circle"
            icon={<LeftOutlined />}
            onClick={() => navigate("/main/task")}
          />
          <div
            className="mediumText"
            style={{ fontSize: "28px", lineHeight: "36px", fontWeight: "500" }}
          >
            {taskDetail?.taskName}
          </div>
        </Space>
        <div className="detailContent">
          <div className="task-detail-info">
            <div className="head_info">
              <div
                style={{
                  width: "100%",
                  textAlign: "start",
                  justifyContent: "space-between",
                  display: "inline-flex",
                  marginBottom: "1rem",
                  // marginTop: "1rem",
                  paddingBottom: "1rem",
                  alignItems: "center",
                  borderBottom: "1px solid #E1EAEF",
                }}
              >
                <label className="mediumText">任务信息</label>
              </div>
              <Descriptions
                // layout="vertical"
                column={1}
                style={{
                  padding: "1rem",
                  width: "100%",
                }}
                size="small"
                items={descItems}
              />
              <Button
                type="link"
                className="checkprogess"
                onClick={() => {
                  setPreviewModal(true);
                  setSelectDataSet(true);
                }}
              >
                查看审核进度
              </Button>
              <CheckProgress
                visible={previewModal}
                OnClose={() => setPreviewModal(false)}
                OnQADonload={() => {
                  if (selectDataSet) {
                    exportAllQA();
                  } else {
                    exportCheckedQA();
                  }
                  setPreviewModal(false);
                }}
              ></CheckProgress>
            </div>
            <div
              className="taskDetailLf"
              style={{
                borderBottom: "1px solid #efefef",
                height: "27rem",
              }}
            >
              <Segmented
                  options={['文件视图', '标签视图']}
                  size="large"
                  onChange={(value) => {
                    setSegmented(value as string);
                  }}
                  className="taskContent-segmented"
              />
              { segmented === '文件视图' && (
                <Collapse
                  ghost={true}
                  bordered={false}
                  expandIconPosition="end"
                  size="small"
                  destroyInactivePanel={true}
                  items={collapseItems}
                  activeKey={activeKey}
                  onChange={(e) => {
                    setActiveKey(e);
                  }}
                  className="taskContent-collapse"
                />
              ) }
              { segmented === '标签视图' && (
                <div style={{ padding: '20px 0' }}>
                  {tagList && tagList.length > 0 ? (
                    <div className="tag-container">
                      {tagList.map((tagItem, index) => {
                        const tagText = `${tagItem.tag}`;
                        const isSelected = selectedTags.has(tagText);

                        return (
                          <div
                            key={index}
                            onClick={() => handleTagClick(tagText)}
                            className={isSelected ? 'tag-selected' : 'tag-item'}
                          >
                            <span>{tagText+`(${tagItem.count})`}</span>
                            {isSelected && (
                              <CheckOutlined
                                style={{
                                  color: '#0FB698',
                                  fontSize: '12px'
                                }}
                              />
                            )}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div style={{
                      textAlign: 'center',
                      color: '#999',
                      padding: '40px 0',
                      fontSize: '14px'
                    }}>
                      暂无标签数据
                    </div>
                  )}
                </div>
              ) }
            </div>
          </div>

          <div className="task-detail-content">
            <Row>
              <Col span={12}>
                <div
                  style={{
                    width: "100%",
                    textAlign: "start",
                    marginBottom: "1rem",
                  }}
                  className="task-title mediumText"
                >
                  生成结果预览
                  <span
                    style={{
                      marginLeft: "3rem",
                      fontSize: "12px",
                      color: "#ccc",
                    }}
                  >
                    预估生成时间: 已完成
                  </span>
                </div>
              </Col>
              <Col span={12}>
                <div
                  style={{
                    width: "100%",
                    textAlign: "start",
                    justifyContent: "flex-end",
                    display: "inline-flex",
                    alignItems: "center",
                  }}
                >
                  <Space size={10}>
                    {!showCheckbox ? (
                      <Button
                        className="default-btn"
                        style={{
                          width: "124px",
                          height: "40px",
                          fontSize: "14px",
                          justifyContent: "center",
                        }}
                        onClick={() => {
                          setShowCheckbox(true);
                          setActiveKey(["", "1"]);
                        }}
                      >
                        多选
                      </Button>
                    ) : checkedFileSet.size === 0 &&
                      checkedQASet.length === 0 &&
                      globalSelectedQAIds.size === 0 ? (
                      <Button
                        className="default-btn"
                        style={{
                          width: "124px",
                          height: "40px",
                          fontSize: "14px",
                          justifyContent: "center",
                        }}
                        onClick={() => {
                          setTreeCheckKeys([]);
                          resetCheck();
                          setTimeout(() => {
                            setShowCheckbox(false);
                          });
                        }}
                      >
                        取消
                      </Button>
                    ) : (
                      <>
                        <Button
                          className="default-btn"
                          style={{
                            width: "124px",
                            height: "40px",
                            fontSize: "14px",
                            justifyContent: "center",
                          }}
                          onClick={() => {
                            // handleDeleteMenuClick();
                            setDeleteQalDialog(true);
                          }}
                        >
                          删除所选项
                        </Button>
                        <Button
                          className="default-btn"
                          style={{
                            width: "124px",
                            height: "40px",
                            fontSize: "14px",
                            justifyContent: "center",
                          }}
                          onClick={() => {
                            setExportAll(false);
                            setExportModal(true);
                          }}
                        >
                          导出所选项
                        </Button>
                        <Button
                          className="default-btn"
                          style={{
                            width: "124px",
                            height: "40px",
                            fontSize: "14px",
                            justifyContent: "center",
                          }}
                          onClick={() => {
                            setTreeCheckKeys([]);
                            resetCheck();
                            setTimeout(() => {
                              setShowCheckbox(false);
                            });
                          }}
                        >
                          取消选择
                        </Button>
                      </>
                    )}
                    {/* <Dropdown
                      menu={{
                        items: filterDropdownItems,
                        onClick: handleFilterMenuClick,
                      }}
                    >
                      <Button
                        className="default-btn"
                        // style={{ width: "124px", height: "40px", fontSize: "14px" }}
                        style={{
                          width: 156,
                          height: "40px",
                          fontSize: "14px",
                          justifyContent: "center",
                        }}
                      >
                        {selectText}
                        {selectText === "筛选" ? (
                          ""
                        ) : (
                          <CloseCircleOutlined onClick={initSelect} />
                        )}
                        <CaretDownOutlined />
                      </Button>
                    </Dropdown> */}

                    <Button
                      className="default-btn"
                      // style={{ width: "124px", height: "40px", fontSize: "14px" }}
                      style={{
                        width: "124px",
                        height: "40px",
                        fontSize: "14px",
                        justifyContent: "center",
                      }}
                      onClick={() => {
                        setExportAll(true);
                        setExportModal(true);
                      }}
                    >
                      导出全部
                      {/* <CaretDownOutlined /> */}
                    </Button>
                    {/* </Dropdown> */}
                    {/* <Tooltip title={isSetReview?"请先返回上一级，确认当前任务审核配置后才能开始人工审核":""}> */}
                    <Button
                      type="primary"
                      className="primary-btn boldText"
                      shape="round"
                      style={{ width: 120 }}
                      onClick={(e) => {
                        handleApply(e);
                        navigate(`/main/task/review/${task_id}`, { state: { fromButton: true } });
                      }}
                      // disabled={isSetReview}
                    >
                      人工审核
                    </Button>
                    {/* </Tooltip> */}
                  </Space>
                </div>
              </Col>
            </Row>
            <Row style={{ borderTop: "1px solid rgb(239, 239, 239)" }}>
              {treeData.length > 0 &&
              ((treeData[0] as any)?.name.endsWith(".csv") ||
                (treeData[0] as any)?.name.endsWith(".json")) ? null : (
                <Col
                  span={10}
                  style={{
                    display: "flex",
                    padding: "10px 12px ",
                    justifyContent: "space-between",
                    alignItems: "center",
                    borderRight: "1px solid rgb(239, 239, 239)",
                  }}
                >
                  <span className="task-title mediumText">原文对照</span>
                  <Button type="text" style={{ color: "#ccc" }}>
                    切换源文件视图
                  </Button>
                </Col>
              )}

              <Col
                span={
                  treeData.length > 0 &&
                  ((treeData[0] as any)?.name.endsWith(".csv") ||
                    (treeData[0] as any)?.name.endsWith(".json"))
                    ? 24
                    : 14
                }
                style={{
                  display: "flex",
                  padding: "10px 0 10px 12px ",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  {showCheckbox && (
                    <Checkbox
                      checked={isCurrentPageAllSelected()}
                      indeterminate={isCurrentPageIndeterminate()}
                      onChange={(e) => handleCurrentPageSelectAll(e.target.checked)}
                    />
                  )}
                  <div className="task-title mediumText">问答对</div>
                </div>
                <div className="filter-container">
                    <Dropdown
                      menu={{
                        items: filterItems,
                        onClick: handleFilter,
                      }}
                    >
                      <Button
                        className="default-btn"
                        // style={{ width: "124px", height: "40px", fontSize: "14px" }}
                        style={{
                          width: 136,
                          height: "40px",
                          fontSize: "14px",
                          justifyContent: "start",
                          position: 'relative',
                        }}
                      >
                        {selectText}
                        <CaretDownOutlined style={{ position: 'absolute', right: 10 }} />
                      </Button>
                    </Dropdown>
                   { filterType === "keyWord" && (
                    <Input
                      size="large"
                      suffix={<SearchOutlined />}
                      className="default-input"
                      placeholder="请输入关键词"
                      style={{ width: 320 }}
                      value={filterVal}
                      onChange={(e) => {
                        setFilterVal(e.target.value);
                      }}
                    />
                   )}
                   {filterType === "labelSearch" && (
                    <Select
                      mode="multiple"
                      style={{ width: 320 }}
                      placeholder="请选择标签"
                      suffixIcon={<SearchOutlined />}
                      allowClear
                      // onChange={handleChange}
                      options={tagList
                        .filter(tagItem => 
                          searchTagValue ? tagItem.tag.toLowerCase().includes(searchTagValue.toLowerCase()) : true
                        )
                        .map((tagItem, index) => {
                          const tagText = `${tagItem.tag}`;
                          return { label: tagText, value: tagText };
                      })}
                      dropdownRender={(menu: any) => (
                        <>
                        <Space style={{ padding: '0 8px 4px' }}>
                            <Input
                              placeholder="请输入"
                              style={{ width: 300 }}
                              size="large"
                              suffix={<SearchOutlined />}
                              allowClear 
                              value={searchTagValue}
                              onChange={(e) => setSearchTagValue(e.target.value)}
                              onKeyDown={(e) => e.stopPropagation()}
                            />
                           
                          </Space>

                          {menu}
   
                        </>
                      )}
                    />)}
                    {filterType === "reviewedSearch" && (
                     <Dropdown
                      open={open}
                      onOpenChange={(flag) => setOpen(flag)}
                      menu={{
                        items: filterMenuItems(filterDropdownItems, searchText),
                        onClick: handleFilterMenuClick,
                      }}
                      overlayClassName="reviewed-dropdown"
                      dropdownRender={(menu) => (
                        <>
                        <div style={contentStyle}>
                          <Space style={{ padding: 8, background:'#fff' }}>
                            <Input
                              placeholder="请输入"
                              style={{ width: 300 }}
                              size="large"
                              prefix={<SearchOutlined />}
                              allowClear
                              value={searchText}
                              onChange={(e) => {setOpen(true);setSearchText(e.target.value);}}
                              onKeyDown={(e) => e.stopPropagation()}
                            />
                          </Space>
                          {React.cloneElement(
                            menu as React.ReactElement<{
                              style: React.CSSProperties;
                            }>,
                            { style: menuStyle },
                          )}</div>

                        </>
                      )}
                    >
                      <Input
                        placeholder="请选择"
                        style={{ width: 300 }}
                        size="large"
                        suffix={<SearchOutlined />}
                        allowClear
                        // readOnly
                        onClick={() => setOpen(true)}
                        value={selectReview}
                        onChange={(e) => {setSelectReview(e.target.value);initSelect();}}
                        onKeyDown={(e) => e.stopPropagation()}
                      />
                    </Dropdown>
                   )}
                </div>
              </Col>
            </Row>

            <Row className="taskDetailContent" style={{ padding: 0 }}>
              {treeData.length > 0 &&
              ((treeData[0] as any)?.name.endsWith(".csv") ||
                (treeData[0] as any)?.name.endsWith(".json")) ? null : (
                <Col span={10} style={{ borderRight: "1px solid #efefef" }}>
                  <div style={{ textAlign: "start" }}>
                    <div
                      className="taskDetailLf1"
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        height: "580px",
                      }}
                    >
                      <label style={{ marginBottom: "1rem" }}>{fileName}</label>
                      <div style={{ flex: 1 }}>
                        <Scrollbar>
                          <div
                            style={{
                              height: "max-content",
                              textAlign: "start",
                              marginTop: "16px",
                              lineHeight: "26px",
                              color: "#6D7279",
                              whiteSpace: "pre-wrap",
                              overflowWrap: "break-word",
                            }}
                          >
                            <HighlightedText
                              text={sourceData}
                              highlights={highlightIdxList}
                            />
                          </div>
                        </Scrollbar>
                      </div>
                    </div>
                  </div>
                </Col>
              )}

              <Col
                span={
                  treeData.length > 0 &&
                  ((treeData[0] as any)?.name.endsWith(".csv") ||
                    (treeData[0] as any)?.name.endsWith(".json"))
                    ? 24
                    : 14
                }
                style={{ height: "596px" }}
              >
                <Scrollbar height={"740px"} ref={scrollbarRef}>
                  <List
                    size="small"
                    bordered={false}
                    dataSource={displayQaList}
                    renderItem={(item, index) => (
                      <QADocumentItem
                        key={"QADocumentItem" + index}
                        QAItem={item}
                        index={index}
                      />
                    )}
                  />
                </Scrollbar>
              </Col>
            </Row>
            <Row>
              {treeData.length > 0 &&
              ((treeData[0] as any)?.name.endsWith(".csv") ||
                (treeData[0] as any)?.name.endsWith(".json")) ? null : (
                <Col span={10}>
                  <div className="taskDetailBottom">
                    溯源置信度 : &nbsp;
                    <span className="span1"> 示例</span>
                    {"≥"}90%&nbsp;&nbsp;&nbsp;&nbsp;
                    <span className="span2"> 示例</span>
                    {"≥"}80%&nbsp;&nbsp;&nbsp;&nbsp;
                    <span className="span3"> 示例</span>
                    {"≥"}70%
                  </div>
                </Col>
              )}

              <Col
                span={
                  treeData.length > 0 &&
                  ((treeData[0] as any)?.name.endsWith(".csv") ||
                    (treeData[0] as any)?.name.endsWith(".json"))
                    ? 24
                    : 14
                }
              >
                <TablePagination
                  total={pagination?.total}
                  pageSize={pagination?.size}
                  page={pagination?.page}
                  OnChange={(page, size) => {
                    const p = { page, size, total: pagination.total };
                    if (p.page !== pagination.page) {
                      setExpendList(-1);
                    }
                    setPagination(p);
                    scrollbarRef.current?.scrollToTop();
                  }}
                />
              </Col>
            </Row>
          </div>
        </div>
      </div>
      <DatasetExportModal
        visible={exportModal}
        OnClose={() => setExportModal(false)}
        OnQADonload={() => {
          if (exportAll) {
            exportAllQA();
          } else {
            exportCheckedQA();
          }
          setExportModal(false);
        }}
      ></DatasetExportModal>
      <Modal
        title={
          <div style={{ display: "flex", alignItems: "center" }}>
          <img src={warningIcon} alt="警告" className="warning" />
            <span style={{ marginLeft: 8 }}>提示</span>
          </div>
        }
        centered
        open={deleteQaDialog}
        closable={false}
        width={520}
        className="delete-model"
        footer={[
          <Button className="modal-btn" onClick={() => setDeleteQalDialog(false)}>取消</Button>,
          <Button className="modal-btn" onClick={() => handleDeleteMenuClick}>
            确定
          </Button>]}
        wrapClassName="custom-modal"

      >
        <p>确定要删除所选数据吗？</p>
      </Modal>
    </Scrollbar>
  );
};

export default TaskDetailView;

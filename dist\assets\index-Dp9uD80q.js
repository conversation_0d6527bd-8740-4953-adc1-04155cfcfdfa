import{r as e,y as a,ao as s,j as t,v as l,f as n,a2 as i,I as r,i as o,ap as d,k as c,t as m,K as u,B as h,a6 as x,an as p,S as g,al as j,af as v,a8 as f,ae as k,am as C,aq as b,x as y,D as w,e as N}from"./react-core-BrQk45h1.js";import{T as I}from"./types-ccCJOgIs.js";import{f as S}from"./formatred-B3vJFs-2.js";import{U as _}from"./UploadErrorModal-Dpa2hJTx.js";import{D as z}from"./DatasetExportModal-C5GWuAi5.js";import{e as D,T as B}from"./empty-logo-5H_PPUCG.js";import{b as T,e as R,s as E,r as L,d as F,f as O,h as $,i as q}from"./task-ClBf-SyM.js";import{a as M,d as P,g as U,b as V}from"./review-5qiVEhSO.js";import{b as K}from"./bxtrash-Dz9B4ICl.js";import{a as A}from"./avatar-1-Bswa0fNs.js";import{a as H,b as W,c as J,d as Z,e as G}from"./avatar-6-Dojl3spU.js";import{g as X}from"./action-DppZlFyC.js";import{R as Q}from"./index-Dp9X2s4-.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";import"./index-y6n7cw2i.js";import"./redux-CXOBDlvD.js";const Y="_tag-item_1pt7m_1",ee="_range-select_1pt7m_21",ae=({taskId:c,defaultSelectUser:m,onChange:u,className:h})=>{const[x,p]=e.useState([]),g=a(),j=s((e=>e.user.userList)),v=["pink","red","yellow","orange","cyan","green","blue","purple","geekblue","magenta","volcano","gold","lime"],[f,k]=e.useState(),C=[A,H,W,J,Z,G];e.useEffect((()=>{0===j.length&&g(X())}),[]),e.useEffect((()=>{m&&p(m.map((e=>e.id)))}),[m]);const b=(e,a)=>{if(!e||0===e.length||a<=0)return-1;return e.split("").reduce(((e,a)=>e+a.charCodeAt(0)),0)%a};return t.jsx(l,{maxTagCount:5,showSearch:!1,mode:"multiple",tagRender:e=>{const{label:a,value:s,closable:l,onClose:r}=e;return t.jsx(d,{className:Y,color:v[b(s,v.length-1)],onMouseDown:e=>{e.preventDefault(),e.stopPropagation()},closable:l,closeIcon:!1,onClose:()=>{r(),P(c,s)},children:t.jsxs(n,{children:[t.jsx(i,{size:24,src:C[b(s,C.length-1)]}),a]})})},value:x,onChange:e=>{if(e.length>x.length){((e,a)=>{const s=new Set(a);return e.filter((e=>!s.has(e)))})(e,x).forEach((e=>{M(c,e)}))}p(e),u(e)},style:{width:"100%"},className:h||ee,options:null==j?void 0:j.map((e=>({label:e.userName,value:e.id}))),optionFilterProp:"label",dropdownRender:e=>t.jsxs(t.Fragment,{children:[t.jsx(r,{bordered:!1,placeholder:"搜索团队成员",value:f,onChange:e=>k(e.target.value),onKeyDown:e=>e.stopPropagation()}),t.jsx(o,{style:{margin:"8px 0"}}),e]}),searchValue:f,optionRender:(e,a)=>t.jsxs(n,{children:[t.jsx(i,{size:24,src:C[b(e.value,C.length-1)]}),e.label]})})},se="_config-modal-container_1om6z_1",te="_default-label_1om6z_15",le="_title-label_1om6z_31",ne="_step-label_1om6z_55",ie="_btn-label_1om6z_71",re="_confirm-modal-footer_1om6z_87",oe="_confirm-btn_1om6z_97",de="_restore-default-btn_1om6z_113",ce="_qreviewCriteria-container_1om6z_121",me="_areviewCriteria-container_1om6z_123",ue="_scoreReviewCriteria-container_1om6z_125",he="_scoreButtonInfo-container_1om6z_127",xe="_modal-range-select_1om6z_135",pe=[{icon:"DislikeSvg",value:"较差",color:"#B4B4B4",edit:!1},{icon:"ChecKSvg",value:"一般",color:"#FBE2A4",edit:!1},{icon:"LikeSvg",value:"良好",color:"#0FB698",edit:!1}],ge=e.forwardRef(((a,s)=>{const{taskData:l,visible:i,OnClose:o}=a,d=c(),[x,p]=m.useMessage(),[g,j]=e.useState("请审核所提出的问题是否具有价值，如不具有实际应用价值，应将其删除"),[v,f]=e.useState("请审核生成的答案内容是否正确，如不正确请对其进行编辑修改"),[k,C]=e.useState("请将生成数据的字符总长度、语言自然度作为主要指标进行综合打分评价"),[b,y]=e.useState(pe),w=e.useRef(null),[N,I]=e.useState([]);e.useEffect((()=>{if(i&&(null==l?void 0:l.taskId)){T(null==l?void 0:l.taskId).then((e=>{200===e.data.code&&e.data.data?R(null==l?void 0:l.taskId).then((e=>{if(200===(null==e?void 0:e.data.code)){const a=e.data.data;j(a.qreviewCriteria),f(a.areviewCriteria),C(a.scoreReviewCriteria),_(a.isStepTwo),y(a.scoreButtonInfo)}})):y(pe)})),U(null==l?void 0:l.taskId).then((e=>{var a;200===(null==(a=null==e?void 0:e.data)?void 0:a.code)&&I(e.data.data)}))}}),[i,l]);const[S,_]=e.useState(!0);return t.jsx(t.Fragment,{children:t.jsx(u,{centered:!0,title:"审核配置",keyboard:!1,maskClosable:!1,styles:{body:{height:"650px"}},width:"870px",open:i,onOk:o,onCancel:o,destroyOnClose:!0,footer:t.jsx("div",{className:re,children:t.jsx(h,{size:"large",type:"primary",shape:"round",className:oe,onClick:()=>{(()=>{if(w.current){const e=w.current.getButtons().map((e=>(delete e.edit,e))),a={areviewCriteria:v,isStepTwo:S,qreviewCriteria:g,scoreButtonInfoList:e,scoreReviewCriteria:k,taskId:(null==l?void 0:l.taskId)||""};E(a).then((e=>{var a;200===(null==(a=e.data)?void 0:a.code)&&o()})),x.success("配置成功");const s=sessionStorage.getItem("id");s&&(null==l?void 0:l.taskId)&&V(null==l?void 0:l.taskId,s),d(`/main/task/review/${null==l?void 0:l.taskId}`)}})()},children:"确认配置"})}),children:t.jsxs("div",{className:se,children:[t.jsx("div",{className:te,children:"在完成可见范围设置前，需要先完成生成数据的审核标准的配置"}),t.jsxs("div",{className:te,children:["任务名称：",null==l?void 0:l.taskName]}),t.jsxs("div",{className:te,children:["可见范围：",t.jsx(ae,{defaultSelectUser:N,className:xe,onChange:e=>{},taskId:(null==l?void 0:l.taskId)||""})]}),t.jsx("div",{className:te,children:"审核配置:"}),t.jsx("div",{className:ne,children:"Step1"}),t.jsxs("div",{className:ce,children:[t.jsxs("div",{className:le,children:["问题审核标准：",t.jsx(h,{className:de,type:"link",onClick:()=>j("请审核所提出的问题是否具有价值，如不具有实际应用价值，应将其删除"),children:"恢复默认"})]}),t.jsx("div",{children:t.jsx(r,{size:"large",value:g,onChange:e=>{j(e.target.value)}})})]}),t.jsxs("div",{className:me,children:[t.jsxs("div",{className:le,children:["答案审核标准：",t.jsx(h,{className:de,type:"link",onClick:()=>f("请审核生成的答案内容是否正确，如不正确请对其进行编辑修改"),children:"恢复默认"})]}),t.jsx("div",{children:t.jsx(r,{size:"large",value:v,onChange:e=>{f(e.target.value)}})})]}),t.jsx("div",{className:ne,children:t.jsx(n,{children:"Step2"})}),t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:ue,children:[t.jsxs("div",{className:le,children:["打分评价标准：",t.jsx(h,{className:de,type:"link",onClick:()=>C("请将生成数据的字符总长度、语言自然度作为主要指标进行综合打分评价"),children:"恢复默认"})]}),t.jsx(r,{size:"large",value:k,onChange:e=>{C(e.target.value)}})]}),t.jsxs("div",{className:he,children:[t.jsxs("div",{className:le,children:[t.jsxs("div",{className:le,children:["打分评价按键：",t.jsx("label",{className:ie,children:"请注意，审核一旦开始，不能修改打分选项"})]}),t.jsx(h,{type:"link",className:de,onClick:()=>y(JSON.parse(JSON.stringify(pe))),children:"恢复默认"})]}),t.jsx(Q,{ref:w,DefaultBtnConfig:b})]})]})]})})})})),je=a=>{const{taskInfo:s,renameList:l,setRenameList:n,getTasksList:i}=a,[o,d]=e.useState(s.taskName);return t.jsxs(t.Fragment,{children:[t.jsx(r,{placeholder:"请输入任务名称",value:o,onChange:e=>{d(e.target.value)}}),t.jsx(h,{icon:t.jsx(x,{}),onClick:()=>{const e=o.trim();if(e&&e.length>0){const a=s;a.taskName=e;const t=l;t.delete(a.taskId),n(t),d(""),L(a.taskId,e).then((()=>{i()}))}else m.error("任务名称不合法")}}),t.jsx(h,{icon:t.jsx(p,{}),onClick:()=>{const e=l;e.delete(s.taskId),n(e),d("")}})]})},ve=[{key:"rename",label:"重命名"},{key:"delete",label:"删除"}],fe=[{icon:"DislikeSvg",value:"较差",color:"#B4B4B4"},{icon:"ChecKSvg",value:"一般",color:"#FBE2A4"},{icon:"LikeSvg",value:"良好",color:"#0FB698"}],ke=()=>{const{confirm:s}=u,[i,o]=e.useState([]),[d,x]=e.useState(!1),[p,T]=e.useState(!1),[R,L]=e.useState(!1),[M,P]=e.useState(),[U,A]=e.useState([]),[H,W]=e.useState("taskName"),[J,Z]=e.useState("createTime"),[G,X]=e.useState("desc"),[Q,Y]=e.useState([]),[ee,se]=e.useState(),[te,le]=e.useState(),[ne,ie]=e.useState([]),re=c();a();const[oe,de]=e.useState(!1),[ce,me]=e.useState(new Set),ue=e.useRef();e.useRef();const[he,xe]=e.useState([]),[pe,ke]=e.useState({page:1,size:10,total:0}),[Ce,be]=e.useState(0),ye={onChange:(e,a)=>{Y(a)},getCheckboxProps:e=>({name:e.taskName})},we=(e,a)=>{e.preventDefault(),null===a.reviewConfigId&&(e=>{E({areviewCriteria:"请审核生成的答案内容是否正确，如不正确请对其进行编辑修改",qreviewCriteria:"请审核所提出的问题是否具有价值，如不具有实际应用价值，应将其删除",scoreReviewCriteria:"请将生成数据的字符总长度、语言自然度作为主要指标进行综合打分评价",taskId:e,isStepTwo:!0,scoreButtonInfoList:fe});const a=sessionStorage.getItem("id");a&&V(e,a)})(null==a?void 0:a.taskId);const s=sessionStorage.getItem("id");s&&(null==a?void 0:a.taskId)&&V(null==a?void 0:a.taskId,s),re(`/main/task/review/${null==a?void 0:a.taskId}`)},Ne=[{title:"序号",dataIndex:"index",render:(e,a,s)=>t.jsx(t.Fragment,{children:Ce+s+1}),width:"4.6%"},{title:"任务名称",dataIndex:"name",shouldCellUpdate:(e,a)=>!0,render:(e,a)=>{const{taskName:s,status:l,taskId:i}=a;return ce.has(i)?t.jsx(n.Compact,{children:t.jsx(je,{taskInfo:a,renameList:ce,setRenameList:me,getTasksList:Ie})}):t.jsx("a",{className:"dataset-name",children:s.replace(/"/g,"")})},width:"13.2%"},{title:"创建时间",dataIndex:"createdate",width:"9.6%",render:(e,{createTime:a})=>S(new Date(a))},{title:()=>i.some((e=>e.failedReason))?t.jsxs(n,{children:["任务状态",t.jsx(y,{color:"#E75252"})]}):"任务状态",dataIndex:"status",render:(e,{taskId:a,status:s,failedReason:l,complete:i,total:r})=>{if(s===I.error){if(i&&i===r)return t.jsxs(n,{children:[t.jsxs("label",{className:"warning-info",children:["任务完成",`(${i}/${r})`]}),t.jsx(b,{style:{color:"#E75252",cursor:"pointer"},onClick:()=>{$(a).then((e=>{var a;200===(null==(a=e.data)?void 0:a.code)&&(ie(e.data.data),x(!0))}))}})]});if(i&&i<r)return t.jsxs("label",{className:"warning-info",children:["进行中",`(${i}/${r})`]})}else{if(s===I.failed)return t.jsx("label",{className:"error-info",children:" 任务失败"});if(s===I.inProgress)return t.jsxs("label",{children:["进行中",`(${i}/${r})`]});if(s===I.success&&i&&i===r)return t.jsxs("label",{className:"default-info",children:["任务完成",`(${i}/${r})`]})}},width:"10.4%"},{title:"创建人",dataIndex:"creator",render:(e,{reviewCount:a,qaCount:s,creator:l})=>t.jsx("label",{children:l.userName}),width:"8%"},{title:"可见成员",dataIndex:"visiblerange",render:(e,{taskId:a,reviewers:s})=>t.jsx(ae,{className:"visableselect",defaultSelectUser:s,onChange:e=>{},taskId:a}),width:"15%"},{title:"来源",dataIndex:"source",render:(e,{description:a})=>a&&"本地上传"===a?t.jsx("label",{children:a}):t.jsx("label",{children:"训练数据"}),width:"8%"},{title:"数据审核进度",dataIndex:"dataprocess",render:(e,{reviewCount:a,qaCount:s})=>s||0===s?t.jsx("label",{children:a+" / "+s}):t.jsx("label",{children:"-"}),width:"9.3%"},{title:"操作",dataIndex:"task",render:(e,a,l)=>{const{status:i,reviewCount:r,taskId:d,complete:c,total:u,buttonDisabled:p}=a;let g=t.jsx(t.Fragment,{});return i===I.inProgress||i===I.success?g=c&&c===u||c&&c<u||0===c?t.jsx(h,{type:"link",className:"grid-link-btn",onClick:()=>{re(`/main/task/detail/${d}`)},children:"详情"}):t.jsx("span",{style:{color:"#0fb698"},children:" ___"}):i===I.failed||i===I.error||i===I.error?g=c&&c===u||c&&c<u?t.jsx(h,{type:"link",className:"grid-link-btn",onClick:()=>{$(d).then((e=>{var a;200===(null==(a=e.data)?void 0:a.code)&&(ie(e.data.data),x(!0))}))},children:"问题详情"}):t.jsx(h,{type:"link",className:"error-info grid-link-btn",onClick:()=>{q(d).then((e=>{var a;200===(null==(a=e.data)?void 0:a.code)?m.info(e.data.msg):m.error(e.data.msg)}))},children:"重新解析"}):i===I.inProgress&&(g=t.jsx(t.Fragment,{})),t.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[g,i===I.success?t.jsx(h,{type:"link",className:"grid-link-btn",onClick:e=>{P(a),we(e,a)},children:"人工审核"}):g=t.jsx("span",{style:{color:"#0fb698"},children:" ___"}),t.jsx(w,{menu:{items:ve,onClick:e=>{if("rename"===e.key){const e=ce;e.add(d),me(e)}else if("delete"===e.key){const e=s({centered:!0,title:"删除提示",icon:t.jsx(v,{}),width:540,content:t.jsx(t.Fragment,{children:t.jsx("div",{className:"default-info",style:{color:"black"},children:"确定要删除所选任务吗？"})}),footer:[t.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",padding:"2rem 0 0 0",gap:"8px"},children:[t.jsx(h,{type:"text",onClick:()=>e.destroy(),shape:"round",children:"取消"}),t.jsx(h,{type:"primary",onClick:()=>{o((e=>e.filter((e=>e.taskId!==d)))),F([d]).then((e=>{200===e.data.code?Ie():m.error(e.data.message)})),e.destroy()},shape:"round",className:"primary-btn",style:{width:"120px"},children:"确认删除"})]})],onOk(){o((e=>e.filter((e=>e.taskId!==d)))),F([d]).then((e=>{200===e.data.code?Ie():m.error(e.data.message)})),e.destroy()},onCancel(){e.destroy()}})}}},children:t.jsx("a",{onClick:e=>e.preventDefault(),children:t.jsxs(n,{children:["更多",t.jsx(N,{})]})})})]})},width:"20.8%"}];function Ie(){if(!oe){de(!0);const e={...pe,sortAttribute:J,sortDirection:G,taskName:ee||""};O(e).then((e=>{var a;if(de(!1),200===(null==(a=null==e?void 0:e.data)?void 0:a.code)){const a=sessionStorage.getItem("name"),s=e.data.data.map((e=>{let s=!1;return a!==e.creator.userName&&(s=null===e.reviewConfigId),{...e,buttonDisabled:s}}));o(s),e.data.data.length>0&&(ke({...pe,total:e.data.totalCount}),be((pe.page-1)*pe.size))}}))}}return e.useEffect((()=>{Ie()}),[]),e.useEffect((()=>(clearInterval(ue.current),Ie(),ue.current=setInterval(Ie,5e3),()=>{ue.current&&clearInterval(ue.current)})),[pe.page,pe.size,G,H,J,ee]),e.useEffect((()=>{const e=setTimeout((()=>{se(te)}),500);return()=>clearTimeout(e)}),[te]),t.jsx(g,{children:t.jsxs("div",{className:"createTaskContent",children:[t.jsx("div",{style:{fontSize:"28px",lineHeight:"36px",fontWeight:"500",marginLeft:"2rem"},className:"mediumText",children:"训练数据"}),t.jsxs("div",{className:"createTaskArea",children:[t.jsxs("div",{style:{textAlign:"start",justifyContent:"space-between",display:"inline-flex",marginBottom:"1rem",width:"100%"},children:[t.jsx("div",{children:t.jsxs(n,{size:"small",children:[t.jsxs(n.Compact,{children:[t.jsx(l,{size:"large",className:"filter-select",value:H,onChange:e=>W(e),options:[{value:"taskName",label:"任务名"}]}),t.jsx(r,{size:"large",className:"filter-input",suffix:t.jsx(j,{}),placeholder:"请输入任务名称",value:te,onChange:e=>{le(e.target.value)},onKeyUp:e=>{("Enter"===e.key||0===(null==te?void 0:te.trim().length))&&se(te)}})]}),t.jsx(l,{size:"large",className:"filter-select",value:G,onChange:e=>X(e),style:{width:"9.75rem"},options:[{value:"desc",label:"按时间倒序"},{value:"asc",label:"按时间正序"}]})]})}),t.jsxs(n,{children:[0===Q.length?null:t.jsxs(h,{size:"large",className:"upload-dataset-btn",onClick:()=>{const e=s({centered:!0,title:"删除提示",icon:t.jsx(v,{}),width:540,content:t.jsx(t.Fragment,{children:t.jsx("div",{className:"default-info",style:{color:"black"},children:"确定要删除所选任务吗？"})}),footer:[t.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",padding:"2rem 0 0 0",gap:"8px"},children:[t.jsx(h,{type:"text",onClick:()=>e.destroy(),shape:"round",children:"取消"}),t.jsx(h,{type:"primary",onClick:()=>{const a=Q.map((e=>e.taskId));o((e=>e.filter((e=>!a.includes(e.taskId))))),F(a).then((e=>{200===e.data.code?Ie():m.error(e.data.message)})),e.destroy()},shape:"round",className:"primary-btn",style:{width:"120px"},children:"确认删除"})]})],onOk(){const a=Q.map((e=>e.taskId));o((e=>e.filter((e=>!a.includes(e.taskId))))),F(Q.map((e=>e.taskId))).then((e=>{200===e.data.code?Ie():m.error(e.data.message)})),e.destroy()},onCancel(){e.destroy()}})},style:{display:"flex",alignItems:"center",gap:12},children:[t.jsx("img",{className:"btn-icon",src:K,alt:"删除任务"}),"删除任务"]}),0===Q.length?null:t.jsxs(h,{size:"large",className:"upload-dataset-btn",onClick:()=>{A(Q),T(!0)},style:{display:"flex",alignItems:"center",gap:12},children:[t.jsx("img",{className:"btn-icon",src:"data:image/svg+xml,%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M14%204L11%202V3.5L11%204.5L11%206L14%204Z'%20fill='black'/%3e%3cpath%20d='M5%2010V6C5%204.89543%205.89543%204%207%204H12'%20stroke='black'%20stroke-width='1.33'/%3e%3cpath%20d='M13.3333%2011.9997H2.66668V9.33301H1.33334V11.9997C1.33334%2012.735%201.93134%2013.333%202.66668%2013.333H13.3333C14.0687%2013.333%2014.6667%2012.735%2014.6667%2011.9997V9.33301H13.3333V11.9997Z'%20fill='black'/%3e%3c/svg%3e",alt:"导出结果"}),"导出结果"]}),t.jsx(h,{size:"large",icon:t.jsx(f,{}),style:{backgroundColor:"#EEF1F5",fontSize:"14px",fontWeight:"500",width:"154px",height:"40px",marginRight:"10px"},onClick:()=>{re("/main/task/uploadDataset")},children:"上传本地数据"}),t.jsx(h,{type:"primary",size:"large",shape:"round",style:{backgroundColor:"black",fontSize:"14px",fontWeight:"700",width:"122px"},className:"boldText",onClick:()=>{re("/main/task/create")},children:"生成训练数据"})]})]}),t.jsx(k,{locale:{emptyText:t.jsx(C,{image:D,description:t.jsx("span",{className:"dataset-table-empty-label",children:"空空如也，去上传本地文件吧~"})})},tableLayout:"fixed",rowKey:"taskId",className:"dataset-table",rowSelection:{type:"checkbox",...ye},columns:Ne,dataSource:i,pagination:!1}),t.jsx(B,{total:pe.total,pageSize:pe.size,page:pe.page,OnChange:(e,a)=>{ke({total:pe.total,page:e,size:a})}})]}),t.jsx(_,{rowData:ne,visible:d,OnClose:e=>{x(!1)}}),t.jsx(z,{visible:p,OnClose:()=>T(!1),exportTaskData:U}),t.jsx(ge,{visible:R,OnClose:()=>L(!1),taskData:M})]})})};export{ke as default};

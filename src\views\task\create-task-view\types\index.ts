import { UploadFile } from 'antd';
import { DataSetStatus } from '@/types';

// 创建任务表单类型
export interface CreateTaskFormType {
  taskName: string;
  fileList: string[];
  splitLevel: number; // 对应prd中的6个句子分组
  questionDensity: number; // 对应prd中的11~20个问题/组
  description: string;
  domain: string;
}

// 模板类型
export interface TemplateType {
  id: number;
  templateName: string;
  roleBackground: string;
  detailedDescription: string;
}

// 数据集文件类型
export interface DatasetFile extends UploadFile {
  parseState?: string; // 解析状态
  parseProcess?: number; // 解析进度
  tags?: string[];
  name: string;
  dataSetId: string;
  dataSource?: number; // 0-平台库/1-用户上传
}

// 模型配置类型
export type ModelConfigType = '自动配置' | '手动配置';

// 模型配置组件Props
export interface ModelConfigProps {
  modelConfig: ModelConfigType;
  splitLevel: number;
  questionDensity: number;
  onModelConfigChange: (config: ModelConfigType) => void;
  onSplitLevelChange: (level: number) => void;
  onQuestionDensityChange: (density: number) => void;
}

// 模板选择组件Props
export interface TemplateSelectionProps {
  templates: TemplateType[];
  selectedIndex: number;
  textValue: string;
  onTemplateSelect: (index: number) => void;
  onTemplateApply: (template: TemplateType, index: number) => void;
  onTextChange: (text: string) => void;
}

// 标签管理组件Props
export interface LabelManagementProps {
  autoLabel: boolean;
  labelText: string;
  isGeneratingLabels: boolean;
  fileList: DatasetFile[];
  onAutoLabelChange: (checked: boolean) => void;
  onLabelTextChange: (text: string) => void;
  onAIGenerate: () => void;
}

// 文件上传组件Props
export interface FileUploadProps {
  fileList: DatasetFile[];
  onFileListChange: (files: DatasetFile[]) => void;
  onShowDataModal: () => void;
  onAddTag: (dataSetId: string, tags: string[]) => void;
  onDelFile: (index: number) => void;
  onReUpload: (index: number) => void;
  datasetModalRef: React.RefObject<any>;
}

// 表单头部组件Props
export interface FormHeaderProps {
  taskName: string;
  onTaskNameChange: (name: string) => void;
  onBack: () => void;
}

// 主组件状态类型
export interface CreateTaskState {
  taskName: string;
  modelConfig: ModelConfigType;
  fileList: DatasetFile[];
  showDataModal: boolean;
  uploadErrorModal: boolean;
  textValue: string;
  selectedIndex: number;
  textValueTemplate: TemplateType;
  autoLabel: boolean;
  labelText: string;
  isGeneratingLabels: boolean;
  AILabels: string[];
  exampleText: TemplateType[];
  currentExample: number;
  scrolling: boolean;
  splitLevel: number;
  questionDensityValue: number;
}

// 任务配置映射类型
export interface TaskConfigMap {
  splitLevel: number;
  questionDensity: number;
}

// 创建任务参数类型
export interface CreateTaskParams {
  taskName: string;
  datasetList: string[];
  taskConfigMap: TaskConfigMap;
  description: string;
  domain: string;
  tags: string[];
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message?: string;
  data?: T;
}

// 标签验证结果类型
export interface LabelValidationResult {
  isValid: boolean;
  message?: string;
}

// 滑块标记类型
export interface SliderMarkConfig {
  value: number;
  label: string;
  tooltip: string;
}

import React, { Children, ReactNode, useState } from 'react';
import { But<PERSON>, Drawer } from 'antd';

interface OpenDrawPop {
  rowData?: any[];
  visible: boolean;
  OnClose: (rows?: any[]) => void;
  OnChange?: () => void;
  titltext: string;
  buttontext: string;
  buttontext1?: string;
  moduletext?: string;
  interruptText?: string;
  isInterruptText?: boolean;
  logo?: boolean;
  children: React.ReactNode;
  text?: boolean;
  isbutton?: boolean;
  onButtonClick: () => void;
  onButtonClick2?: () => void
}

const OpenDraw: React.FC<OpenDrawPop> = ({
  rowData,
  visible,
  OnClose,
  OnChange,
  buttontext,
  buttontext1,
  moduletext,
  interruptText,
  titltext,
  children,
  logo,
  text,
  isInterruptText,
  isbutton,
  onButtonClick,
  onButtonClick2
}) => {
  const onOk = () => {
    OnClose();
  };
  const onCancel = () => {
    OnClose();
  };

  const drawerContent = (
    <div style={{ display: "flex", justifyContent: "center", alignItems: "center", fontSize: "22px" }}>
      <div style={{ marginLeft: "10px", marginRight: "auto" }}>{titltext}</div>
      <span>
        {logo && <img src={logo ? require('../assets/img/logoX.png') : ''} alt="Logo" />}
      </span>
      {text && <div className='moduletext'>选择基于 {moduletext} 的大模型进行微调训练</div>}
      {isInterruptText && <div className='moduletextone'> {interruptText}</div>}
      <div style={{ display: "flex", justifyContent: "space-evenly", width: "100%", bottom: "34px", position: "absolute" }}>
        {
          isbutton && <Button
            style={{
              width: "160px",
              height: "40px",
              background: "#787D85",
              borderRadius: "59px 59px 59px 59px",
              fontWeight: "bold",
              color: "#FFFFFF"
            }}
            onClick={onButtonClick2}
          >
            {buttontext1}
          </Button>}

        <Button
          style={{
            width: "160px",
            height: "40px",

            background: "#111111",
            borderRadius: "59px 59px 59px 59px",
            fontWeight: "bold",
            color: "#FFFFFF"
          }}
          onClick={onButtonClick}
        >
          {buttontext}
        </Button>
      </div>
    </div>
  );

  return (
    <Drawer
      width={"35%"}
      closable={false}
      open={visible}
      onClose={onOk}
    >
      {drawerContent}
      {children}

    </Drawer>
  );
}
export default OpenDraw;
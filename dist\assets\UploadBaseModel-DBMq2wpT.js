import{k as e,p as s,r as a,j as l,f as r,B as t,l as i,I as o,v as m,t as d}from"./react-core-BrQk45h1.js";/* empty css                        */import{q as n}from"./server-YuXc7UOr.js";import{q as c,u as p}from"./modle-Dr2iwiy1.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const h=()=>{const h=e(),[j]=s.useForm(),[u,x]=a.useState([]),[y,g]=a.useState([]);a.useEffect((()=>{(async()=>{try{const[e,s]=await Promise.all([n(),c()]);x(e.map((e=>({label:e.serverName,value:e.id.toString()})))),g(s.map((e=>({label:e.modelName,value:e.modelName}))))}catch(e){}})()}),[]);return l.jsxs("div",{className:"uploadBaseModel",children:[l.jsxs(r,{size:20,style:{display:"inline-flex",alignItems:"center"},children:[l.jsx(t,{shape:"circle",icon:l.jsx(i,{}),onClick:()=>h(-1),style:{fontSize:"12px",width:"36px",height:"36px"}}),l.jsx("div",{className:"mediumText",style:{fontSize:"20px",lineHeight:"36px",fontWeight:"500"},children:"载入基座模型"})]}),l.jsx("div",{className:"uploadModelArea",children:l.jsxs(s,{form:j,onFinish:async()=>{try{const e=await j.validateFields(),s={...e,serverId:Number(e.serverId)},a=await p(s);200===a.code?(d.success("上传成功"),h(-1)):d.error(a.message)}catch(e){d.error("提交失败，请检查表单")}},labelCol:{span:6},wrapperCol:{span:18,offset:2},children:[l.jsx(s.Item,{name:"customModelName",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:l.jsx(o,{placeholder:"请输入模型名称",size:"large",style:{width:511}})}),l.jsx(s.Item,{name:"serverId",label:"服务器名称",rules:[{required:!0,message:"请选择服务器名称"}],children:l.jsx(m,{placeholder:"请选择服务器",options:u,size:"large",style:{width:511}})}),l.jsx(s.Item,{name:"modelVersion",label:"模型版本",children:l.jsx(o,{placeholder:"请输入模型版本",size:"large",style:{width:511}})}),l.jsx(s.Item,{name:"parameter",label:"模型参数量",children:l.jsx(o,{placeholder:"请输入模型参数量",size:"large",style:{width:511}})}),l.jsx(s.Item,{name:"manufacturer",label:"模型厂家",children:l.jsx(o,{placeholder:"请输入模型厂家",size:"large",style:{width:511}})}),l.jsx(s.Item,{name:"introduction",label:"模型描述",children:l.jsx(o.TextArea,{rows:4,placeholder:"请输入模型描述",size:"large",style:{width:511}})}),l.jsxs(s.Item,{wrapperCol:{offset:8},style:{marginTop:40},children:[l.jsx(t,{shape:"round",className:"cancalBut",size:"large",onClick:()=>h(-1),style:{marginRight:60},children:"取消"}),l.jsx(t,{shape:"round",type:"primary",htmlType:"submit",className:"submBut",size:"large",style:{marginLeft:60},children:"确认上传"})]})]})})]})};export{h as default};

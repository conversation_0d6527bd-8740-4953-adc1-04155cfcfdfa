import classes from "./index.module.css";
import {
  Button,
  Flex,
  Form,
  Input,
  Radio,
  RadioChangeEvent,
  Select,
  Space,
} from "antd";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import TextArea from "antd/es/input/TextArea";
import { ModelSelectType, TariningType, ModelFailData } from "../../type";
import { GetModlesParams, getModelSets } from "../../../../../api/modle";
import { ModelSetType } from "../../../../../types";

interface ModelSelectInfoPopr {
  modelData: TariningType;
}

interface ModelFormData {
  modelSelect: string;
  modelName: string;
  modelIntroduce: string;
}
interface MyObject {
  modelId: number;
  modelConfigData: ModelFailData;
}
const ModelSelect = forwardRef((prop: ModelSelectInfoPopr, ref) => {
  const {
    dataset,
    modelName: name,
    modelIntro: description,
    modelId: id,
  } = prop.modelData;

  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    getModelSelectData: () => {
      
      return {
        Name: form.getFieldValue("modelName"),
        Description: form.getFieldValue("modelIntroduce"),
        Dataset: form.getFieldValue("modelSelect"),
        // ModelType: value,
        Id: getIdByName(form.getFieldValue("modelSelect")),
      };
    },
    validateFields: () => {
      form.validateFields();
    },
  }));

  const [modelSelect, setModelSelect] = useState(dataset ? dataset : "");
  const [modelSelectId, setModelSelectId] = useState(id ? id : "");
  const [modelName, setModelName] = useState(name ? name : "");
  const [modelIntroduce, setModelIntronduce] = useState(
    description ? description : ""
  );
  const [modelDatasets, setModelDatasets] = useState<ModelSetType[]>([]);
  const training: string = localStorage.getItem("trainingData") || "{}";
  const objectArray: MyObject[] = JSON.parse(training) as MyObject[];
  const [modelFailProp, setModelFailProp] = useState<ModelFailData>(objectArray[0]?.modelConfigData);
  const intervalRef = useRef<NodeJS.Timer>();


  // 定义分页数据
  const [pagination, setPagination] = useState({
    page: 1,
    size: 20,
    total: 0,
  });

  // 模型详情弹窗传入的值

  // const modelSelect = location.state ? location.state.modelConfigData.properties.modelId : "";
  const getNameById = (id: string) => {
    const item = modelDatasets.find((item) => item.id === id);
    return item ? item.modelName : "";
  };
// 通过name获取id
  const getIdByName = (name: string) => {
    const item = modelDatasets.find((item) => item.modelName === name);
    return item ? item.id : "";
  };
  const modelId = modelFailProp ? modelFailProp.properties.modelId : "";

  const OPTIONS = modelDatasets.map((item) => ({
    value: item.modelName,
    label: item.modelName,
    id: item.id,
   
  }));

  function getBaseModels() {
    const params: GetModlesParams = {
      ...pagination,
      status: "",
      modelName: "",
      category: 0,
    };
    
    getModelSets(params).then((res) => {

      if (res.data?.code === 200) {
        setModelDatasets(res.data?.data);
        const dataList = res.data?.data;
       const filteredData = dataList.filter((item) => modelId === item.id);
        form.setFieldValue("modelSelect", filteredData[0]?filteredData[0].modelName:dataset);
      }
    });
  }

  useEffect(() => {
    getBaseModels();
  }, []);


  
  return (
    <>
      <div className={classes["training-content"]}>
        <div className={classes["step1"]} style={{ width: "15%" }}>
          {/* Step 1 */}
          模型选择
        </div>
        <div className={classes["operatearea"]}>
          <Form
            form={form}
            initialValues={{
              modelIntroduce:
                modelIntroduce ||
                (modelFailProp ? modelFailProp.properties.introduction : ""),

              modelName:
                name ||
                (modelFailProp ? modelFailProp.properties.modelName : ""),

              modelSelect:
                dataset ,
              modelType: "",
            }}
            // labelCol={{ span: 3 }}
            // wrapperCol={{ offset: 1}}
            className={classes["form-container"]}
          >
       
              <Form.Item<ModelFormData>
                label="模型选择："
                name="modelSelect"
                rules={[{ required: true, message: "请选择要训练的模型" }]}
                style={{ width: "580px", marginTop: "45px" }}
              >
                <Select
                  size="large"
                  showSearch
                  placeholder="选择要训练的模型"
                  value={modelSelect}
                  onChange={(e) => {
                    
                    setModelSelect(e);
                  }}
                  style={{ flex: 1 }}
                  options={OPTIONS}
                />
              </Form.Item>
            

            <Form.Item<ModelFormData>
              name="modelName"
              label="任务名称："
              rules={[
                { required: true, message: "请输入任务名称" },
                {
                  pattern: new RegExp(
                    "^(?!_)[a-zA-Z0-9._\\u4e00-\\u9fa5]{2,22}$"
                  ),
                  message: "任务名称不符合要求！",
                },
              ]}
              style={{ marginTop: "45px" }}
              extra={
                <div className={classes["inputtext"]}>
                  支持中英文、数字、下划线(_)，2-22个字符，不能以下划线为开头
                </div>
              }
            >
              <Input
                size="large"
                className={classes["filter-input"]}
                placeholder={`请输入训练任务名称`}
                value={modelName}
                onChange={(e) => {
                  setModelName(e.target.value);
                }}
              />
            </Form.Item>
            <Form.Item<ModelFormData>
              name="modelIntroduce"
              label="模型介绍："
              rules={[{ required: true, message: "请输入模型介绍" }]}
              style={{ marginTop: "45px" }}
            >
              <TextArea
                rows={4}
                size="large"
                className={classes["introdution-input"]}
                style={{ height: 120, resize: "none", width: "463px" }}
                placeholder="请输入模型介绍，不超过50字"
                maxLength={50}
                // value={modelIntroduce}
                // defaultValue={modelIntroduce}
                // onChange={(e) => {
                //     setModelIntronduce(e.target.value);
                //     form.setFieldValue('modelIntroduce', e.target.value);
                // }}
              />
            </Form.Item>
          </Form>
        </div>
      </div>
    </>
  );
});

export default ModelSelect;

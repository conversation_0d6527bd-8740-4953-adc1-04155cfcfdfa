import{j as e,r as t,s,a2 as a,n as l,t as i,K as n,B as r,ar as o,ae as d,am as c,as as u,ap as h,I as f,at as m,au as p,a4 as x,k as v,av as g,S as y,f as j,l as w,q as S,aw as k,a6 as I,c as b,d as C,ax as N,ay as T,az as L,D as z,e as A,R as E,al as F,v as D,aA as B,ac as q,aB as R}from"./react-core-BrQk45h1.js";import{T as M}from"./types-ccCJOgIs.js";import{f as W}from"./formatred-B3vJFs-2.js";import{e as O,T as P}from"./empty-logo-5H_PPUCG.js";import{j as $,q as U,k as H,e as K,l as _,m as Q,n as Z,o as G,s as J}from"./task-ClBf-SyM.js";import{g as V,q as X}from"./conts-BxhewW4k.js";import{H as Y}from"./HighlightedText-DunVM1pH.js";import{w as ee}from"./warningIcon-B4XoRFEk.js";import{c as te,g as se,b as ae,e as le,f as ie,h as ne,i as re}from"./review-5qiVEhSO.js";import{F as oe}from"./file-utils-DYD-epjE.js";import{D as de}from"./DatasetExportModal-C5GWuAi5.js";import{a as ce}from"./avatar-1-Bswa0fNs.js";import{i as ue}from"./info-icon-DzE4dkR7.js";import{a as he}from"./dataset-CNkWbBvO.js";import{q as fe}from"./service-CkPMTEw7.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const me=({text:t,searchKeyword:s})=>{if(!s.trim())return e.jsx("span",{children:t});const a=new RegExp(`(${s})`,"gi"),l=t.split(a);return e.jsx("span",{children:l.map(((t,s)=>a.test(t)?e.jsx("mark",{style:{backgroundColor:"rgba(15, 182, 152, 0.3)"},children:t},s):e.jsx("span",{children:t},s)))})};const pe=({exportTaskData:h,visible:f,OnClose:m,OnQADonload:p})=>{const[x,v]=t.useState("json"),[g,y]=t.useState(),[j,w]=t.useState(),[S,k]=t.useState(),[I,b]=t.useState("createTime"),[C,N]=t.useState("desc"),T=t.useRef(S),[L,z]=t.useState(ce),[A,E]=t.useState(!1),[F,D]=t.useState([]),[B,q]=t.useState([]),[R,P]=t.useState([]),[K,_]=t.useState(),[Q,Z]=t.useState(new Map),[G,J]=t.useState([]),[V,X]=t.useState([]),[Y,ee]=t.useState(0),[te,se]=t.useState("taskName"),[ae,le]=t.useState(),[ie,ne]=t.useState([]),re=[{value:"切换为总数量",label:"切换为总数量"},{value:"切换为已审核",lable:"切换为已审核"}],[oe,de]=t.useState(re[0].value);let me;var pe;(pe=me||(me={}))[pe.All=0]="All",pe[pe.Reviewed=1]="Reviewed",pe[pe.Unreviewed=2]="Unreviewed";const[xe,ve]=t.useState([]),[ge,ye]=t.useState({page:1,size:10,total:0}),{task_id:je}=s(),[we,Se]=t.useState(0),ke=t.useRef();const Ie=e=>{A||(E(!0),$(e).then((e=>{var t;if(E(!1),200===(null==(t=e.data)?void 0:t.code)){const t=e.data.data;w(t),async function(e){var t;const s=[];if(e){for(const a of e){const e=await he(a);if(200===(null==(t=e.data)?void 0:t.code)){const t=e.data.data;t.childFileId=a,s.push(t)}}J(s)}}(t.fileIdList)}})))};t.useEffect((()=>(je&&Ie(je),()=>{ke.current&&clearInterval(ke.current)})),[]);const be=[{key:"1",label:"任务名称",children:(null==j?void 0:j.taskName)?null==j?void 0:j.taskName:"NULL"},{key:"2",label:"创建用户",children:e.jsxs(e.Fragment,{children:[(()=>{const[s,a]=t.useState("");return t.useEffect((()=>{(async()=>{const e=await fe();e&&e.data&&a(e.data.userName)})()}),[]),e.jsx("b",{className:"namesytle",children:s})})(),e.jsx(a,{className:"ellipse-parent29-1",src:L})]})},{key:"3",label:"任务ID",children:(null==j?void 0:j.id)?j.id:"NULL"},{key:"4",label:"创建时间",children:(null==j?void 0:j.createTime)?W(new Date(null==j?void 0:j.createTime)):"NULL"},{key:"5",label:"任务状态",children:e.jsxs(e.Fragment,{children:[(t=>{if((null==t?void 0:t.taskStatus)===M.error){if(t.complete===t.total)return e.jsx("label",{style:{color:"orange"},children:"任务完成"});if(t.complete<t.total)return e.jsx("label",{style:{color:"orange"},children:"进行中"})}else{if((null==t?void 0:t.taskStatus)===M.inProgress)return"进行中";if((null==t?void 0:t.taskStatus)===M.success)return"任务完成";if((null==t?void 0:t.taskStatus)===M.failed)return e.jsx("label",{style:{color:"red"},children:" 任务失败"})}})(j),"（",(null==j?void 0:j.complete)+" / "+(null==j?void 0:j.total),"）"]})},{key:"6",label:"审核进度",children:j&&"FAILED"!==(null==j?void 0:j.taskStatusEnum)?e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx("label",{children:j.reviewCount+" / "+j.qaCount}),e.jsx(l,{title:"为保证导出数据质量，请在导出前进行人工审核",children:e.jsx("img",{src:ue,style:{width:"16px",height:"16px"}})})]}):e.jsx("label",{children:"-"})}],Ce=be[2].children,[Ne,Te]=i.useMessage(),Le=[{title:"成员",dataIndex:"userName",render:(t,s)=>{const l=s.user.userName;return s.user.userAvatar,e.jsxs(e.Fragment,{children:[e.jsx(a,{style:{display:"flex",margin:"0 0 0 1.2rem"},src:L}),e.jsx(e.Fragment,{children:e.jsx("div",{style:{display:"flex",margin:"-1.6rem 0px 0px 4.7rem"},children:l})})]})},width:"20.6%",align:"center"},{title:"审核数量",dataIndex:"number",render:(e,{curAllocated:t,curReviewed:s})=>oe===re[0].value?t:s,width:"21.6%",align:"center"},{title:"比例",dataIndex:"reviewCount",width:"10.6%",align:"center",render:(e,{totalProgress:t,curProgress:s})=>oe===re[0].value?(100*t).toFixed(2)+"%":(100*s).toFixed(2)+"%"},{title:" ",dataIndex:"dataprocess",render:(t,{totalProgress:s,curProgress:a})=>oe===re[0].value?e.jsx(u,{strokeLinecap:"butt",showInfo:!1,size:[220,15],percent:100*s}):e.jsx(u,{strokeLinecap:"butt",showInfo:!1,size:[220,15],percent:100*a}),width:"27.3%"}];function ze(e){je&&(async()=>{try{const t=await H(e);E(!1);const s=t.data;k(Array.isArray(s)?s:void 0)}catch(t){}})()}t.useEffect((()=>{T.current=S}));const Ae=async()=>{je&&(await ze(je),await function(e){try{A||(E(!0),U(e).then((e=>{E(!1);const t=null==e?void 0:e.data;if(t){const e=Array.isArray(t)?t:[t];ne(e)}})))}catch(t){}}(je))};return t.useEffect((()=>(Ae(),()=>{ke.current&&clearInterval(ke.current)})),[]),t.useEffect((()=>(clearInterval(ke.current),()=>{ke.current&&clearInterval(ke.current)})),[ge.page,ge.size,te,ae,S]),e.jsx(e.Fragment,{children:e.jsxs(n,{centered:!0,className:"preview-folder-modal",title:"审核进度",keyboard:!1,maskClosable:!1,styles:{body:{height:"528px"}},width:"870px",open:f,onOk:()=>{m()},onCancel:()=>{m()},footer:[],children:[e.jsxs("div",{className:"task-detail-info",children:[e.jsx("div",{style:{width:"100%",textAlign:"start",justifyContent:"space-between",display:"inline-flex",marginBottom:"1rem",alignItems:"center"}}),e.jsxs("div",{style:{color:"#0FB698",position:"absolute",left:"18rem",top:"7.4rem"},children:[Te,e.jsx(r,{type:"link",size:"small",onClick:()=>{Ne.info("复制成功"),async function(e){try{return await navigator.clipboard.writeText(e)}catch{const t=document.createElement("textarea"),s=document.activeElement;t.value=e,t.setAttribute("readonly",""),t.style.contain="strict",t.style.position="absolute",t.style.left="-9999px",t.style.fontSize="12pt";const a=document.getSelection(),l=a?a.rangeCount>0&&a.getRangeAt(0):null;document.body.appendChild(t),t.select(),t.selectionStart=0,t.selectionEnd=e.length,document.execCommand("copy"),document.body.removeChild(t),l&&(a.removeAllRanges(),a.addRange(l)),s&&s.focus()}}(Ce)},style:{position:"relative",left:"-4rem",width:"5rem"}})]}),e.jsx("div",{className:"line"}),e.jsx(o,{column:2,style:{padding:"2rem 1rem ",width:"50rem"},size:"small",items:be})]}),e.jsx("div",{style:{color:"#6D7279"},className:"progress-process",children:"进度进程明细"}),e.jsx(d,{virtual:!0,locale:{emptyText:e.jsx(c,{image:O,description:e.jsx("span",{className:"dataset-table-empty-label",children:"空空如也，去上传本地文件吧~"})})},style:{flex:1},tableLayout:"fixed",rowKey:"taskId",className:"dataset-table",columns:Le,dataSource:ie,pagination:!1,scroll:{y:250}})]})})},xe=new Map,ve=new Map,ge=({tags:s,availableTags:a=[],onTagsChange:n,maxTags:r=3,qaId:o})=>{const[d,c]=t.useState(!1),[u,v]=t.useState(""),g=t.useRef(null),[y,j]=t.useState(null),[w,S]=t.useState(!1),[k,I]=t.useState(!1),[b,C]=t.useState(new Set),[N,T]=t.useState(new Map),L=o||"default",[z,A]=t.useState((()=>(ve.has(L)||ve.set(L,{currentTags:[...s],availableTags:[...a]}),ve.get(L)))),E=z.currentTags,F=z.availableTags,D=o||"default",[B,q]=t.useState(xe.get(D)||!1),R=t.useRef(null),M=e=>{q(e),xe.set(D,e)};t.useRef(B).current=B;const W=e=>{const t=/^[a-zA-Z0-9\u4e00-\u9fa5]+$/,s=e.filter((e=>t.test(e))),a=e.filter((e=>!t.test(e)));return a.length>0&&i.warning(`已删除不符合格式的标签: ${a.join(", ")}（仅支持中英文和数字）`),s},O=(e,t)=>{const s=W(e);n(s,t)};t.useEffect((()=>{d&&g.current&&g.current.focus()}),[d]);const P=(e,t)=>{const s=W(e),a={currentTags:s,availableTags:t};ve.set(L,a),A(a),s.length!==e.length&&O(s,t)};t.useEffect((()=>{const e=/^[a-zA-Z0-9\u4e00-\u9fa5]+$/,t=s.filter((t=>e.test(t)));if(t.length!==s.length){const e={currentTags:t,availableTags:[...a]};ve.set(L,e),A(e)}}),[s,a,L]);const $=[...E].sort(((e,t)=>e.localeCompare(t,"zh-CN"))),U=(e,t)=>{C((t=>new Set(t).add(e))),T((s=>new Map(s).set(e,t))),setTimeout((()=>{C((t=>{const s=new Set(t);return s.delete(e),s})),T((t=>{const s=new Map(t);return s.delete(e),s}))}),600)},H=()=>{if(u&&!E.includes(u)&&E.length<r){if(!/^[a-zA-Z0-9\u4e00-\u9fa5]+$/.test(u))return i.error(`标签 "${u}" 格式不正确，仅支持中英文和数字`),c(!1),void v("");const e=[...E,u];let t=F;F.includes(u)&&(t=F.filter((e=>e!==u))),P(e,t),O(e,t)}c(!1),v("")};return t.useEffect((()=>{const e=e=>{if(!B)return;const t=e.target;R.current&&R.current.contains(t)||(M(!1),c(!1),v(""))};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[B]),e.jsx("div",{ref:R,style:{position:"relative",width:"100%"},children:e.jsxs("div",{className:"qa-tags",onClick:e=>{e.stopPropagation(),e.stopPropagation()},onMouseEnter:()=>S(!0),onMouseLeave:()=>S(!1),children:[e.jsxs("div",{className:"tag-content",children:[$.map(((t,s)=>{const a=N.get(t)||"",l=b.has(t);return e.jsx("div",{className:"tag-group",children:e.jsx(h,{className:`edit-tag ${a} ${l?"tag-moving":""}`,closable:y===s,color:"#D8F2EF",onClose:e=>{var s;e.preventDefault(),s=t,B&&U(s,"tag-move-down"),setTimeout((()=>{const e=E.filter((e=>e!==s));let t=F;B&&!F.includes(s)&&(t=[...F,s].sort(((e,t)=>e.localeCompare(t,"zh-CN")))),P(e,t),O(e,t)}),B?300:0)},onClick:e=>{e.stopPropagation()},onMouseEnter:()=>j(s),onMouseLeave:()=>j(null),children:t},`tag-${s}`)})})),d&&e.jsx(f,{ref:g,size:"small",placeholder:"输入自定义标签",value:u,onChange:e=>v(e.target.value),maxLength:10,onBlur:H,onPressEnter:H,style:{width:"120px",marginRight:"8px"}}),(0===$.length||w)&&e.jsx(l,{title:$.length>=r?"当前标签已满，请先删除标签再添加新标签":"添加标签",children:e.jsx(h,{className:"site-tag-plus",onClick:()=>{E.length>=r?i.warning("当前标签已满，请先删除标签再添加新标签"):M(!0)},style:{background:"#D8F2EF",borderStyle:"dashed",color:"#00000066",cursor:"pointer",transition:"all 0.3s ease"},children:e.jsx(m,{})})})]}),B&&e.jsxs("div",{className:"available-tags-panel",style:{borderTop:"1px solid #F1F1F1",paddingTop:"8px",display:"flex",alignItems:k?"flex-start":"center"},onClick:e=>e.stopPropagation(),children:[e.jsx("span",{style:{color:"#0FB698",flexShrink:0},children:"可选标签："}),e.jsxs("div",{style:{display:"flex",flexWrap:"wrap",maxHeight:k?"none":"25px",overflow:"hidden",flex:1,rowGap:"8px"},children:[F.filter((e=>!E.includes(e))).sort(((e,t)=>e.localeCompare(t,"zh-CN"))).map((t=>{const s=N.get(t)||"",a=b.has(t);return e.jsx(h,{className:`select-tag ${s} ${a?"tag-moving":""}`,color:"#D8F2EF",onClick:()=>{return e=t,void(!E.includes(e)&&E.length<r?(U(e,"tag-move-up"),setTimeout((()=>{const t=[...E,e],s=F.filter((t=>t!==e));P(t,s),O(t,s)}),300)):E.includes(e)?i.warning("已选择该标签"):E.length>=r&&i.warning("当前已存在3个标签，请先删除标签再添加新标签"));var e},children:t},t)})),e.jsx(h,{className:"select-tag",color:"#D8F2EF",onClick:()=>c(!0),children:"自定义标签"})]}),e.jsx("span",{style:{cursor:"pointer",color:"#999",marginLeft:"8px"},onClick:()=>I(!k),children:k?e.jsx(p,{}):e.jsx(x,{})})]})]})})},ye=()=>{var l,d,c,u,h,m,p,x,O,U,H,ue;const{task_id:he}=s(),xe=v();he||xe("/main/task");const[ve,ye]=t.useState(""),[je,we]=t.useState(!1),[Se,ke]=t.useState(),[Ie,be]=t.useState([]),[Ce,Ne]=t.useState([]),[Te,Le]=t.useState([]),[ze,Ae]=t.useState([]),[Ee,Fe]=t.useState(-1),[De,Be]=t.useState([]),[qe,Re]=t.useState([]),[Me,We]=t.useState([]),[Oe,Pe]=t.useState(),[$e,Ue]=t.useState([]),[He,Ke]=t.useState([]),[_e,Qe]=t.useState(new Set),[Ze,Ge]=t.useState([]),[Je,Ve]=t.useState(new Set),[Xe,Ye]=t.useState(new Map),[et,tt]=t.useState(!1),[st,at]=t.useState(new Map),[lt,it]=t.useState(new Set),[nt,rt]=t.useState(!1),[ot,dt]=t.useState(""),[ct,ut]=t.useState(!1),[ht,ft]=t.useState(["技术","产品","设计","运营","市场","销售","客服","财务","人事","法务","行政","研发","测试","运维","安全","数据","算法","架构","前端","后端","移动端","全栈","DevOps","UI/UX"]),{confirm:mt}=n,[pt,xt]=t.useState("1"),[vt,gt]=t.useState([]),yt=t.useRef(),[jt,wt]=t.useState(!1),[St,kt]=t.useState(!1),[It,bt]=t.useState(!0),Ct=t.useRef(),[Nt,Tt]=t.useState([]),[Lt,zt]=t.useState([]),[At,Et]=t.useState(ce),[Ft,Dt]=t.useState(!0),[Bt,qt]=t.useState(!1),[Rt,Mt]=t.useState([]),[Wt,Ot]=t.useState(),[Pt,$t]=t.useState({page:1,size:10,total:0}),[Ut,Ht]=t.useState(),[Kt,_t]=t.useState(),[Qt,Zt]=t.useState(),[Gt,Jt]=t.useState(),[Vt,Xt]=t.useState("关键词检索"),[Yt,es]=t.useState(""),[ts,ss]=t.useState(!1),[as,ls]=t.useState("keyWord"),[is,ns]=t.useState([]),[rs,os]=t.useState([]),[ds,cs]=t.useState("文件视图"),[us,hs]=t.useState(""),[fs,ms]=t.useState(!1),ps=[{icon:"DislikeSvg",value:"较差",color:"#B4B4B4"},{icon:"ChecKSvg",value:"一般",color:"#FBE2A4"},{icon:"LikeSvg",value:"良好",color:"#0FB698"}],xs=[{key:"reviewed",label:"已审核项"},{key:"verifyUser",label:"按审核人",children:Nt},{key:"reviewOption",label:"按审核选项",children:Lt},{key:"unreviewed",label:"未审核项"}],vs=[{key:"1",label:"任务ID",children:(null==Se?void 0:Se.id)?Se.id:"NULL"},{key:"2",label:"创建用户",children:e.jsxs(e.Fragment,{children:[e.jsx(a,{className:"ellipse-parent29-1",src:At}),(()=>{const[s,a]=t.useState("");return t.useEffect((()=>{(async()=>{const e=await fe();e&&e.data&&a(e.data.userName)})()}),[]),e.jsx("b",{className:"namesytle",children:s})})()]})},{key:"3",label:"段落精细度",children:(null==Se?void 0:Se.splitLevel)?V[(null==Se?void 0:Se.splitLevel)-1]:"自动"},{key:"4",label:"任务状态",children:e.jsxs(e.Fragment,{children:[(t=>{if((null==t?void 0:t.taskStatus)===M.error){if(t.complete===t.total)return e.jsx("label",{style:{color:"orange"},children:"任务完成"});if(t.complete<t.total)return e.jsx("label",{style:{color:"orange"},children:"进行中"})}else{if((null==t?void 0:t.taskStatus)===M.inProgress)return"进行中";if((null==t?void 0:t.taskStatus)===M.success)return"任务完成";if((null==t?void 0:t.taskStatus)===M.failed)return e.jsx("label",{style:{color:"red"},children:" 任务失败"})}})(Se),"（",(null==Se?void 0:Se.complete)+" / "+(null==Se?void 0:Se.total),"）"]})},{key:"5",label:"创建时间",children:(null==Se?void 0:Se.createTime)?W(new Date(null==Se?void 0:Se.createTime)):"NULL"},{key:"6",label:"提问密度",children:(null==Se?void 0:Se.densityLevel)?X[(null==Se?void 0:Se.densityLevel)-1]:"自动"},{key:"7",label:"需求描述",children:e.jsx("div",{style:{maxWidth:230,textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"},children:null==Se?void 0:Se.description})},{key:"8",label:"审核进度",children:Se&&"FAILED"!==(null==Se?void 0:Se.taskStatus)?e.jsx("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:e.jsx("label",{children:Se.reviewCount+" / "+Se.qaCount})}):e.jsx("label",{children:"-"})}];vs[0].children;const[gs,ys]=i.useMessage();function js(e){let t=[];return e.children&&e.children.forEach((e=>{t.push(e.fileId),e.children&&(t=t.concat(js(e)))})),t}const ws=[{key:"1",label:e.jsx(e.Fragment,{}),showArrow:!1,children:e.jsx(e.Fragment,{children:e.jsx(R,{defaultExpandAll:!0,autoExpandParent:!0,checkedKeys:vt,checkable:je,onSelect:(e,t)=>{if(t.selected)if(t.node.children&&0!==t.node.children.length){const e=Ce.filter((e=>e.fileTreeNodeForTask.fileId===t.node.key));e&&e.length>0&&Mt(e[0].fileTreeNodeForTask.children.map((e=>e.fileId)))}else{const e=Te.filter((e=>e.fileId===t.node.key));Mt(e.map((e=>e.fileId)))}},onCheck:(e,t)=>{if(gt(e),t.node.children&&0!==t.node.children.length){const s=Ce.filter((e=>e.fileTreeNodeForTask.fileId===t.node.key)),a=Ce.map((e=>e.fileTreeNodeForTask.children[0])).map((e=>e.children));if(Array.isArray(e))if(0===e.length)Qe(new Set(null)),Ge([]),Ke([]);else if(s&&s.length>0){const e=js(s[0].fileTreeNodeForTask);Qe(new Set(e))}else Qe(new Set(a[0].map((e=>e.fileId))))}else{const e=Te.filter((e=>e.fileId===t.node.key));t.checked?(Qe(new Set(e.map((e=>e.fileId)))),Ge([]),Ke([]),0!==He.length&&Qe(new Set(e.map((e=>e.fileId))))):Qe(new Set(null))}},treeData:$e,fieldNames:{title:"name",key:"fileId",children:"children"},className:"tree-file",titleRender:t=>{const{name:s,qaCount:a,reviewedCount:l}=t;return e.jsxs("div",{style:{display:"flex",flexDirection:"row",whiteSpace:"nowrap"},children:[e.jsx("span",{style:{flex:4,whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",maxWidth:"125px",width:"125px"},children:s}),e.jsxs("label",{style:{color:"#6D7279",flex:1},children:["     ","已审核: "+l+" / "+a]})]})}})})}],Ss=()=>{Ht(void 0),_t(void 0),Zt(""),Jt(""),es("")};t.useEffect((()=>{he&&Is(he)}),[Pt.page,Pt.size,Rt,Wt,Kt,Ut,Qt,Gt,lt]),t.useEffect((()=>{tt(Ns())}),[ze,Je]);const ks=({QAItem:s,index:a})=>{var l;const[n,r]=t.useState(Ee===a),[o,d]=t.useState(!!Je.has(s.id)||!!He.some((e=>e.fileId===s.fileId&&e.ids.includes(s.id)))||!!_e.has(s.fileId)&&!Ze.some((e=>e.fileId===s.fileId&&e.ids.includes(s.id))));return e.jsx(q.Item,{className:Ee===a?"task-list-active":"task-list-item",style:{borderLeft:(e=>{let t="white";return null==is||is.forEach((s=>{s.value===e.score&&(t=s.color)})),`3px solid ${t}`})(s)},children:e.jsxs("div",{className:"qa-item-container",children:[je?e.jsx(N,{checked:o,onChange:e=>{const t=e.target.checked;d(t);const a=new Set(Je),l=new Map(Xe);if(t?(a.add(s.id),l.set(s.id,{fileId:s.fileId,id:s.id})):(a.delete(s.id),l.delete(s.id)),Ve(a),Ye(l),t)gt((e=>e.includes(s.fileId)?e:[...e,s.fileId])),_e.has(s.fileId)?Ge((e=>e.map((e=>{if(e.fileId===s.fileId){const t=e.ids.filter((e=>e!==s.id));return 0===t.length?null:{...e,ids:t}}return e})).filter(Boolean))):Ke((e=>{let t=e;return e.some((e=>e.fileId===s.fileId))?t=e.map((e=>(e.fileId===s.fileId&&e.ids.push(s.id),e))):t.push({fileId:s.fileId,ids:[s.id]}),t}));else if(_e.has(s.fileId))Ge((e=>{let t=e;return e.some((e=>e.fileId===s.fileId))?t=e.map((e=>(e.fileId===s.fileId&&e.ids.push(s.id),e))):t.push({fileId:s.fileId,ids:[s.id]}),t}));else{He.some((e=>e.fileId===s.fileId&&0===e.ids.filter((e=>e!==s.id)).length))&&gt((e=>e.filter((e=>e!==s.fileId)))),Ke((e=>e.map((e=>{if(e.fileId===s.fileId){const t=e.ids.filter((e=>e!==s.id));return 0===t.length?null:{...e,ids:t}}return e})).filter(Boolean)))}}}):null,e.jsxs("div",{className:"qaListItemDiv",onClick:()=>{Me.filter((e=>e.id===s.fileContentId))[0],Be(s.highlightIdxList),Fe((e=>e===a?-1:a)),r(!0),ie(he,s.id).then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)&&ye(e.data.data.content)}))},children:[e.jsxs("div",{children:[e.jsxs("div",{style:{marginBottom:"0.5rem",width:"90%"},className:"qa-list-item",children:[e.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='18'%20height='18'%20viewBox='0%200%2018%2018'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20width='18'%20height='18'%20rx='4'%20fill='%23867ACD'%20fill-opacity='0.15'/%3e%3cpath%20d='M14%2015H11.0818L8.81761%2013.0729C8.74214%2013.0809%208.63732%2013.0849%208.50315%2013.0849C7.66457%2013.0849%206.90147%2012.9053%206.21384%2012.5462C5.53459%2012.1792%204.99371%2011.6525%204.59119%2010.9663C4.19706%2010.28%204%209.47008%204%208.53645C4%207.61081%204.19706%206.80486%204.59119%206.11861C4.99371%205.43235%205.53878%204.90968%206.22641%204.5506C6.91405%204.18353%207.67296%204%208.50315%204C9.33333%204%2010.0881%204.18353%2010.7673%204.5506C11.4549%204.90968%2012%205.43235%2012.4025%206.11861C12.805%206.80486%2013.0063%207.61081%2013.0063%208.53645C13.0063%209.41422%2012.826%2010.1843%2012.4654%2010.8466C12.1132%2011.5089%2011.6268%2012.0316%2011.0063%2012.4146L14%2015ZM6.27673%208.53645C6.27673%209.07109%206.36897%209.54189%206.55346%209.94886C6.74633%2010.3558%207.01048%2010.671%207.34591%2010.8945C7.68134%2011.1099%208.06709%2011.2176%208.50315%2011.2176C8.9392%2011.2176%209.32495%2011.1099%209.66038%2010.8945C10.0042%2010.671%2010.2683%2010.3558%2010.4528%209.94886C10.6457%209.54189%2010.7421%209.07109%2010.7421%208.53645C10.7421%208.00979%2010.6457%207.54697%2010.4528%207.14799C10.2683%206.74102%2010.0042%206.42982%209.66038%206.21436C9.32495%205.99093%208.9392%205.87922%208.50315%205.87922C8.06709%205.87922%207.68134%205.99093%207.34591%206.21436C7.01048%206.42982%206.74633%206.74102%206.55346%207.14799C6.36897%207.54697%206.27673%208.00979%206.27673%208.53645Z'%20fill='%23867ACD'/%3e%3c/svg%3e",alt:"q"}),e.jsx("div",{className:n?"":"qa-list-label",children:e.jsx(me,{text:s.question,searchKeyword:Wt||""})})]}),e.jsxs("div",{className:"qa-list-item",children:[e.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='18'%20height='18'%20viewBox='0%200%2018%2018'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20width='18'%20height='18'%20rx='4'%20fill='%230FB698'%20fill-opacity='0.15'/%3e%3cpath%20d='M9.97015%204L14%2014H11.5577L10.7978%2011.9781H7.03935L6.30665%2014H4L7.9213%204H9.97015ZM10.0651%2010.0246L8.88467%206.90984L7.75848%2010.0246H10.0651Z'%20fill='%230FB698'/%3e%3c/svg%3e",alt:"a"}),e.jsx("div",{className:n?"":"qa-list-label",children:e.jsx(me,{text:s.answer,searchKeyword:Wt||""})})]})]}),fs&&e.jsx("div",{className:"qa-tag",onClick:e=>e.stopPropagation(),children:e.jsx(ge,{tags:(null==(l=null==s?void 0:s.tags)?void 0:l.slice(0,3))||[],availableTags:(null==s?void 0:s.optionalTags)||[],maxTags:3,qaId:s.id,onTagsChange:async(e,t)=>{if(he){const a={answer:s.answer,id:s.id,question:s.question,taskId:he,tags:e,optionalTags:t};await Q(a).then((e=>{var t;if(200===(null==(t=e.data)?void 0:t.code)){i.success("标签更新成功");const e=setTimeout((()=>{le(he).then((e=>{var t,s;200===(null==(t=e.data)?void 0:t.code)?os(e.data.data.tagS):i.error(null==(s=e.data)?void 0:s.message)}))}),800);return()=>clearTimeout(e)}i.error("标签更新失败")})).catch((e=>{i.error("标签更新失败")}))}}})})]})]})},s.id)},Is=e=>{var t;const s={taskId:e,page:Pt.page-1,pageSize:Pt.size,fileIdList:Rt,keyword:Wt,isReview:void 0===Ut&&(!0===Kt||void 0),allocateUserId:Qt,score:Gt,tags:null==(t=Array.from(lt))?void 0:t.join(",")};te(s).then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)&&(be(e.data.data.qaDocumentPage),Ae(e.data.data.qaDocumentPage),$t({...Pt,total:e.data.data.total}))}))},bs=e=>{jt||(wt(!0),$(e).then((e=>{var t;if(wt(!1),200===(null==(t=e.data)?void 0:t.code)){const t=e.data.data;ke(t)}})),Z(e).then((e=>{var t;if(200===(null==(t=e.data)?void 0:t.code)){const t=e.data.data;if(t){const e=t.map(((e,t)=>({...e.fileTreeNodeForTask,fileId:e.fileTreeNodeForTask.fileId}))),s=G(e);Ne(t.map(((e,t)=>(e.fileTreeNodeForTask.fileId=t.toString(),e)))),Le(s),Ue(e)}}})),le(e).then((e=>{var t,s;200===(null==(t=e.data)?void 0:t.code)?os(e.data.data.tagS):i.error(null==(s=e.data)?void 0:s.message)})),Is(e))},Cs=()=>{Ge([]),Qe(new Set),Ke([]),Ve(new Set),Ye(new Map),tt(!1)},Ns=()=>0!==ze.length&&ze.every((e=>Je.has(e.id))),Ts=(e,t)=>{if(!t)return e;const s=[];return e.forEach((e=>{const a=e.label&&e.label.toLowerCase().includes(t.toLowerCase());let l=[];e.children&&e.children.length>0&&(l=Ts(e.children,t)),(a||l.length>0)&&s.push({...e,children:l.length>0?l:e.children})})),s},Ls=[{key:"keyWord",label:"关键词检索"},{key:"reviewedSearch",label:"审核检索",children:Ts(xs,ot)}],{useToken:zs}=g,{token:As}=zs();As.colorBgElevated,As.borderRadiusLG,As.boxShadowSecondary;t.useEffect((()=>(he&&(bs(he),se(he).then((e=>{var t;if(200===(null==(t=null==e?void 0:e.data)?void 0:t.code)){const t=e.data.data;Tt(t.map((e=>({label:e.userName,value:e.id,key:`verifyUser_${e.id}`}))))}})),K(he).then((e=>{var t,s,a;200===(null==(t=null==e?void 0:e.data)?void 0:t.code)&&e.data.data&&(zt(null==(a=null==(s=e.data)?void 0:s.data)?void 0:a.scoreButtonInfo.map((e=>({label:e.value,value:e.value,key:`reviewOption_${e.value}`})))),ns(e.data.data.scoreButtonInfo))})),_(he).then((e=>{var t;200===(null==(t=null==e?void 0:e.data)?void 0:t.code)&&ss(!e.data.data)}))),()=>{yt.current&&clearInterval(yt.current)})),[]);const Es=()=>{const e=new Map;Array.from(Xe.values()).forEach((t=>{e.has(t.fileId)||e.set(t.fileId,[]),e.get(t.fileId).push(t.id)}));const t=Array.from(e.entries()).map((([e,t])=>({fileId:e,ids:t}))),s={excludeInfoList:Ze,fileIdList:Array.from(_e),qaDeleteInfoList:[...He,...t],taskId:he};ne(s).then((e=>{200===e.data.code&&he?(bs(he),Cs()):i.error(e.data.message)}))},Fs=async(e,t)=>{try{const s="WORD"===t?{responseType:"arraybuffer"}:{},a=await re(e,s);if(!a.data)return;const[l,i]="WORD"===t?["application/vnd.openxmlformats-officedocument.wordprocessingml.document","docx"]:["application/json","json"],n=new Blob(["WORD"===t?a.data:JSON.stringify(a.data)],{type:l});oe.saveAs(n,`${(null==Se?void 0:Se.taskName)??"QA数据"}.${i}`)}catch(s){}},Ds=async e=>{Fs({taskId:he??"",all:!0,excludeInfoList:[],fileIdList:[],exportFormat:e},e)},Bs=async e=>{if(Je.size>0&&Je.size===Pt.total){const t=new Map;Array.from(Xe.values()).forEach((e=>{t.has(e.fileId)||t.set(e.fileId,[]),t.get(e.fileId).push(e.id)}));const s=Array.from(t.entries()).map((([e,t])=>({fileId:e,ids:t}))),a={taskId:he??"",all:!0,qaExportInfoList:s,excludeInfoList:Ze,fileIdList:_e?Array.from(_e):void 0,exportFormat:e};Fs(a,e)}else if(Je.size>0){const t=new Map;Array.from(Xe.values()).forEach((e=>{t.has(e.fileId)||t.set(e.fileId,[]),t.get(e.fileId).push(e.id)}));const s=Array.from(t.entries()).map((([e,t])=>({fileId:e,ids:t}))),a={taskId:he??"",all:!1,qaExportInfoList:s,excludeInfoList:Ze,fileIdList:_e?Array.from(_e):void 0,exportFormat:e};Fs(a,e)}else{const t={taskId:he??"",all:!1,qaExportInfoList:He,excludeInfoList:Ze,fileIdList:_e?Array.from(_e):void 0,exportFormat:e};Fs(t,e)}},qs=e=>{e.preventDefault(),_(he).then((e=>{var t,s;200===(null==(t=null==e?void 0:e.data)?void 0:t.code)&&(null==(s=null==e?void 0:e.data)?void 0:s.data)||(e=>{J({areviewCriteria:"请审核生成的答案内容是否正确，如不正确请对其进行编辑修改",qreviewCriteria:"请审核所提出的问题是否具有价值，如不具有实际应用价值，应将其删除",scoreReviewCriteria:"请将生成数据的字符总长度、语言自然度作为主要指标进行综合打分评价",taskId:e,isStepTwo:!0,scoreButtonInfoList:ps});const t=sessionStorage.getItem("id");t&&ae(e,t)})(he)}));const t=sessionStorage.getItem("id");t&&he&&ae(he,t),xe(`/main/task/review/${he}`)};return e.jsxs(y,{children:[e.jsxs("div",{className:"createTaskContent",children:[e.jsxs(j,{size:20,style:{display:"inline-flex",alignItems:"center"},children:[e.jsx(r,{style:{fontSize:"12px",width:"36px",height:"36px"},shape:"circle",icon:e.jsx(w,{}),onClick:()=>xe("/main/task")}),e.jsx("div",{className:"mediumText",style:{fontSize:"28px",lineHeight:"36px",fontWeight:"500"},children:null==Se?void 0:Se.taskName})]}),e.jsxs("div",{className:"detailContent",children:[e.jsxs("div",{className:"task-detail-info",children:[e.jsxs("div",{className:"head_info",children:[e.jsx("div",{style:{width:"100%",textAlign:"start",justifyContent:"space-between",display:"inline-flex",marginBottom:"1rem",paddingBottom:"1rem",alignItems:"center",borderBottom:"1px solid #E1EAEF"},children:e.jsx("label",{className:"mediumText",children:"任务信息"})}),e.jsx(o,{column:1,style:{padding:"1rem",width:"100%"},size:"small",items:vs}),e.jsx(r,{type:"link",className:"checkprogess",onClick:()=>{qt(!0),Dt(!0)},children:"查看审核进度"}),e.jsx(pe,{visible:Bt,OnClose:()=>qt(!1),OnQADonload:()=>{Ft?Ds():Bs(),qt(!1)}})]}),e.jsxs("div",{className:"taskDetailLf",style:{borderBottom:"1px solid #efefef",height:"27rem"},children:[e.jsx(S,{options:["文件视图","标签视图"],size:"large",onChange:e=>{cs(e)},className:"taskContent-segmented"}),"文件视图"===ds&&e.jsx(k,{ghost:!0,bordered:!1,expandIconPosition:"end",size:"small",destroyInactivePanel:!0,items:ws,activeKey:pt,onChange:e=>{xt(e)},className:"taskContent-collapse"}),"标签视图"===ds&&e.jsx("div",{style:{padding:"20px 0"},children:rs&&rs.length>0?e.jsx("div",{className:"tag-container",children:rs.map(((t,s)=>{const a=`${t.tag}`,l=lt.has(a);return e.jsxs("div",{onClick:()=>(e=>{const t=new Set(lt);t.has(e)?t.delete(e):t.add(e),it(t)})(a),className:l?"tag-selected":"tag-item",children:[e.jsx("span",{children:a+`(${t.count})`}),l&&e.jsx(I,{style:{color:"#0FB698",fontSize:"12px"}})]},s)}))}):e.jsx("div",{style:{textAlign:"center",color:"#999",padding:"40px 0",fontSize:"14px"},children:"暂无标签数据"})})]})]}),e.jsxs("div",{className:"task-detail-content",children:[e.jsxs(b,{children:[e.jsx(C,{span:12,children:e.jsxs("div",{style:{width:"100%",textAlign:"start",marginBottom:"1rem"},className:"task-title mediumText",children:["生成结果预览",e.jsx("span",{style:{marginLeft:"3rem",fontSize:"12px",color:"#ccc"},children:"预估生成时间: 已完成"})]})}),e.jsx(C,{span:12,children:e.jsx("div",{style:{width:"100%",textAlign:"start",justifyContent:"flex-end",display:"inline-flex",alignItems:"center"},children:e.jsxs(j,{size:10,style:{marginBottom:"10px"},children:[je?0===_e.size&&0===He.length&&0===Je.size?e.jsx(r,{className:"default-btn",style:{width:"124px",height:"40px",fontSize:"14px",justifyContent:"center"},onClick:()=>{gt([]),Cs(),setTimeout((()=>{we(!1)}))},children:"取消"}):e.jsxs(e.Fragment,{children:[e.jsx(r,{className:"default-btn",style:{width:"124px",height:"40px",fontSize:"14px",justifyContent:"center"},onClick:()=>{rt(!0)},children:"删除所选项"}),e.jsx(r,{className:"default-btn",style:{width:"124px",height:"40px",fontSize:"14px",justifyContent:"center"},onClick:()=>{bt(!1),kt(!0)},children:"导出所选项"}),e.jsx(r,{className:"default-btn",style:{width:"124px",height:"40px",fontSize:"14px",justifyContent:"center"},onClick:()=>{gt([]),Cs(),setTimeout((()=>{we(!1)}))},children:"取消选择"})]}):e.jsx(r,{className:"default-btn",style:{width:"124px",height:"40px",fontSize:"14px",justifyContent:"center"},onClick:()=>{we(!0),xt(["","1"])},children:"多选"}),e.jsx(r,{className:"default-btn",style:{width:"124px",height:"40px",fontSize:"14px",justifyContent:"center"},onClick:()=>{bt(!0),kt(!0)},children:"导出全部"}),e.jsx(r,{type:"primary",className:"primary-btn boldText review-btn",shape:"round",style:{width:120},onClick:e=>{qs(e),xe(`/main/task/review/${he}`,{state:{fromButton:!0}})},children:"人工审核"})]})})})]}),e.jsxs(b,{style:{borderTop:"1px solid rgb(239, 239, 239)"},children:[$e.length>0&&((null==(l=$e[0])?void 0:l.name.endsWith(".csv"))||(null==(d=$e[0])?void 0:d.name.endsWith(".json")))?null:e.jsxs(C,{span:10,style:{display:"flex",padding:"10px 12px ",justifyContent:"space-between",alignItems:"center",borderRight:"1px solid rgb(239, 239, 239)"},children:[e.jsx("span",{className:"task-title mediumText",children:"原文对照"}),e.jsx(r,{type:"text",style:{color:"#ccc"},children:"切换源文件视图"})]}),e.jsxs(C,{span:$e.length>0&&((null==(c=$e[0])?void 0:c.name.endsWith(".csv"))||(null==(u=$e[0])?void 0:u.name.endsWith(".json")))?24:14,style:{display:"flex",padding:"10px 0 10px 12px ",justifyContent:"space-between",alignItems:"center"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[je&&e.jsx(N,{checked:Ns(),indeterminate:(()=>{if(0===ze.length)return!1;const e=ze.filter((e=>Je.has(e.id))).length;return e>0&&e<ze.length})(),onChange:e=>(e=>{if(tt(e),e){const e=new Set(Je),t=new Map(Xe);ze.forEach((s=>{e.add(s.id),t.set(s.id,{fileId:s.fileId,id:s.id})})),Ve(e),Ye(t)}else{const e=new Set(ze.map((e=>e.id))),t=new Set(Je),s=new Map(Xe);e.forEach((e=>{t.delete(e),s.delete(e)})),Ve(t),Ye(s)}})(e.target.checked)}),e.jsx("div",{className:"task-title mediumText",children:"问答对"}),e.jsxs(r,{className:"default-btn",style:{width:"106px",height:"40px",fontSize:"14px",justifyContent:"center",gap:"6px"},onClick:()=>{ms(!fs)},children:["标签显现",fs?e.jsx(T,{style:{color:"#000000",fontSize:"20px"}}):e.jsx(L,{style:{color:"#666",opacity:.3,fontSize:"20px"}})]})]}),e.jsxs("div",{className:"filter-container",children:[e.jsx(z,{menu:{items:Ls,onClick:e=>{if("keyWord"===e.key)ls("keyWord"),Xt("关键词检索"),Ss();else if("reviewedSearch"===e.key)ls("reviewedSearch"),Xt("审核检索");else if("labelSearch"===e.key)ls("labelSearch"),Xt("标签检索");else if(ls("reviewedSearch"),Xt("审核检索"),e.key.indexOf("verifyUser_")>-1){const t=e.key.split("verifyUser_")[1],s=Nt.filter((e=>e.value===t));Zt(t),Jt(""),Ht(void 0),_t(void 0),es(s[0].label)}else if(e.key.indexOf("reviewOption_")>-1){const t=e.key.split("reviewOption_")[1],s=Lt.filter((e=>e.value===t));Jt(t),Ht(void 0),_t(void 0),Zt(""),es(s[0].label)}else"unreviewed"===e.key?(Ht(!1),_t(void 0),Jt(""),Zt(""),es("未审核项")):"reviewed"===e.key&&(Ht(void 0),_t(!0),Jt(""),Zt(""),es("已审核项"))}},dropdownRender:t=>e.jsx(e.Fragment,{children:E.cloneElement(t,{onMouseEnter:e=>{var t;const s=e.target.closest('[data-menu-id*="reviewedSearch"]');s&&!(null==(t=s.dataset.menuId)?void 0:t.includes("submenu"))&&ls("reviewedSearch")}})}),children:e.jsxs(r,{className:"default-btn",style:{width:112,height:"40px",fontSize:"14px",justifyContent:"start",position:"relative"},children:[Vt,e.jsx(A,{style:{position:"absolute",right:10}})]})}),"keyWord"===as&&e.jsx(f,{size:"large",suffix:e.jsx(F,{}),className:"default-input",placeholder:"请输入关键词",style:{width:320},value:Wt,onChange:e=>{Ot(e.target.value)}}),"labelSearch"===as&&e.jsx(D,{mode:"multiple",style:{width:320},placeholder:"请选择标签",suffixIcon:e.jsx(F,{}),allowClear:!0,options:rs.filter((e=>!us||e.tag.toLowerCase().includes(us.toLowerCase()))).map(((e,t)=>{const s=`${e.tag}`;return{label:s,value:s}})),dropdownRender:t=>e.jsxs(e.Fragment,{children:[e.jsx(j,{style:{padding:"0 8px 4px"},children:e.jsx(f,{placeholder:"请输入",style:{width:300},size:"large",suffix:e.jsx(F,{}),allowClear:!0,value:us,onChange:e=>hs(e.target.value),onKeyDown:e=>e.stopPropagation()})}),t]})}),"reviewedSearch"===as&&e.jsx(f,{placeholder:"请选择审核检索项",style:{width:320},size:"large",prefix:e.jsx(F,{}),allowClear:!0,readOnly:!0,onClick:()=>ut(!0),value:Yt,onChange:e=>{es(e.target.value),Ss()},suffix:Yt?e.jsx(B,{onClick:e=>{e.stopPropagation(),es(""),Ss()}}):"",onKeyDown:e=>e.stopPropagation()})]})]})]}),e.jsxs(b,{className:"taskDetailContent",style:{padding:0},children:[$e.length>0&&((null==(h=$e[0])?void 0:h.name.endsWith(".csv"))||(null==(m=$e[0])?void 0:m.name.endsWith(".json")))?null:e.jsx(C,{span:10,style:{borderRight:"1px solid #efefef"},children:e.jsx("div",{style:{textAlign:"start"},children:e.jsxs("div",{className:"taskDetailLf1",style:{display:"flex",flexDirection:"column",height:"580px"},children:[e.jsx("label",{style:{marginBottom:"1rem"},children:Oe}),e.jsx("div",{style:{flex:1},children:e.jsx(y,{children:e.jsx("div",{style:{height:"max-content",textAlign:"start",marginTop:"16px",lineHeight:"26px",color:"#6D7279",whiteSpace:"pre-wrap",overflowWrap:"break-word"},children:e.jsx(Y,{text:ve,highlights:De})})})})]})})}),e.jsx(C,{span:$e.length>0&&((null==(p=$e[0])?void 0:p.name.endsWith(".csv"))||(null==(x=$e[0])?void 0:x.name.endsWith(".json")))?24:14,style:{height:"596px"},children:e.jsx(y,{height:"740px",ref:Ct,children:e.jsx(q,{size:"small",bordered:!1,dataSource:ze,renderItem:(t,s)=>e.jsx(ks,{QAItem:t,index:s},"QADocumentItem"+s)})})})]}),e.jsxs(b,{children:[$e.length>0&&((null==(O=$e[0])?void 0:O.name.endsWith(".csv"))||(null==(U=$e[0])?void 0:U.name.endsWith(".json")))?null:e.jsx(C,{span:10,children:e.jsxs("div",{className:"taskDetailBottom",children:["溯源置信度 :  ",e.jsx("span",{className:"span1",children:" 示例"}),"≥","90%    ",e.jsx("span",{className:"span2",children:" 示例"}),"≥","80%    ",e.jsx("span",{className:"span3",children:" 示例"}),"≥","70%"]})}),e.jsx(C,{span:$e.length>0&&((null==(H=$e[0])?void 0:H.name.endsWith(".csv"))||(null==(ue=$e[0])?void 0:ue.name.endsWith(".json")))?24:14,children:e.jsx(P,{total:null==Pt?void 0:Pt.total,pageSize:null==Pt?void 0:Pt.size,page:null==Pt?void 0:Pt.page,OnChange:(e,t)=>{var s;const a={page:e,size:t,total:Pt.total};a.page!==Pt.page&&Fe(-1),$t(a),null==(s=Ct.current)||s.scrollToTop()}})})]})]})]})]}),e.jsx(de,{visible:St,exportTypeValue:!0,OnClose:()=>kt(!1),OnQADonload:e=>{It?Ds(e):Bs(e),kt(!1)}}),e.jsx(n,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx("img",{src:ee,alt:"警告",className:"warning"}),e.jsx("span",{style:{marginLeft:8},children:"提示"})]}),centered:!0,open:nt,closable:!1,width:520,className:"delete-model",footer:[e.jsx(r,{className:"modal-btn",onClick:()=>rt(!1),children:"取消"}),e.jsx(r,{className:"modal-btn",onClick:()=>Es,children:"确定"})],wrapClassName:"custom-modal",children:e.jsx("p",{children:"确定要删除所选数据吗？"})})]})};export{ye as default};

import React from 'react';
import { Form, Button } from 'antd';
import UploadList from '@/components/UploadList';
import { FileUploadProps, CreateTaskFormType, DatasetFile } from '../types';
import { CSS_CLASSES } from '../constants';
import { uploadDataset } from '@/api/dataset';

const FileUpload: React.FC<FileUploadProps> = ({
  fileList,
  onFileListChange,
  onShowDataModal,
  onAddTag,
  onDelFile,
  onReUpload,
  datasetModalRef,
}) => {
  const handleDelFile = (index: number) => {
    const fileToDelete = fileList[index];

    // 如果删除的文件来自数据集选择（有dataSetId），则需要同步更新checkbox状态
    if (fileToDelete.dataSetId && datasetModalRef.current) {
      datasetModalRef.current.removeSelection(fileToDelete.dataSetId);
    }

    // 从文件列表中移除
    const updatedDataList = fileList.filter((item, i) => i !== index);
    onFileListChange(updatedDataList);
    onDelFile(index);
  };

  const handleReUpload = (index: number) => {
    const file = fileList[index];
    let status = 'uploading';
    const importFile: any = file;
    importFile.status = status;
    
    uploadDataset({ importDataset: file, tags: [] }).then((res: any) => {
      if (res.data?.code === 200) {
        importFile.status = 'success';
        importFile.dataSetId = res.data.data;
      } else {
        importFile.status = 'error';
      }
      
      const updatedFileList = [...fileList];
      updatedFileList[index] = importFile;
      onFileListChange(updatedFileList);
    });
    
    onReUpload(index);
  };

  const handleAddTag = (dataSetId: string, tags: string[]) => {
    const updateList = [...fileList];
    updateList.forEach((file) => {
      if (file.dataSetId === dataSetId) {
        file.tags = tags;
      }
    });
    onFileListChange(updateList);
    onAddTag(dataSetId, tags);
  };

  return (
    <Form.Item<CreateTaskFormType> name="fileList" label="源数据集">
      <Button
        className={CSS_CLASSES.CREATE_TASK_SELECT_BTN}
        onClick={onShowDataModal}
      >
        在平台库中选择
      </Button>
      <div>
        <UploadList
          fileList={fileList}
          onAddTag={handleAddTag}
          noEditTag={true}
          onDelFile={handleDelFile}
          className={CSS_CLASSES.CREATE_TASK_FILE_LIST}
          onReUpload={handleReUpload}
        />
      </div>
    </Form.Item>
  );
};

export default FileUpload;

import{y as s,k as e,A as a,j as i,p as t,I as r,B as o,c as n,d as l,r as c,S as d,E as m}from"./react-core-BrQk45h1.js";import{d as j,W as p,U as g}from"./WelcomePage-D5s039rc.js";import{q as u}from"./service-CkPMTEw7.js";import{g as x}from"./action-DppZlFyC.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./auth-BKEkOdcM.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";import"./index-y6n7cw2i.js";import"./redux-CXOBDlvD.js";const h=({isAgree:c})=>{const d=s(),m=e(),[p]=a(),g=p.getAll("redirect")[0],h=g&&"/login"!==g&&"/register"!==g&&"/"!==g?g:"/overview",f=async()=>{m(h);const s=await u();s&&s.data&&(sessionStorage.setItem("name",s.data.userName),sessionStorage.setItem("account",s.data.userAccount),sessionStorage.setItem("id",s.data.id)),d(x())};return i.jsx("div",{children:i.jsxs(t,{name:"loginDefault",initialValues:{},onFinish:async s=>{const{account:e,password:a}=s;d(j({account:e,password:a},f))},onFinishFailed:s=>{},autoComplete:"off",children:[i.jsx(t.Item,{name:"account",rules:[{required:!0,message:"请输入账号"}],children:i.jsx(r,{placeholder:"请输入账号",className:"loginInput"})}),i.jsx(t.Item,{validateTrigger:"onSubmit",name:"password",rules:[{required:!0,message:"请输入密码"},{validator:(s,e)=>c?Promise.resolve():Promise.reject(new Error("请阅读并同意协议"))}],children:i.jsx(r.Password,{placeholder:"请输入密码",className:"loginInput"})}),i.jsx(o,{type:"link",className:"fogotPassword info-label",children:"忘记密码"}),i.jsx(t.Item,{style:{paddingTop:"1.5rem"},children:i.jsxs(n,{justify:"start",children:[i.jsx(l,{span:16,children:i.jsx(o,{type:"primary",shape:"round",block:!0,htmlType:"submit",className:"loginBtn boldText",children:"登录"})}),i.jsx(l,{span:7,offset:1,children:i.jsx(o,{shape:"round",block:!0,className:"registerBtn boldText",onClick:()=>{m("/register")},children:"注册"})})]})})]})})},f=()=>{const[s,e]=c.useState(!1),a=[{label:"账号登录",key:"1",children:i.jsx(h,{isAgree:s})}];return i.jsx(d,{style:{position:"inherit"},children:i.jsx("div",{className:"bg",children:i.jsxs(n,{justify:"start",style:{minWidth:"1440px"},children:[i.jsx(l,{span:12,className:"loginLf",children:i.jsx(p,{})}),i.jsx(l,{span:12,className:"loginRt",children:i.jsxs("div",{className:"loginBox",children:[i.jsx("b",{className:"loginTitle boldText",children:"行至账号"}),i.jsx(m,{destroyInactiveTabPane:!1,animated:!0,size:"large",defaultActiveKey:"1",items:a,className:"loginTabs"}),i.jsx(g,{isAgree:s,onIsAgreeChange:s=>{e(s)}})]})})]})})})};export{f as default};

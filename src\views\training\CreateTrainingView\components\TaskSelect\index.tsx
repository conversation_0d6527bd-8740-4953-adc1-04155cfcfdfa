import {
  <PERSON><PERSON>,
  <PERSON>,
  Row,
  Segmented,
  Slide<PERSON>,
  <PERSON>,
  Table,
  TableProps,
  Tag,
  Tooltip,
} from "antd";
import { QuestionCircleFilled } from "@ant-design/icons";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import classes from "./index.module.css";
import { TaskType } from "../../../../../types";
import { testSetRatio } from "../../../../../utils/conts";
import { SliderMarks } from "antd/es/slider";
import TaskSelectModal from "../../../../../components/TaskSelectModal";
import { getTaskDetail, getTasks } from "../../../../../api/task";
import { TariningType, ModelFailData } from "../../type";
import group151 from "../../../../../assets/img/group-151.svg";

interface TaskSelectProp {
  modelData: TariningType;
}
interface MyObject {
  modelId: number;
  modelConfigData: ModelFailData;
}
const TestSetValueMap = (testSetStr: string) => {
  const index = testSetRatio.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
const TaskSelect = forwardRef((prop: TaskSelectProp, ref) => {
  const { tasks, testSetConfig, testSet } = prop.modelData;

  const [modelConfig, setModelConfig] = useState<string | number>(
    testSetConfig || "自动配置"
  );
  const [testSetNum, setTestSeNnum] = useState(
    testSet ? TestSetValueMap(testSet) : 50
  );
  const [visible, setVisible] = useState<boolean>(false);
  const training: string = localStorage.getItem("trainingData") || "{}";
  const objectArray: MyObject[] = JSON.parse(training) as MyObject[];
  const [modelFailProp, setModelFailProp] = useState<ModelFailData>(
    objectArray[0]?.modelConfigData
  );
  // 任务id数组
  const taskIds: Array<string> = modelFailProp?.properties.trainConfig.id;
  const [taskData, setTaskData] = useState<TaskType[]>(tasks || []);
const [allTasks, setAllTasks] = useState<TaskType[]>([]);//所有数据集
  console.log("taskData", taskData);
  

  // 获取所有数据集
  const getAllTasks = () => {
    getTasks({size:-1}).then((res) => {
      if (res?.data?.code === 200) {
        const data = res.data.data;
        setAllTasks(data);
        if(taskIds?.length > 0){
          const filteredData = data.filter((item) =>
            taskIds.includes(item.taskId)
          );
          setTaskData(filteredData);
        }
      }
    });
  };
  // 根据id获取任务数据
  useEffect(() => {
    getAllTasks();
  }, []);

  const columns: TableProps<TaskType>["columns"] = [
    {
      title: "序号",
      dataIndex: "index",
      render: (text, record, index) => <>{index + 1}</>,
      width: "20%",
    },
    {
      title: "任务名称",
      dataIndex: "taskName",
      width: "40%",
    },
    {
      title: "问答对数量",
      dataIndex: "qaCount",
      width: "40%",
    },
  ];



  const marks: SliderMarks = {
    0: (
      <Tooltip title={testSetRatio[0]}>
        <div className={classes["slider-mark"]}>{testSetRatio[0]}</div>
      </Tooltip>
    ),
    25: (
      <Tooltip title={testSetRatio[1]}>
        <div className={classes["slider-mark"]}>{testSetRatio[1]}</div>
      </Tooltip>
    ),
    50: (
      <Tooltip title={testSetRatio[2]}>
        <div className={classes["slider-mark"]}>{testSetRatio[2]}</div>
      </Tooltip>
    ),
    75: (
      <Tooltip title={testSetRatio[3]}>
        <div className={classes["slider-mark"]}>{testSetRatio[3]}</div>
      </Tooltip>
    ),
    100: (
      <Tooltip title={testSetRatio[4]}>
        <div className={classes["slider-mark"]}>{testSetRatio[4]}</div>
      </Tooltip>
    ),
  };

  useImperativeHandle(ref, () => ({
    getTaskSelectData: () => {
      return {
        Task: taskData,
        TestSetConfig: modelConfig,
        TestSet: testSetRatio[testSetNum / 25],
      };
    },
  }));
  return (
    <>
      <Row className={classes["step-container"]}>
        <Col span={3.5} className={classes["step-label"] + " boldText"}>
          {/* Step 2 */}
          数据配置
        </Col>
        <Col span={20.5} className={classes["step-content"]}>
          <div className={classes["step-container"]}>
            <div className={classes["dataset-title"]}>
              <label>数据集选择:</label>
            </div>
            <div className={classes["dataset-container"]}>
              <Space direction="vertical" size={22}>
                <div>
                  <Button
                    type="default"
                    className="default-btn"
                    onClick={() => setVisible(true)}
                  >
                    在训练数据中选择
                  </Button>
                  <Tooltip
                    placement="rightTop"
                    title="大语言模型SFT任务需要选择多轮对话-非排序类的数据集。"
                  >
                    <img
                      className="frame-child179"
                      style={{ marginLeft: "30px" }}
                      src={group151}
                    />
                  </Tooltip>
                </div>
                
                <Table
                  tableLayout={"fixed"}
                  rowKey="taskId"
                  className="dataset-table"
                  columns={columns}
                  dataSource={taskData}
                />
              </Space>
            </div>
           
            {/* <Space>
                        <QuestionCircleFilled style={{ color: 'rgba(120, 125, 133, 1)', fontSize: '22px' }} />
                        <label style={{ color: 'rgba(120, 125, 133, 1)' }}>大语言模型SFT任务需要选择多轮对话-非排序类的数据集。</label>
                    </Space> */}
          </div>
          <div className={classes["step-container"]}>
            <div className={classes["dataset-title"]}>
              <label>测试集比例:</label>
            </div>
            <div className={classes["dataset-container"]}>
              <Space direction="vertical" size={22}>
                <div>
                    <Segmented
                      className="createtask-segmented"
                      size="large"
                      options={[
                        {
                          label: (
                            <Tooltip title="自动配置">
                              <a
                                onClick={() => setModelConfig("自动配置")}
                                className={
                                  modelConfig === "自动配置"
                                    ? "model-config-active"
                                    : "model-config"
                                }
                              >
                                自动配置
                              </a>
                            </Tooltip>
                          ),
                          value: "自动配置",
                        },
                        {
                          label: (
                            <Tooltip title="手动配置">
                              <a
                                onClick={() => setModelConfig("手动配置")}
                                className={
                                  modelConfig === "手动配置"
                                    ? "model-config-active"
                                    : "model-config"
                                }
                              >
                                手动配置
                              </a>
                            </Tooltip>
                          ),
                          value: "手动配置",
                        },
                      ]}
                      value={modelConfig}
                      onChange={setModelConfig}
                    />
                    <Tooltip
                      placement="rightTop"
                      title="测试集的目的是确保模型在面对新的、未见过的数据时能够准确地进行预测和生成。这有助于评估模型的泛化能力，即其在实际应用中的实用性。"
                    >
                      <img
                        className="frame-child179"
                        style={{ marginLeft: "30px" }}
                        src={group151}
                      />
                    </Tooltip>
                </div>
             
                {modelConfig === "手动配置" ? (
                  <div>
                    <Space size={22}>
                      <label>比例</label>
                      <Slider
                        defaultValue={testSetNum}
                        className="create-task-slider"
                        dots
                        tooltip={{
                          formatter: (val) => {
                            const q = val ? val : 0;
                            return testSetRatio[q / 25];
                          },
                        }}
                        step={25}
                        marks={marks}
                        // defaultValue={0}
                        value={testSetNum}
                        onChange={(val) => {
                          console.log(val);
                          setTestSeNnum(val);
                        }}
                        railStyle={{
                          height: "6px",
                          background: "#F1F6F9",
                          borderTop: "1px solid #E1EAEF",
                          borderBottom: "1px solid #E1EAEF",
                        }}
                        trackStyle={{
                          height: "6px",
                          background: "#0FB698",
                          borderTop: "1px solid #0CA287",
                          borderBottom: "1px solid #0CA287",
                        }}
                        handleStyle={{}}
                        style={{
                          width: "39.25rem",
                          display: "inline-flex",
                          margin: "unset",
                        }}
                      />
                    </Space>
                  </div>
                ) : null}
              </Space>
            </div>
            <div style={{ width: "40%" }}>
      
              {/* <Space style={{ alignItems: 'flex-start' }}>
                            <QuestionCircleFilled style={{ color: 'rgba(120, 125, 133, 1)', fontSize: '22px', marginTop: '12px' }} />
                            <label style={{ color: 'rgba(120, 125, 133, 1)' }}>测试集的目的是确保模型在面对新的、未见过的数据时能够准确地进行预测和生成。这有助于评估模型的泛化能力，即其在实际应用中的实用性。</label>
                        </Space> */}
            </div>
          </div>
        </Col>
      </Row>
      <TaskSelectModal
      alltasks={allTasks}
        taskIds={taskIds}
        visible={visible}
        OnClose={(tasks) => {
          setVisible(false);
          if (tasks) {
            setTaskData(tasks);
          }
        }}
      />
    </>
  );
});
export default TaskSelect;

import{r as e,j as t,K as s,ax as i,B as l,k as a,u as d,s as n,S as o,f as r,af as c,l as h,ab as x,aC as u,T as p,aD as m,ac as f,aE as v,t as j,x as g,ad as y,aF as S}from"./react-core-BrQk45h1.js";import{H as b}from"./HighlightedText-DunVM1pH.js";import{f as w,q as k,u as I,h as N,s as T,c as C,j as D}from"./review-5qiVEhSO.js";import{b as z,e as q,j as E,n as F,o as H}from"./task-ClBf-SyM.js";import{s as L}from"./index-Dp9X2s4-.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const A=({visible:a,OnClose:d})=>{const[n,o]=e.useState(!1);return e.useEffect((()=>{a&&o(!1)}),[a]),t.jsxs(s,{centered:!0,title:"删除提示",keyboard:!1,maskClosable:!1,width:"540px",styles:{body:{height:"50"}},open:a,onCancel:()=>d(!1),footer:t.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",padding:"2rem 0 0 0",gap:"8px"},children:[t.jsx(i,{checked:n,onChange:e=>{o(e.target.checked)},children:"今日不再提示"}),t.jsx(l,{type:"text",onClick:()=>d(!1),shape:"round",children:"取消"}),t.jsx(l,{type:"primary",onClick:()=>{d(!0,n)},shape:"round",className:"review-btn-default",style:{width:"120px"},children:"确认删除"})]}),children:[t.jsx("div",{className:"default-info",style:{color:"black"},children:"确定要删除本条数据吗？"}),t.jsx("div",{className:"upload-error-label",style:{marginTop:"8px"},children:"请注意删除数据后无法恢复。"})]})},B=()=>{const[i,B]=e.useState(!0),[O,R]=e.useState(new Object),{confirm:W}=s,[P,$]=e.useState(""),[M,U]=e.useState(""),[K,V]=e.useState(""),[X,Y]=e.useState([]),_=a(),G=d(),[J,Q]=e.useState([]),[Z,ee]=e.useState(0),[te,se]=e.useState(!1),[ie,le]=e.useState(!1),[ae,de]=e.useState(),{task_id:ne}=n(),[oe,re]=e.useState([]),[ce,he]=e.useState([]),[xe,ue]=e.useState(new Object),[pe,me]=e.useState(new Map),[fe,ve]=e.useState([]),[je,ge]=e.useState(!1),[ye,Se]=e.useState(!1),[be,we]=e.useState(""),[ke,Ie]=e.useState(""),[Ne,Te]=e.useState(!1),[Ce,De]=e.useState(!1),[ze,qe]=e.useState(!1),[Ee,Fe]=e.useState([]),[He,Le]=e.useState(),[Ae,Be]=e.useState([]),[Oe,Re]=e.useState([]),[We,Pe]=e.useState([]),[$e,Me]=e.useState(!1),[Ue,Ke]=e.useState([]),Ve=()=>{_(-1)};if(!ne)return _("/main/task"),null;e.useEffect((()=>{z(ne).then((e=>{var t;if(200===(null==(t=e.data)?void 0:t.code)){const e=sessionStorage.getItem("id");ne&&e&&async function(e,t){var s,i;if(e&&t){const l=await k(e,t);if(2e3===(null==(s=null==l?void 0:l.data)?void 0:s.code)){const e=null==(i=l.data)?void 0:i.code;Ke(e)}}}(ne,e),Qe(ne),q(ne).then((e=>{var t,s;200===(null==(t=null==e?void 0:e.data)?void 0:t.code)&&Pe(null==(s=e.data.data)?void 0:s.scoreButtonInfo)}))}}))}),[]);const Xe=e=>{let t="white";return null==We||We.forEach((s=>{s.value===e.score&&(t=s.color)})),`3px solid ${t}`};e.useEffect((()=>{Ne&&Ye()}),[J]),e.useEffect((()=>{$(O.question),U(O.answer),De(O.review),we(O.score),ce&&(ce.filter((e=>e.id===O.fileContentId))[0],ne&&O.id&&(w(ne,O.id).then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)&&Le(e.data.data.content)})),Y(O.highlightIdxList)))}),[O]);const Ye=()=>{_e(J)},_e=e=>{let t,s=0;if(null==O?void 0:O.id){const i=e.findIndex((e=>e.id===K));-1===i?(s=e.findIndex((e=>!e.review)),t=-1===s?e[0]:e[s]):i!==e.length-1?(t=e.slice(i,e.length).find((e=>!e.review)),t||(t=e.find((e=>!e.review)))):t=e.find((e=>!e.review))}else s=e.findIndex((e=>!e.review)),t=-1===s?e[0]:e[s];t&&(V(t.id),R(t),$(t.question),U(t.answer),ce.filter((e=>e.id===(null==t?void 0:t.fileContentId)))[0],ne&&t.id&&(w(ne,t.id).then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)&&Le(e.data.data.content)})),Y(t.highlightIdxList)))},Ge=e=>{const t=sessionStorage.getItem("id"),s={taskId:e,page:0,pageSize:999,fileIdList:Ee&&Ee.length>0?Ee:void 0,allocateUserId:t??""};C(s).then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)&&(Q(e.data.data.qaDocumentPage),ee(e.data.data.total))}))},Je=e=>{E(e).then((t=>{var s;if(200===(null==(s=t.data)?void 0:s.code)){const s=t.data.data;ue(s);const i=sessionStorage.getItem("id"),l={taskId:e,page:0,pageSize:999,fileIdList:Ee&&Ee.length>0?Ee:void 0,allocateUserId:i??""};C(l).then((e=>{var t;if(200===(null==(t=e.data)?void 0:t.code)){const t=e.data.data.qaDocumentPage;Q(t),ee(e.data.data.total),_e(t)}}))}}))},Qe=e=>{E(e).then((t=>{var s;if(200===(null==(s=t.data)?void 0:s.code)){const s=t.data.data;ue(s),Ge(e),F(e).then((e=>{var t;if(200===(null==(t=e.data)?void 0:t.code)){const t=e.data.data;if(t){const e=t.map(((e,t)=>(e.fileTreeNodeForTask.fileId=t.toString(),e.fileTreeNodeForTask))),s=H(e);Be(t.map(((e,t)=>(e.fileTreeNodeForTask.fileId=t.toString(),e)))),Re(s),ve(e)}}}))}}))};e.useEffect((()=>{ne&&Ge(ne)}),[Ee]),e.useEffect((()=>{J.length>0&&!(null==O?void 0:O.id)&&Ye()}),[J]);const[Ze,et]=e.useState(0),tt=()=>{const[s,i]=e.useState(!1);let a;e.useEffect((()=>{let e;return Ze>=3&&(i(!0),e=setTimeout((()=>{i(!1),et(0)}),3e3)),()=>clearTimeout(e)}),[Ze]);return t.jsxs(r,{size:4,direction:"vertical",style:{textAlign:"center",position:"relative",top:"-18px"},children:[t.jsx("div",{style:{height:22},children:t.jsx("span",{style:{color:"red",display:s?"":"none"},className:"shake-text",children:"操作过快，请认真审核"})}),t.jsx(r,{children:null==We?void 0:We.map(((e,i)=>t.jsxs(l,{disabled:s||!Ce,type:"primary",style:{width:"120px",background:`${be===e.value?e.color:""}`,"--btn-hover-color":`${e.value?e.color:"red"}`},className:"review-btn-default boldText",onClick:()=>{(e=>{if(a&&clearTimeout(a),we(e),et((e=>e+1)),Ne){const t={id:O.id,taskId:xe.id,score:e};T(t).then((e=>{Me(!0),setTimeout((()=>{const e=sessionStorage.getItem("id")??"",t=xe.id;k(t,e).then((e=>{Je(xe.id),we(""),Me(!1)}))}),500)})),a=setTimeout((()=>{et(0)}),3e3)}})(e.value)},children:[t.jsx(S,{component:L(e.icon)}),t.jsx("span",{className:"text-spacing",children:e.value})]},i)))})]})},[st,it]=e.useState(!1);return t.jsxs(o,{children:[t.jsxs("div",{className:"createTaskContent",style:{width:"90.3%"},children:[t.jsx("div",{style:{display:"inline-flex",alignItems:"center",justifyContent:"space-between",width:"100%",marginBottom:"24px"},children:t.jsxs(r,{size:20,children:[t.jsx(l,{style:{fontSize:"12px",width:"36px",height:"36px"},shape:"circle",icon:t.jsx(h,{}),onClick:()=>{const e=W({centered:!0,title:"保留提示",icon:t.jsx(c,{}),width:540,content:t.jsx(t.Fragment,{children:t.jsx("div",{className:"default-info",style:{color:"black"},children:"当前任务未全部审核完成，是否保留？"})}),footer:[t.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",padding:"2rem 0 0 0",gap:"8px"},children:[t.jsx(l,{type:"text",onClick:()=>{(()=>{const e=sessionStorage.getItem("id");ne&&e&&D(ne,e).then((()=>{it(!0)}))})(),e.destroy(),setTimeout((()=>{var e;const t=(null==(e=G.state)?void 0:e.fromButton)?-2:-1;_(t)}),1e3)},shape:"round",children:"否"}),t.jsx(l,{type:"primary",className:"primary-btn",style:{width:"120px"},onClick:()=>{e.destroy(),setTimeout((()=>{var e;const t=(null==(e=G.state)?void 0:e.fromButton)?-2:-1;_(t)}),1e3)},shape:"round",children:"是"})]})],onOk(){Ve()},onCancel(){Ve()}})}}),t.jsx("div",{style:{fontSize:"28px",lineHeight:"36px",fontWeight:"500"},className:"mediumText",children:xe.taskName}),t.jsx("div",{className:"review-tag boldText",children:"人工审核"})]})}),t.jsxs("div",{className:"createTaskArea",style:{padding:"41px 37px",margin:0},children:[t.jsxs("div",{style:{display:"inline-flex",height:"808px"},children:[t.jsxs("div",{className:"default-review-lf",children:[t.jsx(x,{title:t.jsx("div",{className:"boldText",style:{fontSize:14},children:"原文对照"}),headStyle:{height:"52px",fontWeight:500,fontFamily:"HarmonyOS Sans SC Medium",minHeight:"unset"},bordered:!1,style:{width:"100%",height:"95%",textAlign:"start"},bodyStyle:{padding:"20px 24px"},children:He?t.jsx(t.Fragment,{children:t.jsx("div",{style:{height:"680px",textAlign:"start",marginTop:"16px",lineHeight:"26px",color:"#6D7279",overflowWrap:"break-word",whiteSpace:"pre-line",width:"100%",padding:"5px 10px"},children:t.jsx(u,{autoHide:!0,autoHideTimeout:1e3,autoHideDuration:200,children:t.jsx(b,{text:He,highlights:X})})})}):null}),t.jsxs("div",{className:"taskDetailBottom",children:["溯源置信度 :  ",t.jsx("span",{className:"span1",children:" 示例"}),"≥","90%    ",t.jsx("span",{className:"span2",children:" 示例"}),"≥","80%    ",t.jsx("span",{className:"span3",children:" 示例"}),"≥","70%"]})]}),t.jsx("div",{className:"default-review-rg",children:t.jsxs(x,{bordered:!1,size:"small",headStyle:{height:"52px"},title:t.jsx("div",{className:"boldText",style:{textAlign:"start"},children:"人工审核"}),extra:t.jsxs(r,{children:[t.jsx(g,{color:"#0FB698"}),t.jsxs("label",{style:{color:"#6D7279"},children:["已审核：",t.jsx("label",{style:{color:"#111111"},className:"boldText",children:xe.reviewCount??"0 "}),"个,"]}),t.jsxs("label",{style:{color:"#6D7279"},children:["剩余：",t.jsx("label",{style:{color:"#111111"},className:"boldText",children:Z-(xe.reviewCount??0)+" "}),"个"]})]}),style:{width:"100%",border:"unset"},bodyStyle:{height:714},children:[t.jsxs("div",{style:{display:"flex"},children:[t.jsxs(r,{direction:"vertical",style:{width:"100%",flex:"1",padding:"0 1rem"},size:18,children:[t.jsx("div",{style:{color:"#6D7279"},children:t.jsx("span",{className:"boldText",style:{fontSize:16,marginRight:32},children:"Step 1"})}),t.jsx("div",{style:{display:"flex",justifyContent:"space-between"},className:"boldText",children:"问题："}),t.jsx("div",{style:{color:"#6D7279"},children:"请审核生成的问题和答案内容是否正确，如不正确请对其进行编辑和修改，也可以将其删除"}),t.jsxs("div",{style:{position:"relative"},children:[t.jsx(p,{className:"review-textarea",value:P,style:{resize:"none",height:60},onChange:e=>{te||se(!0),De(!1),$(e.target.value)}}),t.jsx("div",{style:{position:"absolute",right:"0px",visibility:te?"inherit":"hidden"},children:t.jsxs(r,{children:[t.jsx(l,{type:"link",style:{padding:"4px 0"},onClick:()=>{const e={answer:M,id:O.id,question:P,taskId:xe.id};I(e).then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)&&xe&&Qe(xe.id)})),se(!1)},children:"保存"}),t.jsx(l,{type:"link",style:{padding:"4px 0"},onClick:()=>{$(O.question),se(!1)},children:"撤销"})]})})]}),t.jsx("div",{style:{display:"flex",justifyContent:"space-between"},className:"boldText",children:t.jsx("label",{children:"回答："})}),t.jsx("div",{style:{color:"#6D7279"},children:"请审核生成的问题和答案内容是否正确，如不正确请对其进行编辑和修改，也可以将其删除"}),t.jsxs("div",{style:{position:"relative"},children:[t.jsx(p,{className:"review-textarea",value:M,style:{resize:"none",height:148},onChange:e=>{ie||le(!0),De(!1),U(e.target.value)}}),t.jsx("div",{style:{position:"absolute",right:"0px",visibility:ie?"inherit":"hidden"},children:t.jsxs(r,{children:[t.jsx(l,{type:"link",style:{padding:"4px 0"},onClick:()=>{const e={answer:M,id:O.id,question:P,taskId:xe.id};I(e).then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)&&xe&&Qe(xe.id)})),le(!1)},children:"保存"}),t.jsx(l,{type:"link",style:{padding:"4px 0"},onClick:()=>{U(O.answer),le(!1)},children:"撤销"})]})})]}),t.jsxs(r,{size:19,children:[t.jsx(l,{type:"primary",className:Ce?"boldText review-btn-confirm":"boldText review-btn-default",style:{width:120},loading:ze,onClick:()=>{De(!0)},children:"确认无误"}),t.jsx(l,{type:"primary",className:"boldText review-btn-delete",style:{width:120},onClick:()=>{if((()=>{if(localStorage.getItem("ignoreConfirm")){const e=localStorage.getItem("lastRemindedDate"),t=(new Date).toLocaleDateString();return e!==t&&(localStorage.setItem("lastRemindedDate",t),!0)}return!0})())Se(!0);else{const e={qaDeleteInfoList:[{fileId:pe.get(O.id)??"",ids:[O.id]}],taskId:(null==xe?void 0:xe.id)??""};N(e).then((e=>{const t=O;t.id=null,R(t),Qe(xe.id)}))}},children:"删除本条"})]}),t.jsx("div",{className:"boldText",style:{color:"#6D7279",fontSize:16},children:"Step 2"}),t.jsx("div",{className:"boldText",children:"打分："}),t.jsx("div",{style:{color:"#6D7279"},children:"评价标准：请将生成数据的字符总长度、语言自然度作为主要指标进行主观打分评价"}),t.jsx(tt,{})]}),t.jsxs("div",{style:{width:"300px",padding:"0 0 1.25rem 1rem",borderLeft:"1px solid #EEF1F5"},children:[t.jsx(m,{showSearch:!0,style:{width:"100%",height:"40px",marginBottom:"20px"},value:ae,dropdownStyle:{maxHeight:400,overflow:"auto"},allowClear:!0,labelInValue:!0,treeDefaultExpandAll:!0,onChange:e=>{de(e)},onSelect:(e,t)=>{if(t.children&&0!==t.children.length){const e=Ae.filter((e=>e.fileTreeNodeForTask.fileId===t.fileId));e&&e.length>0&&Fe(e[0].fileTreeNodeForTask.children.map((e=>e.fileId)))}else{const e=Oe.filter((e=>e.fileId===t.fileId));Fe(e.map((e=>e.fileId)))}},treeData:fe,fieldNames:{label:"name",value:"fileId",children:"children"}}),t.jsx(o,{style:{height:588},children:t.jsx(f,{className:"qa-list",size:"small",dataSource:J,bordered:!1,renderItem:(e,s)=>t.jsx(f.Item,{style:{textAlign:"start",borderLeft:Xe(e)},onClick:()=>{R(e),V(e.id)},children:t.jsx("div",{className:"review-qa-list",children:t.jsx("label",{className:e.id===K?"current-qa":"",children:e.question})})},e.id)})})]})]}),t.jsx("div",{style:{bottom:"0.2rem",textAlign:"end",position:"absolute",right:"1rem"},children:t.jsx(r,{size:16,children:O.review?t.jsx(l,{type:"primary",className:"review-btn-default boldText",disabled:!0,onClick:()=>{Ye()},children:"已审核，下一组"}):t.jsxs(l,{type:"primary",className:"review-btn-default boldText",disabled:!be,onClick:async()=>{if(!O.modify&&te&&ie){Me(!0);const e={id:O.id,taskId:xe.id,score:be};await T(e),Je(xe.id),Me(!1)}else{le(!0),se(!0);const e={answer:M,id:O.id,question:P,taskId:xe.id};I(e).then((async e=>{var t,s;if(Me(!0),200===(null==(t=e.data)?void 0:t.code)&&xe){const e={id:O.id,taskId:xe.id,score:be};await T(e),Je(xe.id),Me(!1)}else j.error(null==(s=e.data)?void 0:s.message),Me(!1)}))}},children:["完成审核，下一条",$e&&t.jsx(v,{})]})})})]})})]}),t.jsx("div",{style:{marginTop:-31,display:"flex",justifyContent:"flex-end"},children:t.jsxs(r,{children:[t.jsx(y,{size:"default",checked:Ne,onChange:Te}),t.jsx("label",{style:{color:"#6D7279"},children:"审核完成后自动切换下一条"})]})})]})]}),t.jsx(A,{visible:ye,OnClose:(e,t)=>{if(Se(!1),t){localStorage.setItem("ignoreConfirm","true");const e=(new Date).toLocaleDateString();localStorage.setItem("lastRemindedDate",e)}if(e){const e={qaDeleteInfoList:[{fileId:pe.get(O.id)??"",ids:[O.id]}],taskId:(null==xe?void 0:xe.id)??""};N(e).then((e=>{const t=O;t.id=null,R(t),Qe(xe.id)}))}}})]})};export{B as default};

import { Space, Select, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import React from 'react';

export interface DatasetTableHeaderProps {
  filterAttribute: string;
  setFilterAttribute: (val: string) => void;
  filterInput: string;
  setFilterInput: (val: string) => void;
  sortDirection: string;
  setSortDirection: (val: string) => void;
  Extra?: React.ReactNode;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const DatasetTableHeader: React.FC<DatasetTableHeaderProps> = ({
  filterAttribute,
  setFilterAttribute,
  filterInput,
  setFilterInput,
  sortDirection,
  setSortDirection,
  Extra,
  handleChange
}) => (
  <div
    style={{
      textAlign: 'start',
      justifyContent: 'space-between',
      display: 'inline-flex',
      marginBottom: '1rem',
      width: '100%',
    }}
  >
    <div>
      <Space size="small">
        <Space.Compact>
          <Select
            size="large"
            className="filter-select"
            value={filterAttribute}
            onChange={setFilterAttribute}
            options={[
              { value: 'datasetName', label: '数据集' },
              { value: 'tag', label: '标签' },
            ]}
          />
          <Input
            size="large"
            className="filter-input"
            suffix={<SearchOutlined />}
            placeholder={`请输入${filterAttribute === 'datasetName' ? '数据集' : '标签'}名称`}
            value={filterInput}
            onChange={handleChange}
          />
        </Space.Compact>
        <Select
          size="large"
          className="filter-select"
          value={sortDirection}
          onChange={setSortDirection}
          style={{ width: '9.75rem' }}
          options={[
            { value: 'desc', label: '按时间倒序' },
            { value: 'asc', label: '按时间正序' },
          ]}
        />
      </Space>
    </div>
    {Extra}
  </div>
);

export default DatasetTableHeader; 
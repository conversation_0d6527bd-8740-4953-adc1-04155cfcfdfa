# 使用Node.js镜像作为基础镜像
FROM node:latest AS build-env

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json文件到工作目录
COPY package*.json ./

# 安装项目依赖
RUN npm install --legacy-peer-deps

# 复制React项目文件到工作目录
COPY . .

# 构建React项目
RUN npm run build

# 使用Nginx作为基础镜像
FROM nginx:alpine

# 将React项目静态资源复制到Nginx的默认站点目录
COPY --from=build-env /app/build /usr/share/nginx/html

# 暴露Nginx容器的80端口
EXPOSE 80

# 启动Nginx服务
CMD ["nginx", "-g", "daemon off;"]
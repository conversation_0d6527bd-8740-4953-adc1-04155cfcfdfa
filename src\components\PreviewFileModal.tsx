import { <PERSON><PERSON>, <PERSON>, Modal, Row, Space, TreeDataNode, message } from "antd";
import { RollbackOutlined, DoubleLeftOutlined } from "@ant-design/icons";
import { DatasetFileEnum, DataSetTreeType, ParseDatasetType } from "../types";
import { useEffect, useState } from "react";
// @ts-ignore
import FileViewer from "react-file-viewer";
import Scrollbar from "react-scrollbars-custom";
import "../css/PreviewFileModal.css";
import { getDataFilePreview } from "../api/dataset";
import ReactMarkdown from 'react-markdown';
import { generateId } from "./ocrReview/tree";
import DirectoryTree from "antd/es/tree/DirectoryTree";
// import  * as mammoth from 'mammoth';

interface Heading {
  key: string;
  level: number;
  title: string;
}

interface PreviewFileModalProp {
  visible: boolean;
  datasetName?: string;
  fileData: DataSetTreeType;
  OnClose: () => void;
}

const PreviewFileModal: React.FC<PreviewFileModalProp> = ({
  visible,
  fileData,
  datasetName,
  OnClose,
}) => {
  const [fileParseData, setFileParseData] = useState<ParseDatasetType>();

  const [activeBtn, setActiveBtn] = useState(0);
  const [fileType, setFileType] = useState<number>();
  // pdf tree
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]);

  const [fileDoc, setFileDoc] = useState<string>();

  const getFileType = (fileTypeStr: string) => {
    let fileType = 0;
    // if (fileName) {
    //   const fileTypeStr = fileName.split('.')[1];
    if (fileTypeStr === 'txt') {
      fileType = DatasetFileEnum.Txt;
    } else if (fileTypeStr === 'doc' || fileTypeStr === 'docx') {
      fileType = DatasetFileEnum.Doc;
    } else if (fileTypeStr === 'pdf') {
      fileType = DatasetFileEnum.Pdf;
    } else if (fileTypeStr === 'csv') {
      fileType = DatasetFileEnum.Csv;
    } else if (fileTypeStr === 'xlsx') {
      fileType = DatasetFileEnum.Xlsx;
    }
    // }
    return fileType;
  }

  useEffect(() => {
    console.log(fileData);
    const fileId = fileData.fileId;
    if (fileId) {
      getDataFilePreview(fileId).then((res) => {
        if (res.data.code === 200) {
          const data = res.data.data;
          setFileParseData(data);
          // setFilePath(data.srcFilePath);
          const newFileType = getFileType(data.fileType);
          setFileType(newFileType);
          if (newFileType === DatasetFileEnum.Txt || newFileType === DatasetFileEnum.Csv) {
            fetch(data?.srcFilePath!)
              .then(response => response.text())
              .then(text => {
                setFileDoc(text);
                // 使用 decodedText 进行后续操作
              })
              .catch(error => message.error('文件获取失败'));
          } else if (newFileType === DatasetFileEnum.Pdf || newFileType === DatasetFileEnum.Doc || newFileType === DatasetFileEnum.Xlsx) {
            fetch(data?.parseFilePath!)
              .then(response => response.text())
              .then(text => {
                setFileDoc(text);
                // 使用 decodedText 进行后续操作
                console.log(text);
                setTreeData(convertToTree(extractHeadingsFromMarkdown(text)));
              })
              .catch(error => message.error('文件获取失败'));
          }
        }
      })
    }

  }, [fileData])

  function resetData() {
    setFileParseData(undefined);
    setActiveBtn(0);
    setFileType(undefined);
    setTreeData([]);
    setFileDoc(undefined);
  }

  function extractHeadingsFromMarkdown(markdown: string): Heading[] {
    const lines = markdown.split('\n');
    const headings: Heading[] = [];

    lines.forEach(line => {
      const match = line.match(/^#+\s+(.+)/);
      if (match) {
        const level = match[0].split(' ')[0].length;
        const title = match[1];
        headings.push({ key: generateId(), level, title });
      }
    });

    return headings;
  }

  const convertToTree = (headings: Heading[]): TreeDataNode[] => {
    const tree: TreeDataNode[] = [];
    const map: Record<string, TreeDataNode> = {};

    headings.forEach((heading) => {
      const { title, key } = heading;
      const treeNode: TreeDataNode = { title, key, children: [] };

      map[heading.key] = treeNode;

      if (heading.level === 1) {
        // If it's a level 1 heading, add it directly to the tree
        tree.push(treeNode);
      } else {
        // If it's a nested heading, add it as a child of its parent
        const parentKey = headings.find((h) => h.level === heading.level - 1)?.key;
        const parent = map[parentKey!];

        if (parent) {
          if (!parent.children) {
            parent.children = [];
          }

          parent.children.push(treeNode);
        }
      }
    });

    return tree;
  };

  const PreviewFile: React.FC = () => {
    if (fileType === DatasetFileEnum.Txt) {
      return (
        <iframe
          srcDoc={fileDoc}
          width={"100%"}
          height={"550px"}
          style={{ border: "unset" }}
        />
      );
    } else if (fileType === DatasetFileEnum.Pdf) {
      if (activeBtn === 1) {
        return <FileViewer fileType={'pdf'} key="pdf-preview"
          filePath={fileParseData?.srcFilePath!}
          errorComponent={Error}
          unsupportedComponent={Error}
        />
      } else {
        return (
          <ReactMarkdown className={'react-mark-down'}>{fileDoc}</ReactMarkdown>
        );
      }
    } else if (fileType === DatasetFileEnum.Doc) {
      if (activeBtn === 1) {
        return <FileViewer
          fileType={"docx"}
          key="docx-preview"
          filePath={fileParseData?.srcFilePath!}
          // onError={this.onError}
          errorComponent={Error}
          unsupportedComponent={Error}
        />
      } else {
        return <ReactMarkdown>{fileDoc}</ReactMarkdown>
      }
    } else if (fileType === DatasetFileEnum.Csv) {
      if (activeBtn === 1) {
        return <FileViewer
          fileType={"csv"}
          key="csv-preview"
          filePath={fileParseData?.srcFilePath!}
          // onError={this.onError}
          errorComponent={Error}
          unsupportedComponent={Error}
        />
      } else {
        return <ReactMarkdown>{fileDoc}</ReactMarkdown>
      }
    } else if (fileType === DatasetFileEnum.Xlsx) {
      if(activeBtn === 1){
        return <FileViewer
          fileType={"xlsx"}
          key="xlsx-preview"
          filePath={fileParseData?.srcFilePath!}
          // onError={this.onError}
          errorComponent={Error}
          unsupportedComponent={Error}
        />
      } else {
        return <ReactMarkdown>{fileDoc}</ReactMarkdown>
      }
    } else {
      return null;
    }
  };

  return (
    <Modal
      centered
      destroyOnClose
      title={<div className="modal-title">
        <Button
          size="large"
          className="default-btn back-btn"
          onClick={() => {
            OnClose();
            resetData();
          }}
        >
          <RollbackOutlined />
          返回
        </Button>
        <label>数据集预览</label>
      </div>}
      keyboard={false}
      maskClosable={false}
      width={"1200px"}
      style={{ height: "728px" }}
      open={visible}
      onOk={() => {
        OnClose();
        resetData();
      }}
      onCancel={() => {
        OnClose();
        resetData();
      }}
      footer={[]}
    >
      <div className="preview-file-modal">
        <div>
          <div style={{
            color: '#8E98A7',
            fontSize: '16px',
            fontStyle: 'normal',
            fontWeight: '400',
            lineHeight: 'normal',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            层级视图
            <Button type="text" size="small">
              <DoubleLeftOutlined style={{ color: '#8E98A7' }} />
            </Button>
          </div>
          <DirectoryTree
            height={560}
            expandAction={false}
            defaultExpandAll
            blockNode
            treeData={treeData}
            showIcon={false}
          />
        </div>
        <div className="preview-file-main">
          <div style={{ marginBottom: "24px", fontSize: "14px" }}>
            <label className="upload-error-label">{datasetName}{" "}/{" "}</label>
            <label className="mediumText">{fileData?.name}</label>
          </div>
          <div style={{ height: "560px" }}>
            <Scrollbar>
              <PreviewFile />
            </Scrollbar>
          </div>
        </div>
        <div className="preview-switch-btn">
          <div
            onClick={() => setActiveBtn(0)}
            className={
              activeBtn === 0
                ? "preview-switch-btn-item active mediumText"
                : "preview-switch-btn-item"
            }
          >
            解析后
          </div>
          <div
            onClick={() => setActiveBtn(1)}
            className={
              activeBtn === 1
                ? "preview-switch-btn-item active mediumText"
                : "preview-switch-btn-item"
            }
          >
            原文档
          </div>
        </div>
      </div>
      {/* </Scrollbar> */}
    </Modal>
  );
};

export default PreviewFileModal;

import{k as e,r as t,K as s,j as a,ag as l,D as i,f as n,e as r,B as d,ae as o,af as c,t as h,ah as x,ai as m,S as p,aj as u,ak as f,M as j,ac as g,v as k,I as v,al as w,am as y,a6 as C,an as S}from"./react-core-BrQk45h1.js";import{D as b,a as N}from"./types-ccCJOgIs.js";import{d as H,g as E,a as F,b as A,c as V,r as I}from"./dataset-CNkWbBvO.js";import{T as L}from"./UploadList-CCFxdIyn.js";import{d as T}from"./task-ClBf-SyM.js";import{U as M}from"./UploadErrorModal-Dpa2hJTx.js";import{e as O,T as B}from"./empty-logo-5H_PPUCG.js";const D=({rowData:x,tags:m,OnRename:p,OnChange:u,OnDelete:f,OnEdit:j})=>{const g=e(),[k,v]=t.useState([]),[w,y]=t.useState(!1),[C,S]=t.useState(!1),[N,E]=t.useState(),[F,A]=t.useState(""),{confirm:V}=s,[I,L]=t.useState([]);t.useEffect((()=>{let e=[];x.relatedQATaskList&&x.relatedQATaskList.length>0&&(e=x.relatedQATaskList.map((e=>({key:e.taskId,label:e.taskName})))),L(e)}),[]),t.useEffect((()=>{const e=[{key:"addTag",label:"增加标签"}];m.forEach((t=>{e.push({key:t,label:t})})),v(e)}),[m]);const M=e=>{g(`/main/task/detail/${e.key}`)},O=e=>{e&&I&&I.length?y(e):e||y(e)},B=e=>{if("tag"===e.key)E(["tag"]);else if("rename"===e.key)p(),S(!1);else if("delete"===e.key){S(!1);const e=x.relatedQATaskList.length>0;let t=[];e&&(t=[x].map((e=>e.relatedQATaskList.map((t=>({...t,datasetName:e.name,dataSetId:e.id}))))).reduce(((e,t)=>e.concat(t))));const s=[{title:"数据集名称",dataIndex:"datasetName",key:"datasetName",render:(e,t)=>{const{datasetName:s}=t;return a.jsx("a",{className:"dataset-name",children:s})}},{title:"任务名称",dataIndex:"name",key:"name",render:(e,t)=>{const{taskName:s,taskId:i}=t;return a.jsx("a",{className:"dataset-name",onClick:e=>{e.preventDefault(),g(`/main/task/detail/${i}`),l.destroy()},children:s})}},{title:"Action",key:"action",render:(e,{taskId:s})=>a.jsxs(n,{children:[a.jsx(d,{type:"link",style:{color:"#0fb698",fontFamily:"HarmonyOS Sans SC Reqular"},onClick:()=>{g(`/main/task/detail/${s}`),l.destroy()},children:"详情"}),a.jsx(d,{type:"link",style:{color:"#0fb698",fontFamily:"HarmonyOS Sans SC Reqular"},onClick:()=>{const e=V({centered:!0,title:"删除提示",icon:a.jsx(c,{}),width:540,content:a.jsx(a.Fragment,{children:a.jsx("div",{className:"default-info",style:{color:"black"},children:"确定要删除所选任务吗？"})}),footer:[a.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",padding:"2rem 0 0 0",gap:"8px"},children:[a.jsx(d,{type:"text",onClick:()=>e.destroy(),shape:"round",children:"取消"}),a.jsx(d,{type:"primary",onClick:()=>{t=t.filter((e=>e.taskId!==s)),T([s]).then((t=>{200===t.data.code?(e.destroy(),l.destroy()):h.error(t.data.message)}))},shape:"round",className:"primary-btn",style:{width:"120px"},children:"确认删除"})]})],async onOk(){t=t.filter((e=>e.taskId!==s)),await T([s]).then((t=>{200===t.data.code?(e.destroy(),l.destroy()):h.error(t.data.message)}))},onCancel(){e.destroy()}})},children:"删除"})]})}],l=V({centered:!0,title:"删除提示",icon:a.jsx(c,{}),width:540,content:a.jsxs(a.Fragment,{children:[a.jsx("div",{className:"default-info",style:{color:"black"},children:"确定要删除所选数据集吗？"}),e?a.jsxs(a.Fragment,{children:[a.jsx("div",{className:"upload-error-label",style:{marginTop:"8px"},children:"所选源数据集有关联的推理任务，无法进行删除。如需删除，请先删除关联任务。"}),a.jsx(o,{scroll:{y:400},size:"small",pagination:!1,columns:s,dataSource:t,style:{flex:1},tableLayout:"fixed",rowKey:"taskId",className:"dataset-table"})]}):null]}),footer:[a.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",padding:"2rem 0 0 0",gap:"8px"},children:[a.jsx(d,{type:"text",onClick:()=>l.destroy(),shape:"round",children:"取消"}),a.jsx(d,{disabled:e,type:"primary",onClick:()=>{f([x.id]),H([x.id]).then((e=>{l.destroy()}))},shape:"round",className:"primary-btn",style:{width:"120px"},children:"确认删除"})]})],onOk(){f([x.id]),H([x.id]).then((e=>{l.destroy()}))},onCancel(){l.destroy()}})}},D=e=>{S(e)};let P=[{key:"rename",label:"重命名"},{key:"delete",label:"删除"}];return x.datasetStatus===b.sucess?a.jsxs("div",{style:{display:"inline-flex",justifyContent:"space-around",width:"100%"},children:[I&&0!==(null==I?void 0:I.length)?a.jsx(i,{menu:{items:I,onClick:M},onOpenChange:O,open:w,children:a.jsx("a",{onClick:e=>e.preventDefault(),children:a.jsxs(n,{children:["相关任务",a.jsx(r,{})]})})}):a.jsxs("a",{children:[a.jsx(l,{}),a.jsx(l,{})]}),a.jsx(i,{menu:{items:P,onClick:B},onOpenChange:D,open:C,children:a.jsx("a",{onClick:e=>e.preventDefault(),children:a.jsxs(n,{children:["更多",a.jsx(r,{})]})})})]}):a.jsxs("div",{style:{display:"inline-flex",justifyContent:"space-around",width:"100%"},children:[a.jsxs("a",{children:[a.jsx(l,{}),a.jsx(l,{})]}),a.jsx(i,{menu:{items:P,onClick:B},onOpenChange:D,open:C,children:a.jsx("a",{onClick:e=>e.preventDefault(),children:a.jsxs(n,{children:["更多",a.jsx(r,{})]})})})]})};function P(){return Math.random().toString(36).substring(2,10)}const R=({visible:e,fileData:l,datasetName:i,OnClose:n})=>{const[r,o]=t.useState(),[c,g]=t.useState(0),[k,v]=t.useState(),[w,y]=t.useState([]),[C,S]=t.useState();function b(){o(void 0),g(0),v(void 0),y([]),S(void 0)}t.useEffect((()=>{const e=l.fileId;e&&E(e).then((e=>{if(200===e.data.code){const t=e.data.data;o(t);const s=(e=>{let t=0;return"txt"===e?t=N.Txt:"doc"===e||"docx"===e?t=N.Doc:"pdf"===e?t=N.Pdf:"csv"===e?t=N.Csv:"xlsx"===e?t=N.Xlsx:"json"===e?t=N.Json:"md"===e?t=N.Markdown:"png"!==e&&"jpg"!==e&&"jpeg"!==e||(t=N.Image),t})(t.fileType);v(s),s===N.Txt||s===N.Csv?fetch(null==t?void 0:t.srcFilePath).then((e=>e.text())).then((e=>{S(e)})).catch((e=>h.error("文件获取失败"))):s===N.Pdf||s===N.Doc||s===N.Xlsx||s===N.Markdown||s===N.Image?fetch(null==t?void 0:t.parseFilePath).then((e=>e.text())).then((e=>{S(e),y(H(function(e){const t=e.split("\n"),s=[];return t.forEach((e=>{const t=e.match(/^#+\s+(.+)/);if(t){const e=t[0].split(" ")[0].length,a=t[1];s.push({key:P(),level:e,title:a})}})),s}(e)))})).catch((e=>h.error("文件获取失败"))):s===N.Json&&fetch(null==t?void 0:t.srcFilePath).then((e=>e.json())).then((e=>{S(e)})).catch((e=>h.error("文件获取失败")))}}))}),[l]);const H=e=>{const t=[],s={};return e.forEach((a=>{var l;const{title:i,key:n}=a,r={title:i,key:n,children:[]};if(s[a.key]=r,1===a.level)t.push(r);else{const t=null==(l=e.find((e=>e.level===a.level-1)))?void 0:l.key,i=s[t];i&&(i.children||(i.children=[]),i.children.push(r))}})),t},F=()=>k===N.Txt?a.jsx("iframe",{srcDoc:C,width:"100%",height:"550px",style:{border:"unset"}}):k===N.Pdf?1===c?a.jsx(f,{fileType:"pdf",filePath:null==r?void 0:r.srcFilePath,errorComponent:Error,unsupportedComponent:Error},"pdf-preview"):a.jsx(j,{className:"react-mark-down",children:C}):k===N.Doc?1===c?a.jsx(f,{fileType:"docx",filePath:null==r?void 0:r.srcFilePath,errorComponent:Error,unsupportedComponent:Error},"docx-preview"):a.jsx(j,{children:C}):k===N.Csv?1===c?a.jsx(f,{fileType:"csv",filePath:null==r?void 0:r.srcFilePath,errorComponent:Error,unsupportedComponent:Error},"csv-preview"):a.jsx(j,{children:C}):k===N.Xlsx?1===c?a.jsx(f,{fileType:"xlsx",filePath:null==r?void 0:r.srcFilePath,errorComponent:Error,unsupportedComponent:Error},"xlsx-preview"):a.jsx(j,{children:C}):k===N.Json?1===c?a.jsx("div",{style:{whiteSpace:"pre-wrap"},children:a.jsx("code",{children:"string"==typeof C?C:JSON.stringify(C)})}):a.jsx("div",{style:{whiteSpace:"pre-wrap"},children:a.jsx("code",{children:JSON.stringify(C,null,2)})}):k===N.Markdown?1===c?a.jsx("div",{children:C}):a.jsx(j,{children:C}):k===N.Image?1===c?a.jsx("img",{src:null==r?void 0:r.srcFilePath,alt:"图片预览"}):a.jsx(j,{children:C}):null;return a.jsx(s,{centered:!0,destroyOnClose:!0,title:a.jsxs("div",{className:"modal-title",children:[a.jsxs(d,{size:"large",className:"default-btn back-btn",onClick:()=>{n(),b()},children:[a.jsx(u,{}),"返回"]}),a.jsx("label",{children:"数据集预览"})]}),keyboard:!1,maskClosable:!1,width:"1200px",style:{height:"728px"},open:e,onOk:()=>{n(),b()},onCancel:()=>{n(),b()},footer:[],children:a.jsxs("div",{className:"preview-file-modal",children:[a.jsxs("div",{children:[a.jsxs("div",{style:{color:"#8E98A7",fontSize:"16px",fontStyle:"normal",fontWeight:"400",lineHeight:"normal",display:"flex",alignItems:"center",justifyContent:"space-between"},children:["层级视图",a.jsx(d,{type:"text",size:"small",children:a.jsx(x,{style:{color:"#8E98A7"}})})]}),a.jsx(m,{height:560,expandAction:!1,defaultExpandAll:!0,blockNode:!0,treeData:w,showIcon:!1})]}),a.jsxs("div",{className:"preview-file-main",children:[a.jsxs("div",{style:{marginBottom:"24px",fontSize:"14px"},children:[a.jsxs("label",{className:"upload-error-label",children:[i," ","/"," "]}),a.jsx("label",{className:"mediumText",children:null==l?void 0:l.name})]}),a.jsx("div",{style:{height:"560px"},children:a.jsx(p,{children:a.jsx(F,{})})})]}),a.jsxs("div",{className:"preview-switch-btn",children:[a.jsx("div",{onClick:()=>g(0),className:0===c?"preview-switch-btn-item active mediumText":"preview-switch-btn-item",children:"解析后"}),a.jsx("div",{onClick:()=>g(1),className:1===c?"preview-switch-btn-item active mediumText":"preview-switch-btn-item",children:"原文档"})]})]})})},z="data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M3.4375%2016.25H16.5625C17.0803%2016.25%2017.5%2015.9702%2017.5%2015.625V6.625C17.5%206.07272%2017.0523%205.625%2016.5%205.625H10L7.8125%203.75L3.4375%203.75C2.91973%203.75%202.5%204.02982%202.5%204.375V15.625C2.5%2015.9702%202.91973%2016.25%203.4375%2016.25Z'%20fill='%23FFE7B8'%20stroke='%23FBBF78'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e",Z=({dataSet:e,visible:l,OnClose:i})=>{const[r,o]=t.useState(),[c,h]=t.useState([]),[x,m]=t.useState(),[f,j]=t.useState(!1),[k,v]=t.useState(new Object),[w,y]=t.useState([]),C=[z,"data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M4.8125%2018H16.1875C16.6362%2018%2017%2017.6418%2017%2017.2V6H12.9375V2H4.8125C4.36377%202%204%202.35817%204%202.8V17.2C4%2017.6418%204.36377%2018%204.8125%2018Z'%20fill='%23F1F4F9'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M13%202L17%206'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3crect%20x='8'%20y='10'%20width='12'%20height='10'%20rx='2'%20fill='%23818DB8'/%3e%3cpath%20d='M11.5599%2012.144H16.4399V13.352H14.6799V18H13.3199V13.352H11.5599V12.144Z'%20fill='white'/%3e%3c/svg%3e","data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M4.8125%2018H16.1875C16.6362%2018%2017%2017.6418%2017%2017.2V6H12.9375V2H4.8125C4.36377%202%204%202.35817%204%202.8V17.2C4%2017.6418%204.36377%2018%204.8125%2018Z'%20fill='%23F1F4F9'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M13%202L17%206'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3crect%20x='8'%20y='10'%20width='12'%20height='10'%20rx='2'%20fill='%23325DF4'/%3e%3cpath%20d='M14.5784%2012.144L15.6984%2015.744L16.7304%2012.144H18.0424L16.1944%2018H15.1464L14.0104%2014.504L12.9064%2018H11.8664L9.94644%2012.144H11.3544L12.4504%2015.744L13.5064%2012.144H14.5784Z'%20fill='white'/%3e%3c/svg%3e","data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M4.8125%2018H16.1875C16.6362%2018%2017%2017.6418%2017%2017.2V6H12.9375V2H4.8125C4.36377%202%204%202.35817%204%202.8V17.2C4%2017.6418%204.36377%2018%204.8125%2018Z'%20fill='%23F1F4F9'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M13%202L17%206'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3crect%20x='8'%20y='10'%20width='12'%20height='10'%20rx='2'%20fill='%23F44932'/%3e%3cpath%20d='M14.0671%2012.144C14.5578%2012.144%2014.9844%2012.2267%2015.3471%2012.392C15.7098%2012.552%2015.9871%2012.7813%2016.1791%2013.08C16.3764%2013.3787%2016.4751%2013.728%2016.4751%2014.128C16.4751%2014.528%2016.3764%2014.8773%2016.1791%2015.176C15.9871%2015.4747%2015.7098%2015.7067%2015.3471%2015.872C14.9844%2016.032%2014.5578%2016.112%2014.0671%2016.112H13.3151V18H11.9471V12.144H14.0671ZM13.9151%2014.96C14.2458%2014.96%2014.5151%2014.896%2014.7231%2014.768C14.9311%2014.64%2015.0351%2014.4267%2015.0351%2014.128C15.0351%2013.8347%2014.9284%2013.624%2014.7151%2013.496C14.5071%2013.3627%2014.2404%2013.296%2013.9151%2013.296H13.3151V14.96H13.9151Z'%20fill='white'/%3e%3c/svg%3e","data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M4.8125%2018H16.1875C16.6362%2018%2017%2017.6418%2017%2017.2V6H12.9375V2H4.8125C4.36377%202%204%202.35817%204%202.8V17.2C4%2017.6418%204.36377%2018%204.8125%2018Z'%20fill='%23F1F4F9'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M13%202L17%206'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3crect%20x='8'%20y='10'%20width='12'%20height='10'%20rx='2'%20fill='%2326BC74'/%3e%3cpath%20d='M15.775%2015.64H13.487V16.816H16.135V18H12.119V12.144H16.039V13.32H13.487V14.472H15.775V15.64Z'%20fill='white'/%3e%3c/svg%3e","data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M4.8125%2018H16.1875C16.6362%2018%2017%2017.6418%2017%2017.2V6H12.9375V2H4.8125C4.36377%202%204%202.35817%204%202.8V17.2C4%2017.6418%204.36377%2018%204.8125%2018Z'%20fill='%23F1F4F9'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M13%202L17%206'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3crect%20x='8'%20y='10'%20width='12'%20height='10'%20rx='2'%20fill='%23F49B32'/%3e%3cpath%20d='M13.6451%2018.104C13.1811%2018.104%2012.7811%2017.992%2012.4451%2017.768C12.1091%2017.544%2011.8904%2017.2%2011.7891%2016.736L12.9331%2016.136C12.9917%2016.376%2013.0771%2016.5573%2013.1891%2016.68C13.3064%2016.7973%2013.4637%2016.856%2013.6611%2016.856C13.9064%2016.856%2014.0797%2016.7627%2014.1811%2016.576C14.2824%2016.384%2014.3331%2016.0827%2014.3331%2015.672V12.144H15.7011V15.808C15.7011%2016.624%2015.5064%2017.2107%2015.1171%2017.568C14.7331%2017.9253%2014.2424%2018.104%2013.6451%2018.104Z'%20fill='white'/%3e%3c/svg%3e","data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M4.15346%2017.1427H15.6535C16.1071%2017.1427%2016.4749%2016.7749%2016.4749%2016.3213V5.77365H12.3677V1.6665H4.15346C3.6998%201.6665%203.33203%202.03427%203.33203%202.48793V16.3213C3.33203%2016.7749%203.6998%2017.1427%204.15346%2017.1427Z'%20fill='%23F1F4F9'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M12.3672%201.6665L16.4743%205.77365'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3crect%20x='7.66797'%20y='8.28564'%20width='11.4286'%20height='11.4286'%20rx='2'%20fill='%23888888'%20fill-opacity='0.9'/%3e%3cpath%20d='M15.3197%2011.144H16.3517V17H15.0557V13.528L13.7357%2015.416H13.0397L11.7037%2013.512V17H10.4237V11.144H11.4797L13.4077%2013.968L15.3197%2011.144Z'%20fill='white'/%3e%3c/svg%3e"];t.useEffect((()=>{e&&e.id&&(o(e.datasetName),F(e.id).then((e=>{var t;if(200===(null==(t=e.data)?void 0:t.code)){const t=e.data.data;t.children&&t.children.length>0&&h(t.children)}})))}),[e]);const S=e=>{const t=e.split(".").pop();let s=0;return"txt"===t?s=N.Txt:"doc"===t||"docx"===t?s=N.Doc:"pdf"===t?s=N.Pdf:"csv"===t||"xlsx"===t?s=N.Csv:"json"===t?s=N.Json-1:"md"===t?s=N.Markdown-1:"png"!==t&&"jpg"!==t&&"jpeg"!==t||(s=N.Json-1),C[s]},b=({data:e})=>"FILE"!==e.type?a.jsxs("div",{className:"preview-list-item-label",onClick:()=>{e.children&&e.children.length>0&&H(e)},children:[a.jsx("img",{src:z}),a.jsx("label",{children:e.name})]}):a.jsxs("div",{className:"preview-list-item-label",onClick:()=>{v(e),j(!0),sessionStorage.setItem("fileId",e.fileId)},children:[a.jsx("img",{src:S(e.name)}),a.jsx("label",{children:e.name})]}),H=e=>{if(m(e),o(`${r}/${e.name}`),e.children){const t=w;t.push(c),y(t),h(e.children)}};return a.jsxs(s,{centered:!0,destroyOnClose:!0,className:"preview-folder-modal",title:"数据集预览",keyboard:!1,maskClosable:!1,styles:{body:{height:"628px"}},width:"870px",open:l,onOk:()=>{i(),h([])},onCancel:()=>{i(),h([])},footer:[],children:[a.jsxs("div",{className:"preview-header",children:[w.length>0?a.jsxs(d,{className:"preview-back-btn",onClick:()=>{x&&(e=>{o(`${null==r?void 0:r.split(`/${e}`)[0]}`);const t=w,s=t.pop();s&&(h(s),y(t))})(x.name)},children:[a.jsx(u,{}),"上一层"]}):null,a.jsx("span",{className:"mediumText",children:r})]}),a.jsxs(p,{style:{height:570},children:[a.jsx(g,{size:"large",bordered:!0,dataSource:c,renderItem:e=>a.jsxs(g.Item,{className:"preview-list-item",children:[a.jsx(b,{data:e}),"FILE"===e.type?a.jsxs(n,{size:4,className:"preview-list-item-btn",children:[a.jsx(d,{type:"link",size:"small",onClick:()=>{v(e),j(!0)},children:"预览"}),a.jsx(d,{type:"link",size:"small",children:"删除"})]}):null]})}),a.jsx(R,{visible:f,datasetName:r,OnClose:()=>{j(!1)},fileData:k})]})]})},$=e=>{const{dataSet:s,datasets:l,index:i,renameRows:n,setRenameRows:r,setDatasets:o,handleRefresh:c}=e,[x,m]=t.useState(s.datasetName);return a.jsxs(a.Fragment,{children:[a.jsx(v,{placeholder:"请输入数据集名称",value:x,onChange:e=>{e.target.value.length>0?m(e.target.value):m("")}}),a.jsx(d,{icon:a.jsx(C,{}),onClick:()=>{const e=(null==x?void 0:x.trim())||"";if(e&&e.length>0){const t=l,a=t[i].name=e,d=n;d.delete(s.id),r(d),m(""),o(t);const h=t[i].id;I({datasetId:h,datasetName:a}).then((e=>{c()}))}else h.error("数据集名称不合法")}}),a.jsx(d,{icon:a.jsx(S,{}),onClick:()=>{const e=n;e.delete(s.id),r(e),m("")}})]})},J=t.forwardRef(((e,s)=>{const{ShowActionColumn:l,Extra:i,onSelectRows:r,noEditTag:c}=e;t.useImperativeHandle(s,(()=>({onRefresh:de,handleDeleteRows:oe,removeSelection:e=>{const t=H.filter((t=>t.id!==e)),s=F.filter((t=>t!==e));E(t),I(s);const a=new Map(g),l=new Set(S);a.delete(e),l.delete(e),C(a),N(l);const i=Array.from(a.values());r(i)}})));const[x,m]=t.useState(!1),[p,u]=t.useState(new Object),[f,j]=t.useState(!1),[g,C]=t.useState(new Map),[S,N]=t.useState(new Set),[H,E]=t.useState([]),[F,I]=t.useState([]),[T,P]=t.useState(new Set),[R,z]=t.useState([]),[J,Q]=t.useState({page:1,size:10,total:0}),[U,K]=t.useState("datasetName"),[X,q]=t.useState("createTime"),[G,W]=t.useState("desc"),[Y,_]=t.useState(""),ee=t.useRef(),[te,se]=t.useState(!1),[ae,le]=t.useState(0),[ie,ne]=t.useState(!1);function re(){if(!te){se(!0);const e={...J,sortAttribute:X,sortDirection:G,datasetName:"datasetName"===U?Y:"",tag:"datasetName"!==U?Y:"",startTime:"",endTime:""};A(e).then((e=>{var t,s,a;se(!1),200===(null==(t=e.data)?void 0:t.code)&&(z(null==(s=e.data)?void 0:s.data),Q({...J,total:null==(a=e.data)?void 0:a.totalCount}),le((J.page-1)*J.size))}))}}t.useEffect((()=>{}),[p]),t.useEffect((()=>{if(R.length>0){const e=[],t=[];R.forEach((s=>{S.has(s.id)&&(e.push(s),t.push(s.id))})),E(e),I(t)}}),[R,S]);const de=async()=>{var e,t,s;const a={...J,sortAttribute:X,sortDirection:G,datasetName:"datasetName"===U?Y:"",tag:"datasetName"!==U?Y:"",startTime:"",endTime:""},l=await A(a);200===(null==(e=l.data)?void 0:e.code)&&(z(null==(t=l.data)?void 0:t.data),Q({...J,total:null==(s=l.data)?void 0:s.totalCount}))},oe=e=>{z((t=>t.filter((t=>!e.includes(t.id)))))};t.useEffect((()=>{re()}),[]),t.useEffect((()=>{re()}),[J.page,J.size,G,U,X,Y]);const ce={columnWidth:"32px",selectedRowKeys:F,onChange:(e,t)=>{I(e),E(t);const s=new Map(g),a=new Set(S);R.forEach((e=>{s.delete(e.id),a.delete(e.id)})),t.forEach((e=>{s.set(e.id,e),a.add(e.id)})),C(s),N(a);const l=Array.from(s.values());r(l)},getCheckboxProps:e=>({name:e.name,disabled:e.datasetStatus!==b.sucess})},he=[{key:"index",title:"序号",dataIndex:"index",render:(e,t,s)=>a.jsx(a.Fragment,{children:ae+s+1}),width:"4.6%"},{key:"name",title:"数据集名称",dataIndex:"datasetName",ellipsis:!0,shouldCellUpdate:(e,t)=>!0,render:(e,t,s)=>{let{id:l,datasetName:i,childFiles:r}=t;return T.has(l)?a.jsx(a.Fragment,{children:a.jsx(n.Compact,{children:a.jsx($,{dataSet:t,datasets:R,index:s,renameRows:T,setRenameRows:P,setDatasets:z,handleRefresh:de})})}):a.jsx("a",{className:"dataset-name",onClick:e=>{e.preventDefault(),u(t),f||j(!0)},children:i})},width:l?"14.2%":""},{key:"createTime",title:"上传时间",dataIndex:"createTime",width:l?"14.6%":""},{key:"datasetStatus",title:"解析状态",dataIndex:"datasetStatus",render:(e,{datasetStatus:t,total:s,complete:l,failedReason:i})=>t===b.sucess?a.jsxs("label",{className:i?"warning-info":void 0,children:["解析完成 (",`${l}/${s}`,")"]}):t===b.failed?a.jsx("label",{className:"error-info",children:"解析失败"}):t===b.inProgress?0===l&&0===s?a.jsx("label",{children:"待解析"}):a.jsxs("label",{children:["解析中 (",`${l}/${s}`,")"]}):void 0,width:l?"9.6%":""},{key:"tags",title:"标签",ellipsis:!0,dataIndex:"tags",render:(e,t,s)=>{let{tags:l,id:i}=t;return a.jsx(L,{tagList:l,dataSetId:i,onChange:e=>{l=e;const t=R;t.forEach((t=>{t.id===i&&(t.tags=e)})),z(t),V({dataSetId:i,tags:l}).then((e=>{de()}),(e=>{h.error(e)}))},showAddBtn:!c,isClose:!c,OnEdit:e=>{}})},shouldCellUpdate:(e,t)=>!0,width:l?"18.2%":""},{key:"sources",title:"来源",ellipsis:!0,dataIndex:"sources",width:"10%",render:(e,t,s)=>{let{id:l,name:i}=t;return T.has(l)?a.jsx("div",{children:"GDP"}):a.jsx("div",{children:"本地上传"})}},{key:"action",title:"操作",dataIndex:"datasetStatus",render:(e,t)=>{const{datasetStatus:s,name:l,childFiles:i,failedReason:n}=t;return s!==b.sucess||n?s===b.sucess&&n?a.jsx(d,{type:"link",className:"grid-link-btn",onClick:()=>{m(!0)},children:"问题详情"}):s===b.failed?a.jsx(d,{type:"link",className:"grid-link-btn",disabled:!0,onClick:()=>{h.warning("暂不支持此功能，更多优化尽请期待"),ne(!0)},children:"无法解析"}):s===b.inProgress?null:void 0:a.jsx(d,{type:"link",className:"grid-link-btn",onClick:()=>{u(t),f||(j(!0),clearInterval(ee.current))},children:"详情"})},width:"7.8%"},{key:"dropdown",title:" ",dataIndex:"dropdown",ellipsis:!0,render:(e,t,s)=>{let{tags:l,id:i}=t;return a.jsx(D,{tags:l,rowData:t,OnEdit:()=>{},OnChange:e=>{const t=R;t.forEach((t=>{t.id===i&&(t.tags=e)})),z(t),l=e,de()},OnDelete:async e=>{se(!0),await oe(e);const t=setTimeout((()=>{de()}),800);return se(!1),()=>clearTimeout(t)},OnRename:()=>{const e=T;e.add(i),P(e)}},s)},width:"16%"}];return a.jsxs("div",{style:{display:"flex",flexDirection:"column",flex:1},children:[a.jsxs("div",{style:{textAlign:"start",justifyContent:"space-between",display:"inline-flex",marginBottom:"1rem",width:"100%"},children:[a.jsx("div",{children:a.jsxs(n,{size:"small",children:[a.jsxs(n.Compact,{children:[a.jsx(k,{size:"large",className:"filter-select",value:U,onChange:e=>K(e),options:[{value:"datasetName",label:"数据集"},{value:"tag",label:"标签"}]}),a.jsx(v,{size:"large",className:"filter-input",suffix:a.jsx(w,{}),placeholder:`请输入${"datasetName"===U?"数据集":"标签"}名称`,value:Y,onChange:e=>{_(e.target.value)}})]}),a.jsx(k,{size:"large",className:"filter-select",value:G,onChange:e=>W(e),style:{width:"9.75rem"},options:[{value:"desc",label:"按时间倒序"},{value:"asc",label:"按时间正序"}]})]})}),i]}),a.jsx(o,{style:{flex:1},loading:te,locale:{emptyText:a.jsx(y,{image:O,description:a.jsx("span",{className:"dataset-table-empty-label",children:te?"请求中":"空空如也，去上传本地文件吧~"})})},rowKey:"id",rowSelection:{type:"checkbox",...ce},size:"small",className:"dataset-table",columns:he,dataSource:R,tableLayout:"fixed",pagination:!1}),a.jsx(B,{total:J.total,pageSize:J.size,page:J.page,OnChange:(e,t)=>{Q({page:e,size:t,total:J.total})}}),a.jsx(M,{visible:x,OnClose:e=>{m(!1)}}),a.jsx(Z,{visible:f,dataSet:p,OnClose:()=>{j(!1),re()}})]})}));export{J as D};

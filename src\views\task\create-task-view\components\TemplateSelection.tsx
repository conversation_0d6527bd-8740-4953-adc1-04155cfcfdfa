import React from 'react';
import { Form, Card, List, Button, Input, Tooltip } from 'antd';
import { TemplateSelectionProps, CreateTaskFormType } from '../types';
import { TOOLTIP_MESSAGES, CSS_CLASSES, DEFAULT_VALUES, MESSAGES } from '../constants';
import infoIcon from '@/assets/img/info-icon.svg';

const { TextArea } = Input;

const TemplateSelection: React.FC<TemplateSelectionProps> = ({
  templates,
  selectedIndex,
  textValue,
  onTemplateSelect,
  onTemplateApply,
  onTextChange,
}) => {
  const handleApplyClick = (item: any, index: number) => {
    // 去除roleBackground开头和结尾的换行符
    const processedRoleBackground = item.roleBackground.replace(/^\s+|\s+$/g, '');
    // 去除detailedDescription开头和结尾的换行符
    const processedDetailedDescription = item.detailedDescription.replace(/^\s+|\s+$/g, '');

    const newText = `角色背景：\n${processedRoleBackground}\n详情描述：\n${processedDetailedDescription}`;

    onTemplateApply(item, index);
    onTextChange(newText);
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onTextChange(e.target.value);
    onTemplateSelect(-1); // 重置选中状态
  };

  return (
    <Form.Item<CreateTaskFormType> name="domain" label="描述你的需求">
      <div>
        <Card style={{ display: 'flex', margin: '0', padding: '0' }}>
          <List
            header={<div>描述模板</div>}
            className={CSS_CLASSES.LIST_STYLE}
            bordered
            dataSource={templates}
            renderItem={(item, index) => (
              <List.Item
                key={index}
                style={{
                  backgroundColor:
                    selectedIndex === index ? 'rgb(234, 247, 245)' : 'initial',
                }}
                onClick={() => onTemplateSelect(index)}
              >
                {item.templateName}
                {selectedIndex === index && (
                  <Button
                    type="link"
                    size="small"
                    className={CSS_CLASSES.SHOW_BTN}
                    onClick={() => handleApplyClick(item, index)}
                  >
                    应用
                  </Button>
                )}
              </List.Item>
            )}
          />
          <div className={CSS_CLASSES.CONTEXT}>
            <p>
              需求描述{' '}
              <Tooltip title={TOOLTIP_MESSAGES.DESCRIPTION}>
                <img src={infoIcon} style={{ width: '16px', height: '16px' }} alt="info" />
              </Tooltip>
            </p>
            <TextArea
              value={textValue}
              onChange={handleTextChange}
              placeholder={MESSAGES.PLACEHOLDER.DESCRIPTION}
              autoSize={{ minRows: 10, maxRows: 13 }}
              showCount
              maxLength={DEFAULT_VALUES.MAX_DESCRIPTION_LENGTH}
              style={{
                width: '58rem',
                backgroundColor: 'rgb(242, 246, 249)',
                fontSize: '16px',
                paddingBottom: '30px',
              }}
            />
          </div>
        </Card>
      </div>
    </Form.Item>
  );
};

export default TemplateSelection;

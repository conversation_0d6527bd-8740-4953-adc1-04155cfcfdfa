import { Button, Col, Progress, Row, Space } from 'antd';
import { DatasetFile } from '../views/task/create-task-view/types';
import TagList from './TagList';
import { CloseOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import UploadErrorModal from './UploadErrorModal';
import fileaddition from '../assets/img/fileaddition.svg';
import FakeDownload from './FakeDownload';

interface UploadListProps {
  fileList: DatasetFile[];
  className?: string;
  noEditTag?: Boolean;
  onAddTag: (datasetId: string, tags: string[]) => void;
  onDelFile: (index: number) => void;
  onReUpload: (index: number) => void;
}

const statusMap = {
  done: '上传成功',
  success: '上传成功',
  error: '上传失败',
  uploading: '上传中',
  removed: '移除',
};

const UploadList: React.FC<UploadListProps> = ({
  fileList,
  onAddTag,
  className,
  noEditTag,
  onDelFile,
  onReUpload,
}) => {
  const [dataList, setDataList] = useState<DatasetFile[]>([]);
  const [uploadErrorModal, setUploadErrorModal] = useState<boolean>(false);
  useEffect(() => {
    setDataList(fileList);
  }, [fileList]);

  const DetailBtn: React.FC<{ file: DatasetFile; index: number }> = ({ file, index }) => {
    const { parseState, status } = file;
    if (status !== 'uploading' && parseState !== '解析中') {
      if (status === 'error') {
        return (
          <Button type="link" className="reqularText" onClick={() => onReUpload(index)}>
            重新上传
          </Button>
        );
      } else if (parseState === '解析成功') {
        return (
          <Button type="link" className="reqularText">
            详情
          </Button>
        );
        // } else if (parseState === "解析成功") {
        //   return (
        //     <Button
        //       className="reqularText"
        //       type="link"
        //       onClick={() => {
        //         setUploadErrorModal(true);
        //       }}
        //     >
        //       问题详情
        //     </Button>
        //   );
      } else if (parseState === '解析失败') {
        return (
          <Button type="link" className="reqularText">
            重新解析
          </Button>
        );
      }
    }
    return null;
  };

  return (
    <div className={className}>
      {dataList.map((item, index) => {
        return (
          <Row
            key={item.dataSetId}
            className="upload-list-item"
            gutter={16}
            style={{ position: 'relative' }}
          >
            <Col span={5}>
              <div style={{ width: '100%', display: 'flex', gap: 12 }}>
                <img src={fileaddition} />
                <div
                  className="reqularText"
                  style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    textDecoration: 'underline',
                    textOverflow: 'ellipsis',
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {item.name}
                </div>
              </div>
            </Col>
            <Col span={9}>
              <TagList
                tagList={item.tags || []}
                dataSetId={item.dataSetId}
                onChange={(tags) => {
                  onAddTag(item.dataSetId!, tags);
                }}
                // showAddBtn={noEditTag ? false : item.status ? true : false}
                showAddBtn={false}
                AddBtnLabel="添加标签"
              ></TagList>
            </Col>
            <Col span={2}>
              <div style={{ minWidth: '60px' }} className="reqularText">
                <label
                  className={item.status === 'error' ? 'color-dimgray error-info' : 'color-dimgray'}
                >
                  {item.status ? statusMap[item.status] : '平台库'}
                </label>
              </div>
            </Col>
            <Col span={6}>
              <div style={{ width: '130px' }}>{item.parseState}</div>
              <div style={{ width: '90px' }}>
                <DetailBtn file={item} index={index} />
              </div>
            </Col>
            <Col span={2} style={{ textAlign: 'right' }}>
              <Button
                type="text"
                size="small"
                onClick={() => {
                  onDelFile(index);
                }}
              >
                <CloseOutlined />
              </Button>
            </Col>
            {item.status === 'uploading' ? <FakeDownload /> : ''}
          </Row>
        );
      })}
      <UploadErrorModal
        visible={uploadErrorModal}
        OnClose={(rows) => {
          console.log(rows);
          setUploadErrorModal(false);
        }}
      ></UploadErrorModal>
    </div>
  );
};
export default UploadList;

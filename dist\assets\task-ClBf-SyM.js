import{r as t}from"./request-v7NtcsCU.js";function e(e){return t.post("/task",e)}function s(e){if(e.page&&e.size){let s=`/task/list?page=${e.page}&size=${e.size}`;return s=e.sortAttribute?s+`&sortAttribute=${e.sortAttribute}`:s,s=e.sortDirection?s+`&sortDirection=${e.sortDirection}`:s,s=e.taskName?s+`&taskName=${e.taskName}`:s,s=e.startTime?s+`&startTime=${e.startTime}`:s,s=e.endTime?s+`&endTime=${e.endTime}`:s,t.get(s)}return t.get(`/task/list?size=${e.size}`)}async function a(e){try{const s=`/review/progress/all?taskId=${e}`;return(await t.get(s)).data}catch(s){return}}async function n(e){const s=`/review/reviewer/list?taskId=${e}`;return(await t.get(s)).data}function r(e){return t.delete("/task",{data:{deleteIds:e}})}function o(e){return t.get(`/task?taskId=${e}`)}function i(e,s){const a=`/task?taskId=${e}&taskName=${s}`;return t.put(a,{})}function u(e){return t.get(`/task/problem?taskId=${e}`)}function c(e){const s=`/task/${e}/retry`;return t.get(s)}function f(e){const s=`/task/review-config?taskId=${e}`;return t.get(s)}function k(e){const s=`/task/review-config/is-set?taskId=${e}`;return t.get(s)}function p(e){return t.post("/task/review-config",e)}function d(e){const s=`/task/tree?taskId=${e}`;return t.get(s)}function m(t){const e=[];return t.forEach((function t(s){e.push({fileId:s.fileId,name:s.name,qaCount:s.qaCount,type:s.type}),s.children&&s.children.forEach(t)})),e}function l(e){const s=`/task/review-config/is-set?taskId=${e}`;return t.get(s)}function g(e){return t.post("/task/problem",e)}function $(e){return t.delete("/task/problem",{data:e})}function I(){return t.get("/templates/all")}function w(){return t.get("/task/download-template",{responseType:"blob"})}function b(e){const s=new FormData;if(s.append("importQAs",e.importQAs),e.taskName){const t=e.taskName.replace(/^"|"$/g,"");s.append("taskName",t)}return t.post("/task/import",s,{headers:{"Content-Type":"multipart/form-data"}})}function T(e){const s=`/dataset/generateTags?fileIds=${e}`;return t.post(s)}function h(e){return t.put("/qa",e)}export{T as a,l as b,e as c,r as d,f as e,s as f,I as g,u as h,c as i,o as j,n as k,k as l,h as m,d as n,m as o,w as p,a as q,i as r,p as s,$ as t,b as u,g as v};

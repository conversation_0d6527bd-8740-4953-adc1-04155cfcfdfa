import{j as e,R as r}from"./react-core-BrQk45h1.js";const s=({text:s,highlights:t})=>{let l=0;const n=/!\[IMG\]\((.*?)\)/g,a=e=>e.replace(n,((e,r)=>`<img style="width:100%" key=${r} src=${r} alt="图片" />`)),c=t.map(((t,n)=>{const{start:c,end:i,score:o}=t,d=a(s.slice(l,c)),g=s.slice(c,i);let h;return l=i,h=o>=.9?"rgb(153, 215, 202)":o>=.8&&o<.9?"rgba(153, 215, 202,.6)":o>=.7&&o<.8?"rgba(153, 215, 202,.3)":"#fff",e.jsxs(r.Fragment,{children:[e.jsx("div",{dangerouslySetInnerHTML:{__html:d}}),e.jsx("span",{style:{backgroundColor:h},children:g})]},n)})),i=s.slice(l);return e.jsxs("div",{children:[c,e.jsx("div",{dangerouslySetInnerHTML:{__html:a(i)}})]})};export{s as H};

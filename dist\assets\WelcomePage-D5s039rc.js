import{a as l,b as i}from"./auth-BKEkOdcM.js";import{S as e}from"./index-y6n7cw2i.js";import{a as s}from"./utils-DuyTwmHT.js";import{t as x,r as n,j as d,J as r,L as c,K as h,S as j,O as o,Q as u,U as P}from"./react-core-BrQk45h1.js";function a(l,n){return d=>{i(l).then((l=>{var i,r;200===(null==(i=null==l?void 0:l.data)?void 0:i.code)?(s.set("token",l.data.token),d({type:e,payload:l.data.token}),n&&n()):x.error(null==(r=null==l?void 0:l.data)?void 0:r.message)}))}}function t(i,x){return n=>{l(i).then((l=>{var i;200===(null==(i=null==l?void 0:l.data)?void 0:i.code)&&(s.set("token",l.data.token),n({type:e,payload:l.data.token}),x&&x())}))}}const v=({isAgree:l,onIsAgreeChange:i})=>{const[e,s]=n.useState(!1),[x,a]=n.useState(!1);return d.jsxs("div",{children:[d.jsxs(r,{checked:l,onClick:()=>{i(!l)},children:["我已阅读并同意",d.jsx(c,{to:"",onClick:()=>{s(!0)},style:{margin:"0 4px"},children:"行至用户协议"}),"和",d.jsx(c,{to:"",onClick:()=>{a(!0)},style:{margin:"0 4px"},children:"隐私政策"})]}),d.jsx(h,{title:"行至智能（北京）技术有限公司用户协议",open:e,onOk:()=>s(!1),onCancel:()=>s(!1),styles:{body:{height:"600px"}},width:"70%",footer:[],children:d.jsx(j,{children:d.jsxs(o,{children:[d.jsx(u,{level:2,children:" 1. 特别提示"}),d.jsx(P,{children:"在您使用行至智能（北京）技术有限公司旗下的Pollux-数据推理生成平台（以下称为“Pollux”）之前，请您仔细阅读并完全理解本协议的所有条款。特别是免除或限制责任的条款、对用户的权利进行限制的条款、约定争议解决方式和司法管辖的条款等。您一旦使用Pollux，即表示您已阅读并同意接受本协议的所有条款和条件，并愿意受其约束。"}),d.jsx(u,{level:2,children:"2. 接受协议"}),d.jsx(P,{children:"1. 协议的生效条件：本协议链接于Pollux各个服务的注册页面、登录页面或主页面上，用户点击“同意”按钮、勾选“我已阅读并同意”或其他类似意思的按钮或链接，或通过其他任何明示或默认方式表示接受本协议，即表示用户已阅读并同意本协议的全部内容。除非用户接受本协议，否则用户无权并且不应当使用Pollux及其相关服务。"}),d.jsx(P,{children:"2. 协议的变更：行至智能保留随时修改本协议的权利，且无需另行通知。用户应定期查阅本协议，以了解协议的变更内容。变更后的协议在Pollux上发布后，立即自动生效。用户继续使用Pollux将表示用户接受变更后的协议。如果用户不接受变更后的协议，应当停止使用Pollux。"}),d.jsx(P,{children:"3. 协议的构成：本协议内容包括协议正文、所有行至智能已经发布的或将来可能发布的Pollux使用规则。所有Pollux使用规则为本协议不可分割的一部分，与协议正文具有同等法律效力。"}),d.jsxs(P,{children:["4. 用户行为的准则：用户在使用Pollux时，必须遵循国家的相关法律法规，不得在Pollux上或通过Pollux发布、转载、传送含有以下内容的信息：",d.jsxs("ul",{children:[d.jsx("li",{children:"违反宪法确定的基本原则的；"}),d.jsx("li",{children:"违反宪法确定的基本原则的；"}),d.jsx("li",{children:"破坏国家统一、破坏民族团结的；"}),d.jsx("li",{children:"破坏国家宗教政策、宣扬邪教和封建迷信的；"}),d.jsx("li",{children:"散布谣言、扰乱社会秩序、破坏社会稳定的；"}),d.jsx("li",{children:"以及其他违反国家法律法规政策的信息。"})]})]}),d.jsx(u,{level:2,children:"3. 服务内容及软件形式"}),d.jsx(P,{children:"1. 服务内容描述：Pollux是一个数据推理生成平台，旨在为用户提供数据分析、推理、可视化以及其他相关的数据处理服务。用户可以上传数据、进行数据分析、获取推理结果并下载相关报告。"}),d.jsxs(P,{children:["2. 软件形式：",d.jsxs("ul",{children:[d.jsx("li",{children:"网页版：用户可以通过浏览器直接访问Pollux的官方网站，进行注册、登录、数据上传、分析和下载等操作。"}),d.jsx("li",{children:"桌面客户端：行至智能可能会提供桌面客户端版本的Pollux，用户可以下载并安装在个人计算机上使用，享受更流畅、更丰富的功能体验。"}),d.jsx("li",{children:"移动应用：行至智能可能会推出Pollux的移动应用版本，支持在智能手机或平板电脑上使用，方便用户随时随地进行数据推理和分析。"})]})]}),d.jsx(P,{children:"3. 软件更新：为了增强用户体验，行至智能可能会定期更新Pollux的功能和内容。当有新版本发布时，Pollux可能会提示用户进行更新。为了确保软件的正常运行和用户的数据安全，建议用户及时更新到最新版本。"}),d.jsx(P,{children:"4. 服务费用：Pollux可能提供免费和付费两种服务。免费服务可能有一定的功能限制，而付费服务会提供更多的功能和优先的技术支持。具体的费用标准和收费方式，行至智能会在Pollux上进行明确公告。"}),d.jsx(u,{level:2,children:"4. 用户使用规则"}),d.jsx(P,{children:d.jsxs("ul",{style:{listStyleType:"decimal"},children:[d.jsxs("li",{children:["账户注册与使用：",d.jsxs("ul",{children:[d.jsx("li",{children:"用户在使用Pollux前需要注册一个账户。注册时需提供真实、准确、最新的个人信息。如果信息发生变动，应及时更新。"}),d.jsx("li",{children:"用户账户、密码和安全性：用户一旦注册成功，将得到一个用户名和密码。用户应妥善保管用户名和密码，且应对通过该用户名和密码进行的所有活动承担责任。如发现任何非法使用或存在安全漏洞的情况，应立即通知行至智能。"})]})]}),d.jsxs("li",{children:["用户行为规范：",d.jsxs("ul",{children:[d.jsx("li",{children:"用户在使用Pollux时，必须遵循国家的相关法律法规。"}),d.jsx("li",{children:"用户不得上传、展示或传播任何虚假、骚扰性、侵权、诽谤、淫秽、暴力或任何违法信息资料。"}),d.jsx("li",{children:"用户不得侵犯行至智能和其他任何第三方的知识产权、名誉权和其他合法权益。"}),d.jsx("li",{children:"用户不得进行任何可能对Pollux的正常运行造成不利影响的行为。"})]})]}),d.jsxs("li",{children:["内容发布规范：",d.jsxs("ul",{children:[d.jsx("li",{children:"如果用户在Pollux上发布内容，必须确保该内容真实、准确、非误导性。"}),d.jsx("li",{children:"用户发布的内容不得含有任何违法或违规信息。"}),d.jsx("li",{children:"行至智能有权对用户发布的内容进行审核，对不符合规定或可能引发法律风险的内容，行至智能有权不予发布或删除。"})]})]}),d.jsxs("li",{children:["使用限制：",d.jsxs("ul",{children:[d.jsxs("li",{children:[" ","用户不得使用任何自动程序、脚本或其他方式对Pollux进行访问和数据抓取。"]}),d.jsx("li",{children:"用户不得恶意注册Pollux账户，包括但不限于频繁创建、删除账户。"}),d.jsx("li",{children:"用户不得通过任何手段试图破解Pollux或获取其他用户的账户信息。"})]})]}),d.jsxs("li",{children:["服务使用：",d.jsxs("ul",{children:[d.jsx("li",{children:"用户应当遵循Pollux上发布的各项规则、声明和操作提示，正确、合理地使用Pollux。"}),d.jsx("li",{children:"如果用户违反上述规定，行至智能有权采取一切必要的措施，包括但不限于删除用户发布的内容、暂停或终止用户的账户使用权，以及追究用户的法律责任。"})]})]})]})}),d.jsx(u,{level:2,children:"5. 知识产权声明"}),d.jsx(P,{children:d.jsxs("ul",{style:{listStyleType:"decimal"},children:[d.jsxs("li",{children:["软件与服务的知识产权：",d.jsx("ul",{children:d.jsx("li",{children:"所有与Pollux相关的知识产权，包括但不限于版权、商标、专利、商业秘密以及其他所有权，均为行至智能所有。未经行至智能明确书面许可，任何单位或个人不得以任何方式全部或部分复制、转载、引用、链接、抓取或以其他方式使用Pollux的任何内容。"})})]}),d.jsxs("li",{children:["用户内容的知识产权：",d.jsx("ul",{children:d.jsx("li",{children:"用户在Pollux上发布的任何内容，除非有明确声明，否则版权均归原作者所有。但为了保证服务的正常运行，用户已同意，对于用户在Pollux上发布的内容，授予行至智能非独家的、永久的、不可撤销的、免费的使用权，行至智能有权展示、推广、复制、修改、出版或以其他方式使用这些内容。"})})]}),d.jsxs("li",{children:["知识产权保护：",d.jsx("ul",{children:d.jsx("li",{children:"行至智能尊重并保护知识产权，如果任何第三方认为Pollux的内容（包括用户发布的内容）侵犯了其合法权益，可以向行至智能提交书面通知，行至智能将在收到通知后采取相应的措施。"})})]}),d.jsxs("li",{children:["禁止侵权行为：",d.jsx("ul",{children:d.jsx("li",{children:"用户承诺不得在Pollux上发布或传播任何侵犯他人知识产权的内容。如果用户的行为导致行至智能遭受损失，用户应当承担全部赔偿责任。"})})]}),d.jsxs("li",{children:["技术与算法：",d.jsx("ul",{children:d.jsx("li",{children:"Pollux所使用的技术和算法均为行至智能或其合作伙伴所有或已获得合法授权。未经行至智能明确书面同意，任何人不得使用、修改、反向工程、反汇编、反编译或以其他任何方式尝试发现其源代码。"})})]})]})}),d.jsx(u,{level:2,children:"6. 隐私政策与数据保护"}),d.jsxs(P,{children:["行至智能高度重视用户的隐私和数据保护。我们收集、使用、存储和分享用户的个人数据，都是为了向用户提供更好的服务，并确保这些数据的安全。",d.jsxs("ul",{children:[d.jsx("li",{children:"数据收集：在用户使用Pollux时，我们可能会收集用户的注册信息、使用记录、设备信息等。这些数据帮助我们优化服务、提供个性化推荐和保障账户安全。"}),d.jsx("li",{children:"数据使用：我们使用用户数据来提供、维护和改进Pollux，以及进行市场推广和研究。"}),d.jsx("li",{children:"数据存储：用户的数据存储在安全的服务器上，并采取各种技术手段确保数据不被泄露、损坏或丢失。"}),d.jsx("li",{children:"数据分享：除非得到用户的明确同意，或根据法律法规的要求，我们不会与第三方分享用户的个人数据。"}),d.jsx("li",{children:"用户权利：用户有权随时访问、更正、删除自己的个人数据，以及撤回之前给予的数据处理同意。"})]}),"我们鼓励用户定期查看我们的隐私政策，了解我们如何处理用户的个人数据。"]}),d.jsx(u,{level:2,children:"7. 责任限制与免责声明"}),d.jsxs(P,{children:["尽管行至智能始终致力于为用户提供高质量的服务，但由于互联网的特性和各种不可预测的因素，我们不能保证Pollux的服务始终是完美无缺的。",d.jsxs("ul",{children:[d.jsx("li",{children:"服务状态：Pollux是按照现有技术和条件所能达到的现状提供的。行至智能不对Pollux提供的服务做任何明示或暗示的保证，包括但不限于商业适售性、特定用途的适用性及未侵犯他人权利。"}),d.jsx("li",{children:"第三方风险：用户通过Pollux与第三方进行交互或使用第三方服务，由此产生的风险和损失，行至智能不承担任何责任。"}),d.jsx("li",{children:"数据丢失：行至智能将采取合理措施保护用户数据的安全，但如果因不可抗力、黑客攻击等非行至智能原因导致的数据丢失、泄露，行至智能不承担责任。"}),d.jsx("li",{children:"免责范围：在法律允许的最大范围内，行至智能不对因使用Pollux而产生的任何间接、特殊、附带、惩罚性或衍生性损害承担责任。"})]}),"用户明确同意使用Pollux的风险由用户个人承担。"]}),d.jsx(u,{level:2,children:"8. 服务的修改与终止"}),d.jsxs(P,{children:["行至智能始终在不断地改进和优化Pollux，以更好地满足用户的需求。因此，我们可能会不时地修改、增加或减少某些服务功能，或暂停、终止某项服务。",d.jsxs("ul",{children:[d.jsx("li",{children:"服务修改：行至智能保留随时修改Pollux服务内容的权利。当服务内容发生变动时，行至智能将在Pollux上或通过其他途径通知用户。"}),d.jsxs("li",{children:["服务终止：在以下情况下，行至智能有权终止或暂停向用户提供服务：",d.jsxs("ul",{children:[d.jsx("li",{children:"用户违反了本协议的任何条款；"}),d.jsx("li",{children:"法律法规要求终止服务；"}),d.jsx("li",{children:"出于安全原因或其他必要情况。"})]})]}),d.jsxs("li",{children:["数据处理：",d.jsxs("ul",{children:[d.jsx("li",{children:"服务终止后，行至智能没有义务为用户保留或提供任何与其账号相关的数据，但除非法律有明确规定，否则用户的个人数据将被删除或匿名化处理。"}),d.jsx("li",{children:"在服务终止前，用户有责任备份与其账号相关的所有信息和数据。"})]})]}),d.jsxs("li",{children:["退款政策：",d.jsx("ul",{children:d.jsx("li",{children:"如用户购买的Pollux付费服务在未到期前被终止或中断，用户应当根据实际使用时间按比例退款。具体退款方式和金额，将根据用户购买服务时的相关规定确定。"})})]})]}),"用户理解并同意，行至智能终止或修改服务的决定是为了更好地提供服务，用户不能因此要求行至智能承担任何责任。"]}),d.jsx(u,{level:2,children:"9. 协议的修改"}),d.jsx(P,{children:"行至智能保留随时修改本协议条款的权利。一旦本协议的内容发生变动，行至智能将会在Pollux上公布修改后的协议内容，或通过其他适当的方式（如邮件）通知用户。修改后的协议一旦在Pollux上公布即生效，并代替原来的协议。 用户有责任定期查看Pollux上的协议变更通知。如果用户不同意修改后的协议，应立即停止使用Pollux。如果用户继续使用Pollux，则表示用户接受修改后的协议。"}),d.jsx(u,{level:2,children:"10. 争议解决"}),d.jsx(P,{children:d.jsxs("ul",{style:{listStyleType:"decimal"},children:[d.jsxs("li",{children:["协商解决：",d.jsx("ul",{children:d.jsx("li",{children:"对于因本协议引起的或与本协议有关的任何争议，用户和行至智能首先应尝试友好协商解决。"})})]}),d.jsxs("li",{children:["适用法律：",d.jsx("ul",{children:d.jsx("li",{children:"本协议的签订、执行和解释及争议的解决均应适用中华人民共和国法律。"})})]}),d.jsxs("li",{children:["仲裁条款：",d.jsx("ul",{children:d.jsx("li",{children:"若协商不成，任何一方均有权将争议提交至北京仲裁委员会进行仲裁，仲裁结果是终局的，对双方均有约束力。"})})]}),d.jsxs("li",{children:["法院管辖：",d.jsx("ul",{children:d.jsx("li",{children:"如仲裁不适用或任何一方不接受仲裁结果，用户和行至智能同意由行至智能所在地的人民法院管辖并审理此类争议。"})})]})]})}),d.jsx(u,{level:2,children:"11. 其他条款"}),d.jsx(P,{children:d.jsxs("ul",{children:[d.jsx("li",{children:"完整协议：本协议及其附加文档构成了用户和行至智能之间关于Pollux的完整协议，取代了双方之前关于同一事项的任何协议、声明或承诺。"}),d.jsx("li",{children:"条款独立性：如果本协议的任何条款被认定为无效或不可执行，该条款的其余部分仍应继续有效。"}),d.jsx("li",{children:"放弃权利：行至智能对本协议的任何违约或违规行为的放弃，并不代表对后续违约或违规行为的放弃。"}),d.jsx("li",{children:"适用法律：本协议的签订、执行、解释和争议解决均适用中华人民共和国的法律。"}),d.jsx("li",{children:"标题的目的：本协议中的标题仅为方便而设，不具有法律或合同效力。"}),d.jsx("li",{children:"通知：所有发送给用户的通知都可通过电子邮件、常规邮件、短信、Pollux平台公告或其他现行或以后开发出的通讯工具进行。服务条款的修改或其他事项变更时，行至智能将会以上述形式进行通知。"})]})})]})})}),d.jsx(h,{title:"行至智能（北京）技术有限公司隐私协议",open:x,onOk:()=>a(!1),onCancel:()=>a(!1),styles:{body:{height:"600px"}},width:"70%",footer:[],children:d.jsx(j,{children:d.jsxs(o,{children:[d.jsx(P,{children:"欢迎您使用Pollux-数据推理生成平台（以下称为“Pollux”）！"}),d.jsx(P,{children:"行至智能（北京）技术有限公司（以下称为“行至智能”）始终坚守对用户隐私的尊重和保护。我们深知您的个人信息对您的重要性，因此我们制定了本隐私政策，旨在明确地告知您，当您使用Pollux时，我们是如何收集、使用、存储和保护您的个人信息的。在您决定使用Pollux之前，请您仔细阅读并确保您充分理解本隐私政策的内容。一旦您开始使用Pollux，即表示您已同意我们按照本隐私政策处理您的相关信息。"}),d.jsx(P,{children:"1.重要提示"}),d.jsx(P,{children:"本隐私政策适用于Pollux的所有服务，但不包括其他第三方提供的服务。当您使用第三方服务时，这些服务可能有其自己的隐私政策，请您务必仔细阅读。"}),d.jsx(P,{children:"我们强烈建议您在使用Pollux之前完整阅读并理解本隐私政策。如果您不同意本隐私政策的任何部分，您应立即停止使用Pollux。"}),d.jsx(P,{children:"请您定期查看本隐私政策，以确保您了解我们可能做出的任何更改。如果我们对本隐私政策进行重大更改，我们将通过Pollux或其他方式通知您。"}),d.jsx(P,{children:"如果您对本隐私政策有任何疑问或需要进一步的澄清，您可以随时联系我们。"}),d.jsx(P,{children:"2. 个人信息定义"}),d.jsx(P,{children:"个人信息是指能够单独或与其他信息结合起来，用以识别、关联或联系到特定的自然人的任何信息。这包括但不限于您的姓名、电话号码、电子邮件地址、IP地址、账户名、账户密码以及您在使用Pollux时的操作记录和行为数据。不包括已经经过处理无法重新识别特定个体且不能复原的信息。"}),d.jsx(P,{children:"3. 信息收集方式"}),d.jsx(P,{children:"当您使用Pollux时，我们可能会通过以下方式收集您的个人信息："}),d.jsxs("ul",{children:[d.jsx("li",{children:"您直接提供的信息：例如，您在注册账户、填写个人资料或参与调查时提供的姓名、电话号码、电子邮件地址等。"}),d.jsx("li",{children:"您在使用服务过程中产生的信息：当您使用Pollux，我们可能会收集您的操作记录、设备信息、日志信息、位置信息等。"}),d.jsx("li",{children:"从第三方获取的信息：我们可能会从与我们合作的第三方服务提供商或合作伙伴那里收到关于您的信息，例如，当您通过第三方账户登录Pollux时。"}),d.jsx("li",{children:"Cookie和其他技术：我们可能会使用Cookie、Web Beacons和其他技术来收集关于您的浏览习惯、设备信息和其他相关信息。"})]}),d.jsx(P,{children:"4. 信息使用方式 我们可能会出于以下目的使用您的个人信息："}),d.jsxs("ul",{children:[d.jsx("li",{children:"提供服务：确保Pollux的正常运行，为您提供所需的服务和功能。"}),d.jsx("li",{children:"优化服务：分析和了解您如何使用Pollux，以改进我们的产品和服务，提供更好的用户体验。"}),d.jsx("li",{children:"安全保障：保护您的账户安全，预防、发现和处理欺诈、违法或其他可能危害Pollux安全的行为。"}),d.jsx("li",{children:"推广与营销：向您提供与Pollux相关的广告、推广和信息，除非您选择不接收此类信息。"}),d.jsx("li",{children:"法律和法规要求：满足相关法律、法规和政策的要求，或应对法律程序或政府要求。"})]}),d.jsx(P,{children:"5. Cookie和同类技术的使用"}),d.jsx(P,{children:"当您使用Pollux时，我们可能会使用Cookie、Web Beacons和其他同类技术来收集和存储您的信息，以提供更个性化的用户体验和服务。这些技术可能会收集您的浏览器类型、操作系统、访问时间、引用/退出页面和点击流数据等信息。您可以选择通过浏览器设置拒绝或管理Cookie，但这可能会影响您使用Pollux的某些功能。"}),d.jsxs("ul",{children:[d.jsx("li",{children:"Cookie：用于存储与您的浏览器或设备相关的小型数据文件，可以帮助我们记住您的偏好和其他数据，从而提高您的使用体验。"}),d.jsx("li",{children:"Web Beacons：通常与Cookie一起使用，用于统计网站的访问数量和使用模式。"}),d.jsx("li",{children:"其他技术：如本地共享对象和HTML5，用于存储与您的应用程序、浏览器或设备相关的内容。"})]}),d.jsx(P,{children:"6. 信息共享、转让和公开披露"}),"行至智能在以下情况下可能会共享、转让或公开披露您的个人信息：",d.jsxs("ul",{children:[d.jsx("li",{children:"经您同意：在获得您明确同意后，我们会与第三方共享您的个人信息。"}),d.jsx("li",{children:"合作伙伴：为了提供您所需的服务，我们可能需要与我们的合作伙伴共享您的某些信息。但我们仅会共享为提供服务必要的信息，并要求合作伙伴遵循本隐私政策的相关规定。"}),d.jsx("li",{children:"法律要求：在法律、法规、法律程序或政府要求下，我们可能需要公开披露您的个人信息。"}),d.jsx("li",{children:"合并、收购或资产转让：在涉及合并、收购或资产转让的情况下，我们会确保新的持有者继续遵循本隐私政策，或者我们会请求您重新同意新的隐私政策。"})]}),d.jsx(P,{children:"7. 信息保护"}),"行至智能高度重视您的个人信息安全。我们采取了一系列的技术和管理措施来确保您的信息安全：",d.jsxs("ul",{children:[d.jsx("li",{children:"数据加密：我们使用加密技术来存储和传输您的个人信息，确保信息在传输过程中的安全。"}),d.jsx("li",{children:"访问控制：我们限制对您个人信息的访问权限，只有经过特定授权的员工才能访问这些信息。"}),d.jsx("li",{children:"安全培训：我们定期为员工提供关于数据保护和隐私的培训，确保他们了解如何正确处理和保护用户的个人信息。"}),d.jsx("li",{children:"物理安全：我们的数据中心采用严格的物理安全措施，以防止未经授权的人员访问。"}),d.jsx("li",{children:"应对安全事件：在发生个人信息泄露、损毁或丢失等安全事件时，我们会立即启动应急机制，采取相应措施阻止安全事件扩大，并及时通知您。"})]}),d.jsx(P,{children:"8. 信息保存"}),"行至智能将您的个人信息保存在中华人民共和国境内。我们仅在为实现本隐私政策中所述目的所必需的时间内保留您的个人信息，并在超出保留期限后删除或匿名化处理这些信息。具体的保存时间取决于以下因素：",d.jsxs("ul",{children:[d.jsx("li",{children:"服务的性质：例如，为您提供账户服务所需的信息可能会被长期保存，直到您删除账户。"}),d.jsx("li",{children:"法律要求：某些法律、法规或政策可能要求我们在特定时间内保存某些信息。"}),d.jsx("li",{children:"安全考虑：为防止欺诈或滥用，某些信息可能会被保存超过您使用Pollux的时间。"})]}),"请注意，即使您删除了账户或停止使用Pollux，我们仍可能根据法律要求或合理的商业需要保留某些信息。",d.jsx(P,{children:"9. 用户权利"}),"作为Pollux的用户，您有权对您的个人信息进行以下操作：",d.jsxs("ul",{children:[d.jsx("li",{children:"访问权：您有权随时访问您在Pollux上的个人信息。"}),d.jsx("li",{children:"更正权：如果您发现我们持有的关于您的个人信息是不准确或不完整的，您有权要求我们进行更正或更新。"}),d.jsx("li",{children:"删除权：在某些情况下，您可以要求我们删除您的个人信息。"}),d.jsx("li",{children:"限制处理权：您有权要求我们暂停处理您的个人信息。"}),d.jsx("li",{children:"数据携带权：您有权要求我们提供您的信息，以便您将其转移到其他服务提供商。"}),d.jsx("li",{children:"撤回同意：在您之前给予我们处理您个人信息的同意的情况下，您有权随时撤回您的同意。"})]}),"我们承诺尊重并保护上述权利，并确保您可以轻松、安全地行使这些权利。",d.jsx(P,{children:"10. 第三方服务"}),"Pollux可能包含指向其他第三方服务的链接或功能。请注意，当您点击这些链接或使用这些功能时，您将被引导到这些第三方服务。这些第三方服务是由其各自的运营商控制的，并不受本隐私政策的约束。我们建议您在使用这些第三方服务之前，仔细阅读并了解其隐私政策和条款。行至智能对您在这些第三方服务上的任何活动或这些第三方的隐私政策不承担任何责任。",d.jsx(P,{children:"11. 隐私政策的变更和通知"}),"我们可能会根据业务需求或法律法规的变更对本隐私政策进行修改。当我们对隐私政策做出重大变更时，我们会通过Pollux或其他适当的方式向您发出通知。请您定期查看本隐私政策，以确保您了解我们可能做出的任何更改。继续使用Pollux表示您同意受到修订后的隐私政策的约束。",d.jsx(P,{children:"12. 联系方式"}),"如果您对本隐私政策有任何疑问、意见或建议，或者需要与我们就您的个人信息进行沟通，您可以通过以下方式联系我们：",d.jsxs("ul",{children:[d.jsx("li",{children:"电子邮件：<EMAIL>"}),d.jsx("li",{children:"邮寄地址：北京市海淀区知春路紫金数码园3号907室，行至智能（北京）技术有限公司"})]}),"我们将尽快回复并处理您的请求。",d.jsx(P,{children:"13. 适用法律和管辖"}),"本隐私政策的制定、执行和解释以及与其相关的争议均应适用中华人民共和国的法律。如行至智能与用户之间就个人信息保护产生争议，双方应首先尝试友好协商解决；如协商不成，用户同意将争议提交至行至智能所在地的有管辖权的法院进行诉讼。"]})})})]})},p=()=>d.jsx("div",{style:{padding:"20px",width:"100%",height:"100%"},children:d.jsxs("div",{className:"rectangle-parent57 group-child19",children:[d.jsxs("div",{className:"logo-container",children:[d.jsx("div",{className:"logo32",children:d.jsx("img",{style:{width:"17px",height:"22.58px"},src:"/logo.png"})}),d.jsx("div",{className:"everreachai-pollux16 enText",children:"EverReachAI Pollux"})]}),d.jsxs("div",{className:"pollux-parent",children:[d.jsx("div",{className:"pollux mediumText",children:"欢迎使用Pollux"}),d.jsx("div",{className:"div647 lightText",children:"企业级大模型训练数据生成工具"}),d.jsx("div",{className:"logo31",children:d.jsx("img",{src:"/logo.png"})})]})]})});export{v as U,p as W,a as d,t as r};

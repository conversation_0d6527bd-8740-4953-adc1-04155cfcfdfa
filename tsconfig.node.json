{
    "compilerOptions": {
    //   "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",
      "target": "ES2022",
      "lib": ["ES2022"],
      "module": "ESNext",
      "skipLibCheck": true,
      "moduleResolution": "node",
      "isolatedModules": true,
      "moduleDetection": "force",
      "noEmit": true,
      "strict": true,
      "noUnusedLocals": true,
      "noUnusedParameters": true,
      "noFallthroughCasesInSwitch": true,
      "esModuleInterop": true,
      "allowSyntheticDefaultImports": true,
      "composite": true,
    },
    "include": ["vite.config.ts"]
  }
  
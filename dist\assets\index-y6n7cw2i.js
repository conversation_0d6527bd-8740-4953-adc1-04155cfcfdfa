const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/DashboardView-CP355P2v.js","assets/react-core-BrQk45h1.js","assets/file-utils-DYD-epjE.js","assets/vendor-5xQGrmEQ.js","assets/antd-icons-CJI3ned2.js","assets/react-core-BdOndhxL.css","assets/charts-C9p0W68L.js","assets/request-v7NtcsCU.js","assets/http-CC8KrKfC.js","assets/utils-DuyTwmHT.js","assets/utils-BgDCIyLK.css","assets/index-EQnKihlc.js","assets/conts-BxhewW4k.js","assets/modle-Dr2iwiy1.js","assets/group-151-0eGrjKh_.js","assets/index-CIvVPbEk.css","assets/index-CTiuutXR.js","assets/index-tH02mKs8.css","assets/AddBaseModel-CuO8Vl-v.js","assets/info-icon2-BPNrwGFd.js","assets/uploadBaseModel-DgqMkDyn.css","assets/LoginView-BluehvES.js","assets/WelcomePage-D5s039rc.js","assets/auth-BKEkOdcM.js","assets/LoginView-BNMjJK8d.css","assets/service-CkPMTEw7.js","assets/action-DppZlFyC.js","assets/redux-CXOBDlvD.js","assets/RegisterView-zBWCOgqh.js","assets/types-ccCJOgIs.js","assets/MainView-Clx7FvL2.js","assets/white-logo-C5vvyXfj.js","assets/white-logo-8aS6VUWp.css","assets/uploadcomputer-DVakL5y0.js","assets/avatar-1-Bswa0fNs.js","assets/avatar-6-Dojl3spU.js","assets/MainView-Bni6J-Vq.css","assets/index-BoOrJAcy.js","assets/UploadList-CCFxdIyn.js","assets/UploadErrorModal-Dpa2hJTx.js","assets/task-ClBf-SyM.js","assets/DatasetTable-qqjeGPbM.js","assets/dataset-CNkWbBvO.js","assets/empty-logo-5H_PPUCG.js","assets/DatasetTable-BlUe9xvV.css","assets/info-icon-DzE4dkR7.js","assets/SourceDatasetView-DS9gUpmF.js","assets/bxtrash-Dz9B4ICl.js","assets/SourceDatasetView-DcO5VZJD.css","assets/index-Dp9uD80q.js","assets/formatred-B3vJFs-2.js","assets/DatasetExportModal-C5GWuAi5.js","assets/review-5qiVEhSO.js","assets/index-Dp9X2s4-.js","assets/index-D4bGI7Pq.css","assets/index-BBZI0pgu.css","assets/index-_XyEz62Q.js","assets/HighlightedText-DunVM1pH.js","assets/warningIcon-B4XoRFEk.js","assets/index-BXPrsv0G.css","assets/DefaultReviewView-BKFcpbit.js","assets/FirstRunView-C7_0iLte.js","assets/FirstRunView-ChCVZ_gn.css","assets/AdminView-DIewTXil.js","assets/index-DPcMU252.js","assets/TimeModal-CKvOYePi.js","assets/TimeModal-cyBNVvBy.css","assets/DeleteConfirmModal-CNzDvFL4.js","assets/index-9syjjKwz.css","assets/index-DbNOxkH-.js","assets/index-C7rWjcmW.js","assets/index-BEvGzxlO.css","assets/UploadDataset-BViysV1c.js","assets/UploadBaseModel-DBMq2wpT.js","assets/server-YuXc7UOr.js","assets/index-DePkR0U1.js","assets/NewServer-xUxo7OJu.js","assets/UpdateServer-zCUcw-UR.js"])))=>i.map(i=>d[i]);
import{u as e,j as t,N as n,R as r,a,H as i,r as o,b as s,P as l,C as m,z as d}from"./react-core-BrQk45h1.js";import{a as c}from"./utils-DuyTwmHT.js";import{c as _,a as u,b as p,t as f,l as h}from"./redux-CXOBDlvD.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const j={},E=function(e,t,n){let r=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),a=(null==n?void 0:n.nonce)||(null==n?void 0:n.getAttribute("nonce"));r=e(t.map((e=>{if((e=function(e){return"/"+e}(e))in j)return;j[e]=!0;const t=e.endsWith(".css"),n=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${n}`))return;const r=document.createElement("link");return r.rel=t?"stylesheet":"modulepreload",t||(r.as="script"),r.crossOrigin="",r.href=e,a&&r.setAttribute("nonce",a),document.head.appendChild(r),t?new Promise(((t,n)=>{r.addEventListener("load",t),r.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function a(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return r.then((t=>{for(const e of t||[])"rejected"===e.status&&a(e.reason);return e().catch(a)}))},y=r=>{var a=c.get("token");const i=e();return a?t.jsx(t.Fragment,{children:r.children}):t.jsx(n,{to:"/login?redirect="+i.pathname})},x=r.lazy((()=>E((()=>import("./DashboardView-CP355P2v.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10])))),S=r.lazy((()=>E((()=>import("./index-EQnKihlc.js")),__vite__mapDeps([11,1,2,3,4,5,12,13,7,8,9,10,14,15])))),g=r.lazy((()=>E((()=>import("./index-CTiuutXR.js")),__vite__mapDeps([16,1,2,3,4,5,13,7,8,9,10,12,6,17])))),P=r.lazy((()=>E((()=>import("./AddBaseModel-CuO8Vl-v.js")),__vite__mapDeps([18,1,2,3,4,5,13,7,8,9,10,19,20])))),L=r.lazy((()=>E((()=>import("./LoginView-BluehvES.js")),__vite__mapDeps([21,1,2,3,4,5,22,23,7,8,9,10,24,25,26,27])))),R=r.lazy((()=>E((()=>import("./RegisterView-zBWCOgqh.js")),__vite__mapDeps([28,1,2,3,4,5,22,23,7,8,9,10,24,29,27])))),D=r.lazy((()=>E((()=>import("./MainView-Clx7FvL2.js")),__vite__mapDeps([30,1,2,3,4,5,31,32,23,7,8,9,10,33,34,35,25,36])))),T=r.lazy((()=>E((()=>import("./index-BoOrJAcy.js")),__vite__mapDeps([37,1,2,3,4,5,38,39,40,7,8,9,10,41,29,42,43,44,12,14,45])))),A=r.lazy((()=>E((()=>import("./SourceDatasetView-DS9gUpmF.js")),__vite__mapDeps([46,1,2,3,4,5,42,7,8,9,10,41,29,38,39,40,43,44,33,47,48])))),v=r.lazy((()=>E((()=>import("./index-Dp9uD80q.js")),__vite__mapDeps([49,1,2,3,4,5,29,50,39,40,7,8,9,10,51,43,52,47,34,35,26,53,54,27,55])))),O=r.lazy((()=>E((()=>import("./index-_XyEz62Q.js")),__vite__mapDeps([56,1,2,3,4,5,29,50,43,40,7,8,9,10,12,57,58,52,51,34,45,42,25,59])))),I=r.lazy((()=>E((()=>import("./DefaultReviewView-BKFcpbit.js")),__vite__mapDeps([60,1,2,3,4,5,57,52,7,8,9,10,40,53,54])))),V=r.lazy((()=>E((()=>import("./FirstRunView-C7_0iLte.js")),__vite__mapDeps([61,1,2,3,4,5,62])))),k=r.lazy((()=>E((()=>import("./AdminView-DIewTXil.js")),__vite__mapDeps([63,1,2,3,4,5,31,32,34])))),z=r.lazy((()=>E((()=>import("./index-DPcMU252.js")),__vite__mapDeps([64,1,2,3,4,5,65,45,66,19,6,13,7,8,9,10,67,68])))),b=r.lazy((()=>E((()=>import("./index-DbNOxkH-.js")),__vite__mapDeps([69,1,2,3,4,5,65,45,66,58,13,7,8,9,10,48])))),w=r.lazy((()=>E((()=>import("./index-C7rWjcmW.js")),__vite__mapDeps([70,1,2,3,4,5,13,7,8,9,10,12,29,50,43,40,14,6,71])))),B=r.lazy((()=>E((()=>import("./UploadDataset-BViysV1c.js")),__vite__mapDeps([72,1,2,3,4,5,38,39,40,7,8,9,10,33])))),F=r.lazy((()=>E((()=>import("./UploadBaseModel-DBMq2wpT.js")),__vite__mapDeps([73,1,2,3,4,5,74,7,8,9,10,13,20])))),C=r.lazy((()=>E((()=>import("./index-DePkR0U1.js")),__vite__mapDeps([75,1,2,3,4,5,74,7,8,9,10,20])))),M=r.lazy((()=>E((()=>import("./NewServer-xUxo7OJu.js")),__vite__mapDeps([76,1,2,3,4,5,74,7,8,9,10,20])))),N=r.lazy((()=>E((()=>import("./UpdateServer-zCUcw-UR.js")),__vite__mapDeps([77,1,2,3,4,5,74,7,8,9,10,67,20])))),H=[{path:"/login",element:t.jsx(L,{})},{path:"/register",element:t.jsx(R,{})},{path:"/admin",element:t.jsx(y,{children:t.jsx(k,{})}),children:[{path:"/admin/dashboard",element:t.jsx(x,{})}]},{path:"/",element:t.jsx(y,{children:t.jsx(D,{})}),children:[{path:"/main/sourcedata",element:t.jsx(A,{})},{path:"/overview",element:t.jsx(V,{})},{path:"/main/task",children:[{path:"/main/task",element:t.jsx(v,{})},{path:"/main/task/create",element:t.jsx(T,{})},{path:"/main/task/uploadDataset",element:t.jsx(B,{})},{path:"/main/task/detail/:task_id",element:t.jsx(O,{})},{path:"/main/task/review/:task_id",element:t.jsx(I,{})}]},{path:"/main/finetune",children:[{path:"/main/finetune",element:t.jsx(b,{})},{path:"/main/finetune/detail/:id/:name",element:t.jsx(z,{})},{path:"/main/finetune/adjustment/:task_id",element:t.jsx(S,{})},{path:"/main/finetune/configure/:task_id/:name/:status",element:t.jsx(g,{})},{path:"/main/finetune/create",element:t.jsx(w,{})},{path:"/main/finetune/uploadBaseModel",element:t.jsx(F,{})},{path:"/main/finetune/addBaseModel",element:t.jsx(P,{})},{path:"/main/finetune/serverManagement",element:t.jsx(C,{})},{path:"/main/finetune/serverManagement/newServer",element:t.jsx(M,{})},{path:"/main/finetune/serverManagement/updateServer/:server_id",element:t.jsx(N,{})}]}]}];function q(){const e=a(H);return t.jsx(t.Fragment,{children:e})}function U(){return t.jsx(i,{children:t.jsx(o.Suspense,{children:t.jsx(q,{})})})}const W="SET_TOKEN",$="SET_USER_LIST",G={userInfo:{},token:""};const K={userList:[]};const J={trainingData:[]},Q=_(p({auth:function(e=G,t){switch(t.type){case"SET_USER":return{...e,userInfo:t.payload};case W:return{...e,token:t.payload};default:return e}},user:function(e=K,t){return t.type===$?{...e,userList:t.payload}:e},training:(e=J,t)=>{if("SET_TRAINING_DATA"===t.type){const n={...e,trainingData:[...e.trainingData,t.payload]};return localStorage.setItem("trainingData",JSON.stringify(n.trainingData)),n}return e}}),u(f,h));s.createRoot(document.getElementById("root")).render(t.jsx(l,{store:Q,children:t.jsx(m,{locale:d,theme:{token:{fontFamily:"HarmonyOS Sans SC Reqular",colorLink:"#0fb698",colorText:"#111111"},components:{Button:{fontFamily:"HarmonyOS Sans SC Reqular"},Progress:{colorSuccess:"#0fb698",colorInfo:"#0fb698"},Tabs:{fontFamily:"HarmonyOS Sans SC Medium",inkBarColor:"#0fb698",itemSelectedColor:"#0fb698",colorBorderSecondary:"unset"},Radio:{colorPrimary:"#0fb698"},Switch:{colorPrimary:"#0FB698"},Modal:{borderRadiusLG:24,colorBgMask:"rgba(71, 118, 128, 0.40)"},Segmented:{itemSelectedColor:"#19C1A3",itemColor:"#8E98A7",itemHoverColor:"#19C1A3"},Slider:{handleLineWidthHover:6,trackBg:"#0FB698",trackHoverBg:"#0FB698",handleLineWidth:6,railSize:6},Form:{fontFamily:"HarmonyOS Sans SC Reqular"},Tree:{colorPrimary:"#0FB698"},Checkbox:{colorPrimary:"#0FB698"},Descriptions:{labelBg:"#F6F9FC"},Steps:{colorPrimary:"#0FB698"}}},children:t.jsx(U,{})})}));export{W as S,$ as a};

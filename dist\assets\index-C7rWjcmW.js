import{r as e,p as t,j as a,v as s,I as l,T as i,K as r,ae as n,am as o,B as d,f as c,aq as m,c as h,d as x,n as u,q as g,o as p,k as j,F as v,l as f,aM as y,t as b}from"./react-core-BrQk45h1.js";import{g as S,n as N,e as _,h as C,p as k,s as I}from"./modle-Dr2iwiy1.js";import{h as w,i as F,l as T,j as E,k as A}from"./conts-BxhewW4k.js";import{T as z}from"./types-ccCJOgIs.js";import{f as D}from"./formatred-B3vJFs-2.js";import{e as V,T as B}from"./empty-logo-5H_PPUCG.js";import{f as R}from"./task-ClBf-SyM.js";import{g as L}from"./group-151-0eGrjKh_.js";import{E as q}from"./charts-C9p0W68L.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const M="_training-content_14g1y_1",O="_step1_14g1y_11",H="_operatearea_14g1y_25",$="_form-container_14g1y_65",J="_filter-input_14g1y_111",K="_inputtext_14g1y_121",P="_introdution-input_14g1y_153",Y=e.forwardRef(((r,n)=>{var o;const{dataset:d,modelName:c,modelIntro:m,modelId:h}=r.modelData,[x]=t.useForm();e.useImperativeHandle(n,(()=>({getModelSelectData:()=>(E(v),{Name:x.getFieldValue("modelName"),Description:x.getFieldValue("modelIntroduce"),Dataset:x.getFieldValue("modelSelect"),Id:E(x.getFieldValue("modelSelect"))}),validateFields:()=>{x.validateFields()}})));const[u,g]=e.useState(d||""),[p,j]=e.useState(h||""),[v,f]=e.useState(c||""),[y,b]=e.useState(m||""),[N,_]=e.useState([]),C=localStorage.getItem("trainingData")||"{}",k=JSON.parse(C),[I,w]=e.useState(null==(o=k[0])?void 0:o.modelConfigData);e.useRef();const[F,T]=e.useState({page:1,size:20,total:0}),E=e=>{const t=N.find((t=>t.modelName===e));return t?t.id:""},A=I?I.properties.modelId:"",z=N.map((e=>({value:e.modelName,label:e.modelName,id:e.id})));return e.useEffect((()=>{!function(){const e={...F,status:"",modelName:"",category:0};S(e).then((e=>{var t,a,s;if(200===(null==(t=e.data)?void 0:t.code)){_(null==(a=e.data)?void 0:a.data);const t=(null==(s=e.data)?void 0:s.data).filter((e=>A===e.id));x.setFieldValue("modelSelect",t[0]?t[0].modelName:d)}}))}()}),[]),a.jsx(a.Fragment,{children:a.jsxs("div",{className:M,children:[a.jsx("div",{className:O,style:{width:"15%"},children:"模型选择"}),a.jsx("div",{className:H,children:a.jsxs(t,{form:x,initialValues:{modelIntroduce:y||(I?I.properties.introduction:""),modelName:c||(I?I.properties.modelName:""),modelSelect:d,modelType:""},className:$,children:[a.jsx(t.Item,{label:"模型选择：",name:"modelSelect",rules:[{required:!0,message:"请选择要训练的模型"}],style:{width:"580px",marginTop:"45px"},children:a.jsx(s,{size:"large",showSearch:!0,placeholder:"选择要训练的模型",value:u,onChange:e=>{var t;g(e);const a=E(e);null==(t=r.onModelSelectChange)||t.call(r,a)},style:{flex:1},options:z})}),a.jsx(t.Item,{name:"modelName",label:"任务名称：",rules:[{required:!0,message:"请输入任务名称"},{pattern:new RegExp("^(?!_)[a-zA-Z0-9._\\u4e00-\\u9fa5]{2,22}$"),message:"任务名称不符合要求！"}],style:{marginTop:"45px"},extra:a.jsx("div",{className:K,children:"支持中英文、数字、下划线(_)，2-22个字符，不能以下划线为开头"}),children:a.jsx(l,{size:"large",className:J,placeholder:"请输入训练任务名称",value:v,onChange:e=>{f(e.target.value)}})}),a.jsx(t.Item,{name:"modelIntroduce",label:"模型介绍：",rules:[{required:!0,message:"请输入模型介绍"}],style:{marginTop:"45px"},children:a.jsx(i,{rows:4,size:"large",className:P,style:{height:120,resize:"none",width:"463px"},placeholder:"请输入模型介绍，不超过50字",maxLength:50})})]})})]})})})),Z="_step-label_osz4h_1",G="_step-container_osz4h_15",U="_step-content_osz4h_21",W="_dataset-container_osz4h_61",Q="_dataset-title_osz4h_69",X="_slider-mark_osz4h_77",ee="_confirm-modal-footer_1i96l_87",te="_confirm-btn_1i96l_97",ae="_confirm-modal-footer-label_1i96l_143",se=({visible:t,OnClose:s,taskIds:l,alltasks:i})=>{const[h,x]=e.useState(0),[u,g]=e.useState({page:1,size:10,total:0}),[p,j]=e.useState([]),[v,f]=e.useState([]),[y,b]=e.useState([]),[S,N]=e.useState([]),[_,C]=e.useState(!1),[k,I]=e.useState([]),[w,F]=e.useState([]),T=()=>{R({...u}).then((e=>{var t;200===(null==(t=null==e?void 0:e.data)?void 0:t.code)&&(b(e.data.data),e.data.data.length>0&&(g({...u,total:e.data.totalCount}),x((u.page-1)*u.size)))}))};e.useEffect((()=>{E(l)}),[l]);const E=e=>{if(!e||0===e.length)return;const t=[],a=[];S.forEach((s=>{e.includes(s.taskId)&&(t.push(s.taskId),a.push(s),C(!0))})),j(t),f(a)};e.useEffect((()=>{T(),N(i)}),[]),e.useEffect((()=>{T()}),[u.page,u.size]),e.useEffect((()=>{x((u.page-1)*u.size)}),[u]);const A=[{title:"序号",dataIndex:"index",render:(e,t,s)=>a.jsx(a.Fragment,{children:h+s+1}),width:"10%"},{title:"任务名称",dataIndex:"taskName",width:"20%"},{title:"创建时间",dataIndex:"createdate",width:"20%",render:(e,{createTime:t})=>D(new Date(t))},{title:"任务状态",dataIndex:"status",render:(e,{taskId:t,status:s,failedReason:l,complete:i,total:r})=>s===z.success?l?a.jsxs(c,{children:[a.jsxs("label",{className:"default-info",children:["任务完成",`(${i}/${r})`]}),a.jsx(m,{style:{color:"#E75252",cursor:"pointer"}})]}):a.jsxs("label",{className:"default-info",children:["任务完成",`(${i}/${r})`]}):s===z.failed?i&&i>0?a.jsxs("label",{className:"warning-info",children:["进行中",`(${i}/${r})`]}):a.jsx("label",{className:"error-info",children:" 任务失败"}):s===z.inProgress?a.jsxs("label",{children:["进行中",`(${i}/${r})`]}):void 0,width:"20%"},{title:"问答对数量",dataIndex:"qaCount",width:"20%"}],L={onChange:(e,t)=>{const a=Array.from(new Set([...k,...e])),s=Array.from(new Map([...w,...t].map((e=>[e.taskId,e]))).values()),l=y.map((e=>e.taskId)).filter((t=>!e.includes(t))),i=a.filter((e=>!l.includes(e))),r=s.filter((e=>!l.includes(e.taskId)));I(i),F(r),j(i),f(r);const n=r.reduce(((e,t)=>e+(t.qaCount||0)),0);C(n>=0)},getCheckboxProps:e=>({name:e.taskName,disabled:e.status!==z.success})};return a.jsxs(r,{centered:!0,title:"选择训练数据",keyboard:!1,maskClosable:!1,styles:{body:{height:"650px",overflowY:"auto"}},width:"50%",open:t,onOk:()=>s(v),onCancel:()=>s(),destroyOnClose:!0,footer:a.jsxs(a.Fragment,{children:[a.jsx("div",{className:ae,children:"只能选择解析完成的推理任务哦"}),a.jsx("div",{className:ee,children:a.jsx(d,{size:"large",type:"primary",shape:"round",className:te,disabled:!_,onClick:()=>s(v),children:"确认选择"})})]}),children:[a.jsx(n,{locale:{emptyText:a.jsx(o,{image:V,description:a.jsx("span",{className:"dataset-table-empty-label",children:"空空如也，去上传本地文件吧~"})})},tableLayout:"fixed",rowKey:"taskId",className:"dataset-table",rowSelection:{type:"checkbox",...L,selectedRowKeys:p},columns:A,dataSource:y,pagination:!1}),a.jsx(B,{total:u.total,pageSize:u.size,page:u.page,OnChange:(e,t)=>{g({total:u.total,page:e,size:t})}})]})},le=e.forwardRef(((t,s)=>{var l;const{tasks:i,testSetConfig:r,testSet:o}=t.modelData,[m,j]=e.useState(r||"自动配置"),[v,f]=e.useState(o?(e=>{const t=w.indexOf(e);return t>-1?25*t:0})(o):50),[y,b]=e.useState(!1),S=localStorage.getItem("trainingData")||"{}",N=JSON.parse(S),[_,C]=e.useState(null==(l=N[0])?void 0:l.modelConfigData),k=null==_?void 0:_.properties.trainConfig.id,[I,F]=e.useState(i||[]),[T,E]=e.useState([]),[A,z]=e.useState({page:1,size:10,total:0});e.useEffect((()=>{R({size:-1}).then((e=>{var t;if(200===(null==(t=null==e?void 0:e.data)?void 0:t.code)){const t=e.data.data;if(E(t),(null==k?void 0:k.length)>0){const e=t.filter((e=>k.includes(e.taskId)));F(e)}}}))}),[]);const D=[{title:"序号",dataIndex:"index",render:(e,t,s)=>{const l=(A.page-1)*A.size+s+1;return a.jsx(a.Fragment,{children:l})},width:"20%"},{title:"任务名称",dataIndex:"taskName",width:"40%"},{title:"问答对数量",dataIndex:"qaCount",width:"40%"}],V={0:a.jsx(u,{title:w[0],children:a.jsx("div",{className:X,children:w[0]})}),25:a.jsx(u,{title:w[1],children:a.jsx("div",{className:X,children:w[1]})}),50:a.jsx(u,{title:w[2],children:a.jsx("div",{className:X,children:w[2]})}),75:a.jsx(u,{title:w[3],children:a.jsx("div",{className:X,children:w[3]})}),100:a.jsx(u,{title:w[4],children:a.jsx("div",{className:X,children:w[4]})})};return e.useImperativeHandle(s,(()=>({getTaskSelectData:()=>({Task:I,TestSetConfig:m,TestSet:w[v/25]})}))),a.jsxs(a.Fragment,{children:[a.jsxs(h,{className:G,children:[a.jsx(x,{span:3.5,className:Z+" boldText",children:"数据配置"}),a.jsxs(x,{span:20.5,className:U,children:[a.jsxs("div",{className:G,children:[a.jsx("div",{className:Q,children:a.jsx("label",{children:"数据集选择:"})}),a.jsx("div",{className:W,children:a.jsxs(c,{direction:"vertical",size:22,children:[a.jsxs("div",{children:[a.jsx(d,{type:"default",className:"default-btn",onClick:()=>b(!0),children:"在训练数据中选择"}),a.jsx(u,{placement:"rightTop",title:"大语言模型SFT任务需要选择多轮对话-非排序类的数据集。",children:a.jsx("img",{className:"frame-child179",style:{marginLeft:"30px"},src:L})})]}),a.jsx(n,{tableLayout:"fixed",rowKey:"taskId",className:"dataset-table",columns:D,dataSource:I,pagination:{current:A.page,pageSize:A.size,onChange:(e,t)=>{z((a=>({...a,page:e,size:t||a.size})))}}})]})})]}),a.jsxs("div",{className:G,children:[a.jsx("div",{className:Q,children:a.jsx("label",{children:"测试集比例:"})}),a.jsx("div",{className:W,children:a.jsxs(c,{direction:"vertical",size:22,children:[a.jsxs("div",{children:[a.jsx(g,{className:"createtask-segmented",size:"large",options:[{label:a.jsx(u,{title:"自动配置",children:a.jsx("a",{onClick:()=>j("自动配置"),className:"自动配置"===m?"model-config-active":"model-config",children:"自动配置"})}),value:"自动配置"},{label:a.jsx(u,{title:"手动配置",children:a.jsx("a",{onClick:()=>j("手动配置"),className:"手动配置"===m?"model-config-active":"model-config",children:"手动配置"})}),value:"手动配置"}],value:m,onChange:j}),a.jsx(u,{placement:"rightTop",title:"测试集的目的是确保模型在面对新的、未见过的数据时能够准确地进行预测和生成。这有助于评估模型的泛化能力，即其在实际应用中的实用性。",children:a.jsx("img",{className:"frame-child179",style:{marginLeft:"30px"},src:L})})]}),"手动配置"===m?a.jsx("div",{children:a.jsxs(c,{size:22,children:[a.jsx("label",{children:"比例"}),a.jsx(p,{defaultValue:v,className:"create-task-slider",dots:!0,tooltip:{formatter:e=>w[(e||0)/25]},step:25,marks:V,value:v,onChange:e=>{f(e)},railStyle:{height:"6px",background:"#F1F6F9",borderTop:"1px solid #E1EAEF",borderBottom:"1px solid #E1EAEF"},trackStyle:{height:"6px",background:"#0FB698",borderTop:"1px solid #0CA287",borderBottom:"1px solid #0CA287"},handleStyle:{},style:{width:"39.25rem",display:"inline-flex",margin:"unset"}})]})}):null]})}),a.jsx("div",{style:{width:"40%"}})]})]})]}),a.jsx(se,{alltasks:T,taskIds:k,visible:y,OnClose:e=>{b(!1),e&&F(e)}})]})})),ie="_training-header_hllq2_15",re="_step-div_hllq2_29",ne="_pre-next-div_hllq2_51",oe="_next-btn_hllq2_91",de={"step-label":"_step-label_f6j9m_1","form-container":"_form-container_f6j9m_15","step-container":"_step-container_f6j9m_37","step-content":"_step-content_f6j9m_43",chart:"_chart_f6j9m_185",estimatetime:"_estimatetime_f6j9m_203",text:"_text_f6j9m_225"},ce=e.forwardRef(((l,i)=>{var r;const{framework:n,batchValue:o,iterationLevel:d,dataset:c,tasks:m,modelId:u,srategy:g,serverSelection:p,arithmeticConfiguration:j}=l.trainingData,[v]=t.useForm();l.trainingData.framework&&v.setFieldsValue({framework:n}),e.useImperativeHandle(i,(()=>({getOverviewData:()=>({Framework:v.getFieldValue("framework"),ServerSelection:v.getFieldValue("serverSelection"),ArithmeticConfiguration:I,ServerConfigId:Number(z)}),validateFields:()=>{v.validateFields()}})));const[f,y]=e.useState(n||[]),[b,S]=e.useState(),[C,k]=e.useState(),[I,w]=e.useState(),[F,T]=e.useState([]),[E,A]=e.useState(""),[z,D]=e.useState(""),[V,B]=e.useState(!0),[R,L]=e.useState([]),[M,O]=e.useState([]),[H,$]=e.useState({estimatedTime:"",unit:""}),J=localStorage.getItem("trainingData")||"{}",K=JSON.parse(J),[P,Y]=e.useState(null==(r=K[0])?void 0:r.modelConfigData),Z=null==P?void 0:P.properties.basicConfigRequest,G=e.useRef();e.useEffect((()=>{G.current=C}),[C]),e.useEffect((()=>{l.selectedModelId&&N(l.selectedModelId).then((e=>{var t;if(200===(null==(t=e.data)?void 0:t.code)){const t=e.data.data;let a=[];Array.isArray(t)?a=t.map((e=>({label:e.serverName,value:e.serverName,id:e.id}))):t&&"object"==typeof t&&(a=[{label:t.serverName,value:t.serverName,id:t.id}]),k(a)}else k([])})).catch((e=>{k([])}))}),[l.selectedModelId]);return e.useEffect((()=>{if(!I)return;const e=R.filter((e=>(null==e?void 0:e.name)===I));O(e)}),[I]),a.jsx(a.Fragment,{children:a.jsxs(h,{className:de["step-container"],children:[a.jsx(x,{span:3.5,className:de["step-label"]+" boldText",children:"资源配置"}),a.jsxs(x,{span:18.5,className:de["step-content"],children:[a.jsx("div",{style:{fontSize:"14px"},children:a.jsxs(t,{form:v,initialValues:{framework:f||((null==P?void 0:P.properties)?null==Z?void 0:Z.trainingFramework:""),serverSelection:b,arithmeticConfiguration:I},onFinish:e=>{},labelCol:{span:8},wrapperCol:{span:22,offset:2},className:de["form-container"],children:[a.jsx(t.Item,{name:"framework",label:"训练框架:",labelAlign:"left",rules:[{required:!0,message:"请选择训练的框架"}],style:{width:"511px"},className:de["form-items"],children:a.jsx(s,{size:"large",showSearch:!0,placeholder:"请选择训练的框架",value:f,onChange:e=>{y(e)},options:[{label:"pytorch",value:"pytorch"}]})}),a.jsx(t.Item,{name:"serverSelection",label:"服务器选择:",labelAlign:"left",rules:[{required:!0,message:"请选择服务器"}],style:{width:"511px"},children:a.jsx(s,{size:"large",showSearch:!0,placeholder:"请选择服务器",value:b,onChange:e=>{var t,a;const s=String(null==(a=null==(t=G.current)?void 0:t.find((t=>t.value===e)))?void 0:a.id);D(s),_(s).then((e=>{var t,a;if((null==(t=e.data)?void 0:t.code)&&200===(null==(a=e.data)?void 0:a.code)){const t=e.data.data.gpu_usage,a={};Object.keys(t).forEach((e=>{const s=`A100_${("0"+(Number(e)+1)).slice(-2)}`;a[s]=t[e]}));const s=Object.entries(a).map((([e,t])=>({name:e,data:t})));L(s);const l=s.map(((e,a)=>({id:Object.keys(t)[a],label:e.name,value:e.name})));T(l)}})),w(""),S(e)},style:{flex:1},options:C})}),a.jsx(t.Item,{name:"arithmeticConfiguration",label:"算力配置:",labelAlign:"left",rules:[{required:!0,message:"请选择算力配置"}],style:{width:"511px"},children:a.jsx(s,{size:"large",showSearch:!0,placeholder:"请选择算力配置",value:I,onChange:e=>{w(e)},style:{flex:1},options:F},z)}),R.length>0&&a.jsx("div",{className:de.chart,children:a.jsx(q,{powerData:M})})]})}),a.jsx("div",{className:de.estimatetime,children:a.jsxs("p",{className:de.text,children:["预估训练时长: ",H&&H.estimatedTime?H.estimatedTime>=3600?Math.floor(H.estimatedTime/3600)+" 小时 "+Math.floor(H.estimatedTime%3600/60)+" 分钟 "+H.estimatedTime%60+" 秒 ":H.estimatedTime>60?Math.floor(H.estimatedTime/60)+" 分钟 "+H.estimatedTime%60+" 秒 ":H.estimatedTime+" 秒 ":"未知"]})})]})]})})})),me="_result-container_1yrr0_1",he="_result-title-container_1yrr0_33",xe="_result-title_1yrr0_33",ue="_result-subtitle_1yrr0_63",ge="_confirm-btn_1yrr0_85",pe=()=>{const e=j();return a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:me,children:[a.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M24%2048C10.746%2048%200%2037.254%200%2024C0%2010.746%2010.746%200%2024%200C37.254%200%2048%2010.746%2048%2024C48%2037.254%2037.254%2048%2024%2048ZM18.648%2033.424C18.8802%2033.6562%2019.1558%2033.8404%2019.4591%2033.9661C19.7625%2034.0918%2020.0876%2034.1565%2020.416%2034.1565C20.7444%2034.1565%2021.0695%2034.0918%2021.3729%2033.9661C21.6762%2033.8404%2021.9518%2033.6562%2022.184%2033.424L37.34%2018.268C37.5722%2018.0358%2037.7564%2017.7602%2037.882%2017.4568C38.0077%2017.1535%2038.0723%2016.8283%2038.0723%2016.5C38.0723%2016.1717%2038.0077%2015.8465%2037.882%2015.5432C37.7564%2015.2398%2037.5722%2014.9642%2037.34%2014.732C37.1078%2014.4998%2036.8322%2014.3157%2036.5288%2014.19C36.2255%2014.0643%2035.9003%2013.9997%2035.572%2013.9997C35.2437%2013.9997%2034.9185%2014.0643%2034.6152%2014.19C34.3118%2014.3157%2034.0362%2014.4998%2033.804%2014.732L20.308%2028.012L14.268%2021.972C13.7991%2021.5031%2013.1631%2021.2397%2012.5%2021.2397C11.8369%2021.2397%2011.2009%2021.5031%2010.732%2021.972C10.2631%2022.4409%209.99967%2023.0769%209.99967%2023.74C9.99967%2024.0683%2010.0643%2024.3935%2010.19%2024.6968C10.3156%2025.0002%2010.4998%2025.2758%2010.732%2025.508L18.648%2033.424Z'%20fill='%230C9883'/%3e%3c/svg%3e"}),a.jsxs("div",{className:he,children:[a.jsx("div",{className:xe,children:"已成功创建微调训练模型"}),a.jsx("div",{className:ue,children:"请返回【微调模型】刷新查看"})]})]}),a.jsx(d,{type:"primary",className:`primary-btn ${ge}`,onClick:()=>{e("/main/finetune")},children:"完成"})]})},je="_training-content_1a4h7_1",ve="_step3_1a4h7_9",fe="_operatearea_1a4h7_21",ye="_strategy_1a4h7_29",be="_parameter_1a4h7_31",Se="_disposition_1a4h7_55",Ne="_dispositionlabel_1a4h7_67",_e=e=>{const t=E.indexOf(e);return t>-1?25*t:0},Ce=e=>{const t=T.indexOf(e);return t>-1?25*t:0},ke=e=>{switch(e){case 1e-5:return 0;case 5e-5:return 25;case 1e-4:return 50;case 5e-4:return 75;case.001:return 100}},Ie=e=>{const t=F.indexOf(e);return t>-1?25*t:0},we=e.forwardRef(((l,i)=>{var r;const{srategy:n,learningValue:o,iterationLevel:d,batchValue:c,trainSetConfig:m}=l.modelData,[h]=t.useForm(),x=localStorage.getItem("trainingData")||"{}",j=JSON.parse(x),[f,y]=e.useState(null==(r=j[0])?void 0:r.modelConfigData),[b,S]=e.useState(0),[N,_]=e.useState(n||(null==f?void 0:f.properties.trainConfig.trainStrategy)),[C,k]=e.useState(m||"自动配置"),[I,w]=e.useState({iterationLevel:d?_e(d):50,learningValue:o?Ce(o):50,batchValue:c?Ie(c):50}),z={0:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:E[0]}),a.jsx(u,{title:A[0],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),25:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:E[1]}),a.jsx(u,{title:A[1],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),50:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:E[2]}),a.jsx(u,{title:A[2],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),75:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:E[3]}),a.jsx(u,{title:A[3],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),100:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:E[4]}),a.jsx(u,{title:A[4],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]})},D={0:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:T[0]}),a.jsx(u,{title:A[0],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),25:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:T[1]}),a.jsx(u,{title:A[1],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),50:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:T[2]}),a.jsx(u,{title:A[2],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),75:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:T[3]}),a.jsx(u,{title:A[3],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),100:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7",width:"40px"},children:T[4]}),a.jsx(u,{title:A[4],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]})},V={0:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:F[0]}),a.jsx(u,{title:A[0],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),25:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:F[1]}),a.jsx(u,{title:A[1],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),50:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:F[2]}),a.jsx(u,{title:A[2],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),75:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:F[3]}),a.jsx(u,{title:A[3],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),100:a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{color:"#8E98A7"},children:F[4]}),a.jsx(u,{title:A[4],children:a.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]})};e.useImperativeHandle(i,(()=>({getTaskSelectData:()=>({TrainSetConfig:C,Srategy:N,IterationLevel:E[I.iterationLevel/25],LearningValue:T[I.learningValue/25],BatchValue:F[I.batchValue/25]})})));return e.useEffect((()=>{l.modelData&&h.setFieldsValue({iteration:I.iterationLevel,learningrate:I.learningValue,batchprocessing:I.batchValue})})),a.jsx(a.Fragment,{children:a.jsxs("div",{className:je,children:[a.jsx("div",{className:ve,style:{width:"15%"},children:"训练配置"}),a.jsxs("div",{className:fe,children:[a.jsxs("div",{className:ye,children:["训练策略:",a.jsx(v,{style:{width:"340px",margin:"-10px 0 0 55px"},children:a.jsx(s,{size:"large",placeholder:"选择要训练的策略",value:N,onChange:_,style:{flex:1},options:["LoRA"].map((e=>({value:e,label:e})))})}),a.jsx(u,{placement:"rightTop",title:"在固定预训练大模型本身的参数的基础上，在保留自注意力模块中原始权重矩阵的基础上，\r\n                    对权重矩阵进行低秩分解，训练过程中只更新低秩部分的参数。",children:a.jsx("img",{className:"frame-child179",style:{marginLeft:"30px"},src:L})})]}),a.jsxs("div",{className:be,children:["参数配置:",a.jsx("div",{style:{margin:"-10px 0 0 55px"},children:a.jsx(t.Item,{children:a.jsx(g,{className:"createtask-segmented",size:"large",options:[{label:a.jsx(u,{title:"自动配置",children:a.jsx("a",{onClick:()=>k("自动配置"),className:"自动配置"===C?"model-config-active":"model-config",children:"自动配置"})}),value:"自动配置"},{label:a.jsx(u,{title:"手动配置",children:a.jsx("a",{onClick:()=>k("手动配置"),className:"手动配置"===C?"model-config-active":"model-config",children:"手动配置"})}),value:"手动配置"}],value:C,onChange:k})})})]}),a.jsx("div",{className:"creative",children:"自动配置"===C?a.jsx("div",{children:a.jsxs(t,{form:h,initialValues:{iteration:25*((null==f?void 0:f.properties.trainConfig.interationNumber)-1)||0,learningrate:ke(null==f?void 0:f.properties.trainConfig.learnRate)||0,batchprocessing:(null==f?void 0:f.properties.trainConfig.batchSize)/2*25||0},children:[a.jsxs("div",{className:Se,children:[a.jsxs("div",{className:Ne,children:[a.jsx("label",{style:{color:"#000000",lineHeight:"19px"},children:"迭代轮次"}),a.jsx(u,{title:"模型在学习过程中完整遍历训练数据集的次数，每次迭代都涉及模型尝试学习并改进其预测能力",children:a.jsx("img",{className:"frame-child179",src:L})})]}),a.jsx(t.Item,{name:"iteration",noStyle:!0,children:a.jsx(p,{className:"create-tasks-slider",dots:!0,tooltip:{formatter:e=>A[(e||0)/25]},step:25,marks:z,value:I.iterationLevel,onChange:e=>{w((t=>({...t,iterationLevel:e}))),k("手动配置")},railStyle:{height:"6px",background:"#F1F6F9",borderTop:"1px solid #E1EAEF",borderBottom:"1px solid #E1EAEF"},trackStyle:{height:"6px",background:"#0FB698",borderTop:"1px solid #0CA287",borderBottom:"1px solid #0CA287"},handleStyle:{},style:{width:"100%",display:"inline-flex",margin:"unset"}})})]}),a.jsxs("div",{className:Se,children:[a.jsxs("div",{className:Ne,children:[a.jsx("label",{style:{color:"#000000",lineHeight:"19px"},children:"学习率"}),a.jsx(u,{title:"在训练过程中调整模型权重的步长大小",children:a.jsx("img",{className:"frame-child179",src:L})})]}),a.jsx(t.Item,{name:"learningrate",noStyle:!0,children:a.jsx(p,{className:"create-tasks-slider",dots:!0,tooltip:{formatter:e=>A[(e||0)/25]},step:25,marks:D,value:I.learningValue,onChange:e=>{w((t=>({...t,learningValue:e}))),k("手动配置")},railStyle:{height:"6px",background:"#F1F6F9",borderTop:"1px solid #E1EAEF",borderBottom:"1px solid #E1EAEF"},trackStyle:{height:"6px",background:"#0FB698",borderTop:"1px solid #0CA287",borderBottom:"1px solid #0CA287"},handleStyle:{},style:{width:"100%",display:"inline-flex",margin:"unset"}})})]}),a.jsxs("div",{className:Se,children:[a.jsxs("div",{className:Ne,children:[a.jsx("label",{style:{color:"#000000",lineHeight:"19px"},children:"Top-p采样"}),a.jsx(u,{title:"通过动态调整候选词池的大小，影响文本的创新性和多样性。",children:a.jsx("img",{className:"frame-child179",src:L})})]}),a.jsx(t.Item,{name:"batchprocessing",noStyle:!0,children:a.jsx(p,{className:"create-tasks-slider",dots:!0,tooltip:{formatter:e=>A[(e||0)/25]},step:25,marks:V,value:I.batchValue,onChange:e=>{w((t=>({...t,batchValue:e}))),k("手动配置")},railStyle:{height:"6px",background:"#F1F6F9",borderTop:"1px solid #E1EAEF",borderBottom:"1px solid #E1EAEF"},trackStyle:{height:"6px",background:"#0FB698",borderTop:"1px solid #0CA287",borderBottom:"1px solid #0CA287"},handleStyle:{},style:{width:"100%",display:"inline-flex",margin:"unset"}})})]})]})}):a.jsx("div",{children:a.jsxs(t,{form:h,initialValues:{iteration:25*((null==f?void 0:f.properties.trainConfig.interationNumber)-1)||0,learningrate:ke(null==f?void 0:f.properties.trainConfig.learnRate)||0,batchprocessing:(null==f?void 0:f.properties.trainConfig.batchSize)/2*25||0},children:[a.jsxs("div",{className:Se,children:[a.jsxs("div",{className:Ne,children:[a.jsx("label",{style:{color:"#000000",lineHeight:"19px"},children:"迭代轮次"}),a.jsx(u,{title:"模型在学习过程中完整遍历训练数据集的次数，每次迭代都涉及模型尝试学习并改进其预测能力",children:a.jsx("img",{className:"frame-child179",src:L})})]}),a.jsx(t.Item,{name:"iteration",noStyle:!0,children:a.jsx(p,{className:"create-tasks-slider",dots:!0,tooltip:{formatter:e=>A[(e||0)/25]},step:25,marks:z,value:I.iterationLevel,onChange:e=>{w((t=>({...t,iterationLevel:e})))},railStyle:{height:"6px",background:"#F1F6F9",borderTop:"1px solid #E1EAEF",borderBottom:"1px solid #E1EAEF"},trackStyle:{height:"6px",background:"#0FB698",borderTop:"1px solid #0CA287",borderBottom:"1px solid #0CA287"},handleStyle:{},style:{width:"100%",display:"inline-flex",margin:"unset"}})})]}),a.jsxs("div",{className:Se,children:[a.jsxs("div",{className:Ne,children:[a.jsx("label",{style:{color:"#000000",lineHeight:"19px"},children:"学习率"}),a.jsx(u,{title:"在训练过程中调整模型权重的步长大小",children:a.jsx("img",{className:"frame-child179",src:L})})]}),a.jsx(t.Item,{name:"learningrate",noStyle:!0,children:a.jsx(p,{className:"create-tasks-slider",dots:!0,tooltip:{formatter:e=>A[(e||0)/25]},step:25,marks:D,value:I.learningValue,onChange:e=>{w((t=>({...t,learningValue:e})))},railStyle:{height:"6px",background:"#F1F6F9",borderTop:"1px solid #E1EAEF",borderBottom:"1px solid #E1EAEF"},trackStyle:{height:"6px",background:"#0FB698",borderTop:"1px solid #0CA287",borderBottom:"1px solid #0CA287"},handleStyle:{},style:{width:"100%",display:"inline-flex",margin:"unset"}})})]}),a.jsxs("div",{className:Se,children:[a.jsxs("div",{className:Ne,children:[a.jsx("label",{style:{color:"#000000",lineHeight:"19px"},children:"Top-p采样"}),a.jsx(u,{title:"通过动态调整候选词池的大小，影响文本的创新性和多样性。",children:a.jsx("img",{className:"frame-child179",src:L})})]}),a.jsx(t.Item,{name:"batchprocessing",noStyle:!0,children:a.jsx(p,{className:"create-tasks-slider",dots:!0,tooltip:{formatter:e=>A[(e||0)/25]},step:25,marks:V,value:I.batchValue,onChange:e=>{w((t=>({...t,batchValue:e})))},railStyle:{height:"6px",background:"#F1F6F9",borderTop:"1px solid #E1EAEF",borderBottom:"1px solid #E1EAEF"},trackStyle:{height:"6px",background:"#0FB698",borderTop:"1px solid #0CA287",borderBottom:"1px solid #0CA287"},handleStyle:{},style:{width:"100%",display:"inline-flex",margin:"unset"}})})]})]})})})]})]})})})),Fe="_step-label_evb8k_1",Te="_overview-container_evb8k_73",Ee="_overview-item_evb8k_87",Ae="_overview-title_evb8k_105",ze="_config-container_evb8k_115",De=({trainingData:e})=>{var t;return a.jsx(a.Fragment,{children:a.jsxs(h,{children:[a.jsx(x,{span:6,className:Fe+" boldText",children:"训练结果"}),a.jsx(x,{span:16,children:a.jsxs("div",{className:Te,children:[a.jsxs("div",{className:Ee,children:[a.jsx("div",{className:Ae,children:"所选训练模型："}),a.jsx("label",{children:e.dataset})]}),a.jsxs("div",{className:Ee,children:[a.jsx("div",{className:Ae,children:"新模型名称："}),a.jsx("label",{children:e.modelName})]}),a.jsxs("div",{className:Ee,children:[a.jsx("div",{className:Ae,children:"新模型介绍："}),a.jsx("label",{children:e.modelIntro})]}),a.jsxs("div",{className:Ee,children:[a.jsx("div",{className:Ae,children:"数据配置："}),a.jsxs("div",{className:ze,children:[null==(t=e.tasks)?void 0:t.map((e=>a.jsxs("label",{children:[e.taskName,"（",e.qaCount,"）"]},e.taskId))),a.jsxs("label",{children:["测试集比例：",e.testSet]})]})]}),a.jsxs("div",{className:Ee,children:[a.jsx("div",{className:Ae,children:"训练配置："}),a.jsxs("div",{className:ze,children:[a.jsxs("label",{children:["训练策略：",e.srategy]}),a.jsxs("label",{children:["迭代次数：",e.iterationLevel]}),a.jsxs("label",{children:["批次大小：",e.batchValue]}),a.jsxs("label",{children:["学习率   ： ",e.learningValue]})]})]}),a.jsxs("div",{className:Ee,children:[a.jsx("div",{className:Ae,children:"训练框架："}),a.jsx("label",{children:e.framework})]}),a.jsxs("div",{className:Ee,children:[a.jsx("div",{className:Ae,children:"服务器选择："}),a.jsx("label",{children:e.serverSelection})]}),a.jsxs("div",{className:Ee,children:[a.jsx("div",{className:Ae,children:"算力配置："}),a.jsx("label",{children:e.arithmeticConfiguration})]})]})})]})})},Ve=()=>{const s=j(),[l,i]=e.useState(0),[r,n]=e.useState(0),[o,m]=e.useState(0),[h,x]=e.useState(!1),u=e.useRef(null),g=e.useRef(null),p=e.useRef(null),v=e.useRef(null),S=e.useRef(null),N=e.useRef(null),_=e.useRef(null),w=e.useRef(null),F=[S,N,_,w];e.useRef(null);const T=e.useRef(null),[E,A]=e.useState(""),[z,D]=e.useState(!1),[V,B]=e.useState(!1),[R,L]=e.useState(""),[q,M]=e.useState(new Object);localStorage.getItem("trainingData"),e.useEffect((()=>{}),[q]),e.useEffect((()=>{const e=setTimeout((()=>{const e=[],t=new Map,a=document.querySelector(".createTaskArea");if(a){const s={root:a,rootMargin:"-80px 0px -60% 0px",threshold:[.1,.3,.5]},l=()=>{if(0===t.size)return;let e=0,a=0;t.forEach(((t,s)=>{t.intersectionRatio>e&&(e=t.intersectionRatio,a=s)})),n(a),m((e=>Math.max(e,a)))};F.forEach(((a,i)=>{if(a.current){const r=new IntersectionObserver((e=>{e.forEach((e=>{e.isIntersecting&&e.intersectionRatio>=.1?t.set(i,e):t.delete(i)})),l()}),s);r.observe(a.current),e.push(r)}}))}return()=>{e.forEach((e=>e.disconnect()))}}),500);return()=>clearTimeout(e)}),[]),e.useEffect((()=>{const e=()=>{if(z&&!V)return void x(!1);const e=document.querySelector(".ScrollbarsCustom-Scroller");if(e){const t=e.scrollTop||0;x(t>170)}},t=((e,t)=>{let a=null;return(...s)=>{a&&clearTimeout(a),a=setTimeout((()=>{e(...s)}),t)}})(e,5),a=setTimeout((()=>{const a=document.querySelector(".ScrollbarsCustom-Scroller");a&&(a.addEventListener("scroll",t,{passive:!0}),e())}),100);return()=>{clearTimeout(a);const e=document.querySelector(".ScrollbarsCustom-Scroller");e&&e.removeEventListener("scroll",t)}}),[z,V]);const O=()=>{let e={...q};if(u.current){const t=u.current.getModelSelectData();e={...e,dataset:t.Dataset,modelIntro:t.Description,modelName:t.Name,testSetRatio:t.ModelType,modelId:t.Id}}if(g.current){const t=g.current.getTaskSelectData();e={...e,tasks:t.Task,testSet:t.TestSet,testSetConfig:t.TestSetConfig}}if(p.current){const t=p.current.getTaskSelectData();e={...e,trainSetConfig:t.TrainSetConfig,srategy:t.Srategy,iterationLevel:t.IterationLevel,learningValue:t.LearningValue,batchValue:t.BatchValue}}if(v.current){const t=v.current.getOverviewData();e={...e,framework:t.Framework,serverSelection:t.ServerSelection,arithmeticConfiguration:t.ArithmeticConfiguration,serverConfigId:t.ServerConfigId}}return M(e),e};e.useEffect((()=>{const e=localStorage.getItem("isOnline");L(e)}),[localStorage.getItem("isOnline")]);const H=e=>{if(!e)return 0;return parseFloat(e.replace("%",""))/100};return a.jsx(a.Fragment,{children:a.jsx("div",{style:{height:"90vh"},children:a.jsxs("div",{className:"createTaskContent",style:{height:"100%"},children:[a.jsxs("div",{ref:T,className:ie,style:{position:"sticky",top:0,background:h?"white":"transparent",zIndex:1e3,height:h?"88px":"44px",paddingTop:h?"44px":"0",boxShadow:h?"0px 4px 4px 0px #7792B91A":"none",transition:"height 0.08s linear, padding-top 0.08s linear, background-color 0s linear"},children:[a.jsxs(c,{size:20,style:{display:"inline-flex",alignItems:"center"},children:[a.jsx(d,{style:{fontSize:"12px",width:"36px",height:"36px"},shape:"circle",icon:a.jsx(f,{}),onClick:()=>{s("/main/finetune"),localStorage.removeItem("trainingData")}}),a.jsx("div",{className:"mediumText",style:{fontSize:"28px",lineHeight:"36px",fontWeight:"500"},children:"微调训练"})]}),a.jsx("div",{className:re,children:z?null:a.jsx(y,{progressDot:(e,{index:t})=>{let s={width:"8px",height:"8px",borderRadius:"50%",boxSizing:"border-box",backgroundColor:"#7792B9"};return(t===r||t<r)&&(s={...s,width:"16px",height:"16px",backgroundColor:"white",border:"4px solid #0FB698",marginInlineStart:"-3px",top:"-3px",position:"relative"}),a.jsx("div",{style:s})},size:"small",current:r,onChange:e=>{i(e);setTimeout((()=>{var t;const a=null==(t=F[e])?void 0:t.current;if(a){const e=document.querySelector(".createTaskArea");if(e){let t=a.offsetTop-50;const s=e.scrollHeight-e.clientHeight;t=Math.min(t,s),t=Math.max(t,0),e.scrollTo({top:t,behavior:"smooth"})}}}),0)},items:[{title:"模型选择"},{title:"数据配置"},{title:"训练配置"},{title:"资源配置"}],style:{height:"4px"}})})]}),z?a.jsxs("div",{className:"createTaskArea",style:{overflowY:"auto",height:"100%"},children:[V?a.jsx(pe,{}):a.jsx("div",{style:{marginTop:"10px"},children:a.jsx(De,{trainingData:q})}),!V&&a.jsx("div",{className:ne,children:a.jsx(d,{type:"primary",className:`primary-btn ${oe}`,onClick:async()=>{var e;const t=O(),a={basicConfigRequest:{computeResource:Array.isArray(t.arithmeticConfiguration)?t.arithmeticConfiguration:[t.arithmeticConfiguration],server:t.serverSelection,serverConfigId:t.serverConfigId,trainingFramework:t.framework},category:1===t.testSetRatio?0:1,modelName:t.modelName,modelId:t.modelId,introduction:t.modelIntro,trainConfig:{id:(null==(e=t.tasks)?void 0:e.length)>0?t.tasks.map((e=>e.taskId)):["86fdfeef55b6bcf4768ab9b0bd7a0cba"],datasetRadio:H(t.testSet),trainStrategy:t.srategy,interationNumber:Number(t.iterationLevel),batchSize:Number(t.batchValue),learnRate:t.learningValue,baseModelId:t.modelId}},s=localStorage.getItem("trainingData");if(s){localStorage.removeItem("trainingData");try{const e=JSON.parse(s);e.length>0&&e[0].modelId&&C(e[0].modelId)}catch(l){}}k(a).then((e=>{var t,a,s,l;200===(null==(t=null==e?void 0:e.data)?void 0:t.code)?(B(!0),I(null==(a=e.data)?void 0:a.data.modelId)):50001===(null==(s=null==e?void 0:e.data)?void 0:s.code)&&b.error(null==(l=null==e?void 0:e.data)?void 0:l.message)}))},children:"开始训练"})})]}):a.jsx("div",{className:"createTaskArea",style:{position:"relative",overflowY:"auto"},children:a.jsxs(t,{children:[a.jsx("div",{id:"model-select-section",ref:S,style:{marginBottom:"60px",borderBottom:"1px solid #D9D9D9",padding:"20px"},children:a.jsx(Y,{ref:u,modelData:q,onModelSelectChange:e=>{A(e)}})}),a.jsx("div",{id:"task-select-section",ref:N,style:{marginBottom:"60px",borderBottom:"1px solid #D9D9D9",padding:"20px"},children:a.jsx(le,{ref:g,modelData:q})}),a.jsx("div",{id:"training-set-section",ref:_,style:{marginBottom:"60px",borderBottom:"1px solid #D9D9D9",padding:"20px 20px 72px 20px"},children:a.jsx(we,{ref:p,modelData:q})}),a.jsx("div",{id:"overview-section",ref:w,style:{marginBottom:"60px",padding:"20px"},children:a.jsx(ce,{ref:v,trainingData:q,selectedModelId:E})}),a.jsx("div",{className:ne,children:a.jsx(d,{type:"primary",className:`primary-btn ${oe}`,onClick:async()=>{var e;const t=await(async()=>{var e,t,a,s;if(u.current){const a=u.current.getModelSelectData();if(!a.Name||!a.Description||!a.Dataset)return null==(t=(e=u.current).validateFields)||t.call(e),{isValid:!1,firstInvalidStepIndex:0}}if(g.current){const e=g.current.getTaskSelectData();if(!e.Task||0===e.Task.length)return{isValid:!1,firstInvalidStepIndex:1}}if(p.current){const e=p.current.getTaskSelectData();if(!e.Srategy||0===e.Srategy.length)return{isValid:!1,firstInvalidStepIndex:2}}if(v.current){const e=v.current.getOverviewData();if(!e.Framework||0===e.Framework.length||void 0===e.ServerSelection||!e.ArithmeticConfiguration||0===e.ArithmeticConfiguration.length||!Number.isInteger(e.ServerConfigId))return null==(s=(a=v.current).validateFields)||s.call(a),{isValid:!1,firstInvalidStepIndex:3}}return{isValid:!0}})();if(t.isValid)O(),D(!0),x(!1);else if(b.error("请将信息填写完整"),void 0!==t.firstInvalidStepIndex){i(t.firstInvalidStepIndex),n(t.firstInvalidStepIndex);const a=50,s=null==(e=F[t.firstInvalidStepIndex])?void 0:e.current;if(s){const e=document.querySelector(".createTaskArea");if(e){let t=s.offsetTop-a;const l=e.scrollHeight-e.clientHeight;t=Math.min(t,l),t=Math.max(t,0),e.scrollTo({top:t,behavior:"smooth"})}}}},children:"下一步"})})]})})]})})})};export{Ve as default};

import{j as e,r as s,V as t,K as a,x as i,T as r,W as n,B as l,t as o,X as c,Y as d,Z as p,_ as x,$ as h,a0 as m,a1 as u,a2 as g,l as f,a3 as j,a4 as v,a5 as y,I as C,a6 as k,f as w,c as b,d as N,k as S,u as F,D as _,a7 as D,p as B,a8 as I,a9 as O,aa as E}from"./react-core-BrQk45h1.js";import{w as U}from"./white-logo-C5vvyXfj.js";import{g as T,u as A}from"./auth-BKEkOdcM.js";import{cO as R}from"./vendor-5xQGrmEQ.js";import{u as L}from"./uploadcomputer-DVakL5y0.js";import{h as H}from"./file-utils-DYD-epjE.js";import{a as z}from"./avatar-1-Bswa0fNs.js";import{a as V,b as G,c as P,d as J,e as M}from"./avatar-6-Dojl3spU.js";import{q as $}from"./service-CkPMTEw7.js";import{a as W}from"./utils-DuyTwmHT.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";const q=({children:s})=>e.jsx("div",{className:"group-child31",children:s});function Z(e,s){return new File([e],s,{type:e.type})}const K=({originNode:s,file:t})=>{const{attributes:a,listeners:i,setNodeRef:r,transform:n,transition:l,isDragging:o}=u({id:t.uid}),c={transform:R.Transform.toString(n),transition:l,cursor:"move",height:"100%"};return e.jsx("div",{ref:r,style:c,className:o?"is-dragging":"",...a,...i,children:"error"===t.status&&o?s.props.children:s})},X=({visible:u,OnClose:g})=>{const[f,j]=s.useState([]),v={name:"file",multiple:!0,accept:".jpg,.png",data:{file_type:"jpg,png"},showUploadList:!1,fileList:f,maxCount:9,onDrop(e){},beforeUpload(e){const s=e;s.status="uploading",s.status=null,s.parseState="解析成功",s.parseProcess=1,s.dataSource=0,s.tags=["军工"];const t=JSON.parse(JSON.stringify(e));return t.name="test.zip",t.status="error",t.parseState="解析成功",t.parseProcess=1,t.dataSource=1,t.tags=[],j([...f,s,t]),!1}},y=t(m,{activationConstraint:{distance:10}});return e.jsx(e.Fragment,{children:e.jsxs(a,{centered:!0,title:e.jsxs("div",{style:{display:"inline-flex",justifyContent:"center",alignItems:"center",gap:"1rem",padding:"35px 12.4% 20px 12.4%"},children:[e.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='44'%20height='31'%20viewBox='0%200%2044%2031'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cmask%20id='mask0_562_1122'%20style='mask-type:alpha'%20maskUnits='userSpaceOnUse'%20x='0'%20y='-1'%20width='44'%20height='32'%3e%3crect%20x='0.5'%20y='-0.5'%20width='43'%20height='31'%20rx='2.66'%20fill='white'%20stroke='white'/%3e%3c/mask%3e%3cg%20mask='url(%23mask0_562_1122)'%3e%3crect%20x='1'%20width='42'%20height='30'%20rx='4'%20fill='url(%23paint0_linear_562_1122)'/%3e%3crect%20x='14.045'%20y='5.045'%20width='23.91'%20height='5.91'%20rx='0.675'%20fill='url(%23paint1_linear_562_1122)'%20stroke='white'%20stroke-width='0.09'/%3e%3cg%20filter='url(%23filter0_d_562_1122)'%3e%3crect%20x='1'%20y='9'%20width='42'%20height='21'%20rx='2.16'%20fill='url(%23paint2_linear_562_1122)'/%3e%3crect%20x='1.045'%20y='9.045'%20width='41.91'%20height='20.91'%20rx='2.115'%20stroke='white'%20stroke-width='0.09'/%3e%3c/g%3e%3c/g%3e%3cdefs%3e%3cfilter%20id='filter0_d_562_1122'%20x='-0.44'%20y='7.2'%20width='44.88'%20height='23.88'%20filterUnits='userSpaceOnUse'%20color-interpolation-filters='sRGB'%3e%3cfeFlood%20flood-opacity='0'%20result='BackgroundImageFix'/%3e%3cfeColorMatrix%20in='SourceAlpha'%20type='matrix'%20values='0%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%20127%200'%20result='hardAlpha'/%3e%3cfeOffset%20dy='-0.36'/%3e%3cfeGaussianBlur%20stdDeviation='0.72'/%3e%3cfeComposite%20in2='hardAlpha'%20operator='out'/%3e%3cfeColorMatrix%20type='matrix'%20values='0%200%200%200%200.560758%200%200%200%200%200.67093%200%200%200%200%200.75071%200%200%200%200.2%200'/%3e%3cfeBlend%20mode='normal'%20in2='BackgroundImageFix'%20result='effect1_dropShadow_562_1122'/%3e%3cfeBlend%20mode='normal'%20in='SourceGraphic'%20in2='effect1_dropShadow_562_1122'%20result='shape'/%3e%3c/filter%3e%3clinearGradient%20id='paint0_linear_562_1122'%20x1='22'%20y1='0'%20x2='22'%20y2='8.49558'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23A0B9CC'/%3e%3cstop%20offset='1'%20stop-color='%23A8BDC9'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_562_1122'%20x1='26'%20y1='5'%20x2='26'%20y2='7.25'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F2F9FD'/%3e%3cstop%20offset='1'%20stop-color='white'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_562_1122'%20x1='7.18948'%20y1='7.04651'%20x2='16.8946'%20y2='30.2532'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23CBE5F2'/%3e%3cstop%20offset='1'%20stop-color='%23DEEDF5'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e"}),e.jsx("div",{style:{fontSize:"24px",fontWeight:700},className:"boldText",children:"意见反馈"})]}),width:1840,styles:{body:{padding:"0 12.4%"}},open:u,onOk:g,onCancel:g,footer:[e.jsx(l,{type:"primary",onClick:g,shape:"round",className:"primary-btn",children:"提交反馈"})],children:[e.jsxs("div",{style:{color:"#6D7279"},children:[e.jsx(i,{color:"#0FB698",style:{marginRight:"0.5rem"}}),"欢迎您向产品开发组反馈宝贵意见"]}),e.jsx(r,{placeholder:"请输入",style:{resize:"none",border:"1px solid #D7DDE7",height:"208px",margin:"1rem 0 2rem 0"}}),e.jsxs("div",{style:{display:"flex",gap:"40px"},children:[e.jsxs("div",{style:{width:"412px"},children:[e.jsxs("div",{style:{color:"#6D7279"},children:[e.jsx(i,{color:"#0FB698",style:{marginRight:"0.5rem"}}),"您可以在此添加问题界面截屏"]}),e.jsxs(n,{...v,className:"createTaskDragger",children:[e.jsxs("div",{children:[e.jsx("img",{className:"createTaskDraggerIcon",alt:"",src:L}),e.jsx("p",{children:"可以将图片文件拖入或"}),e.jsx(l,{type:"default",className:"default-btn",onClick:e=>{e.stopPropagation(),navigator.clipboard.read().then((e=>{if(e)if(f.length<9)for(let s=0;s<e.length;s++){const t=e[s];t.types.forEach((async e=>{if(-1!==e.indexOf("image")){const a=await t.getType(e),i=(s="png",`${Math.random().toString(36).substring(2,8)}.${s}`),r=Z(a,i),n=new FileReader;n.onload=()=>{const e={...r,uid:i.split(".")[0],url:n.result};j([...f,e])},n.readAsDataURL(r)}var s}))}else o.error("最多可以上传9张图片")})).catch((e=>{o.error(`粘贴失败:${e}`)}))},children:"粘贴剪切板内容"})]}),e.jsx("div",{className:"createTaskDraggerInfo",children:e.jsx("label",{className:"crateTaskDraggerLabel",children:"支持的图片格式有jpg、png"})})]})]}),e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{style:{marginBottom:"1rem",color:"#8E98A7"},children:"拖动可以调整图片顺序，最多可以上传9张图片"}),e.jsx(c,{sensors:[y],onDragEnd:({active:e,over:s})=>{e.id!==(null==s?void 0:s.id)&&j((t=>{const a=t.findIndex((s=>s.uid===e.id)),i=t.findIndex((e=>e.uid===(null==s?void 0:s.id)));return h(t,a,i)}))},children:e.jsx(d,{items:f.map((e=>e.uid)),strategy:p,children:e.jsx(x,{listType:"picture-card",fileList:f,showUploadList:{showPreviewIcon:!1,showRemoveIcon:!0},onChange:({fileList:e})=>j(e),itemRender:(s,t)=>e.jsx(K,{originNode:s,file:t})})})})]})]})]})})},Y=({userName:t,visible:i,OnClose:r})=>{const n=s.useRef(null),[o,c]=s.useState(z),[d,p]=s.useState(0),x=[z,V,G,P,J,M,"/assets/avatar-7-3TH4dxH6.png"],h=e=>{p((s=>s+e>0?0:s+e<=-260?-260:s+e))};return e.jsxs(a,{centered:!0,title:"用户头像",width:"28%",open:i,onOk:r,onCancel:r,footer:[],children:[e.jsxs("div",{style:{display:"inline-flex",alignItems:"center",paddingTop:"40px",width:"100%",justifyContent:"center",flexDirection:"column",gap:"24px"},children:[e.jsx(g,{size:140,src:o}),e.jsxs(l,{type:"link",style:{color:"#111111"},onClick:()=>{var e;null==(e=document.getElementById("uploadAvatar"))||e.click()},children:[e.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M20%2017.722C20.595%2017.375%2021%2016.737%2021%2016V5C21%203.897%2020.103%203%2019%203H5C3.897%203%203%203.897%203%205V16C3%2016.736%203.405%2017.375%204%2017.722V18H2V20H22V18H20V17.722ZM5%2016V5H19L19.002%2016H5Z'%20fill='black'/%3e%3c/svg%3e",style:{marginRight:"8px"}}),"选择本地图片"]}),e.jsx("input",{type:"file",style:{display:"none"},id:"uploadAvatar",accept:"image/*",onChange:e=>{const s=e.target.files;s&&s[0]&&s[0]}})]}),e.jsxs("div",{className:"edit-avatar-list",children:[d>=0?null:e.jsx(l,{className:"edit-avatar-pre-btn",onClick:()=>h(80),children:e.jsx(f,{})}),e.jsx("div",{children:"预设头像"}),e.jsxs("div",{style:{display:"inline-flex",gap:"12px",marginTop:"20px",transform:`translateX(${d}px)`,transition:"transform 400ms ease-in-out 0s"},children:[e.jsx("div",{style:{borderRadius:"50%",width:"80px",height:"80px",overflow:"hidden",display:"flex",justifyContent:"space-evenly",alignItems:"center"},children:e.jsx(g,{ref:n,size:83,style:{background:"linear-gradient(313.78deg, #0FB698 16.15%, #113932 81.12%)",fontStyle:"normal",fontWeight:900,fontSize:"16px",lineHeight:"19px",color:"#FFFFFF"},onClick:async()=>{if(n.current){const e=(await H(n.current)).toDataURL();c(e)}},children:e.jsx("label",{className:"blackText",style:{fontSize:32},children:t?t[0]:null})})}),x.map(((s,t)=>e.jsx("img",{style:{cursor:"pointer"},width:80,height:80,src:s,alt:s,onClick:()=>c(s)},s)))]}),d<=-260?null:e.jsx(l,{className:"edit-avatar-next-btn",onClick:()=>h(-80),children:e.jsx(j,{})})]})]})},Q=({OnClose:t})=>{const[a,i]=s.useState("ElioHan"),[r,n]=s.useState("130 4567 8910"),[o,c]=s.useState("学生"),[d,p]=s.useState(z),[x,h]=s.useState(!1),[m,u]=s.useState(!1),[f,j]=s.useState(!1),[S,F]=s.useState(!1),[_,D]=s.useState(!1),[B,I]=s.useState();return s.useEffect((()=>{const e=sessionStorage.getItem("userInfo");e&&I(JSON.parse(e))}),[]),s.useEffect((()=>{B&&(i(B.name),n(B.phone),c(B.job),p(B.avatar))}),[B]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"div23 mediumText",children:[e.jsx(l,{shape:"circle",icon:e.jsx(v,{style:{fontSize:"14px"}}),onClick:()=>t()}),"个人中心"]}),e.jsx("div",{className:"group-div",children:e.jsxs(q,{children:[e.jsx("div",{className:"b6 boldText",children:"基本信息"}),e.jsxs("div",{className:"ellipse-parent6",children:[e.jsx(g,{size:88,src:d}),e.jsx(l,{shape:"circle",className:"avatar-edit-btn",onClick:()=>D(!0),children:e.jsx(y,{})})]}),e.jsxs("div",{className:"info-div",children:[e.jsx("div",{className:"info-item-div",style:x?{}:{paddingLeft:"0.75rem"},children:x?e.jsx(C,{allowClear:!0,value:a,onChange:e=>i(e.target.value),onBlur:()=>h(!1),suffix:e.jsx(l,{type:"text",onClick:()=>{},children:e.jsx(k,{})})}):e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"info-username boldText",children:a})," ",e.jsx(l,{className:"div18",type:"link",onClick:()=>h(!0),children:"修改"})]})}),e.jsx("div",{className:"info-item-div",style:m?{}:{paddingLeft:"0.75rem"},children:m?e.jsx(C,{allowClear:!0,value:r,onChange:e=>n(e.target.value),onBlur:()=>u(!1),suffix:e.jsx(w.Compact,{children:e.jsx(l,{type:"text",onClick:()=>{},children:e.jsx(k,{})})})}):e.jsxs(e.Fragment,{children:[e.jsx("span",{children:r}),e.jsx(l,{className:"div19",type:"link",onClick:()=>u(!m),children:"更改绑定"})]})}),e.jsx("div",{className:"info-item-div",style:f?{}:{paddingLeft:"0.75rem"},children:f?e.jsx(C,{allowClear:!0,value:o,onChange:e=>c(e.target.value),onBlur:()=>j(!1),suffix:e.jsx(w.Compact,{children:e.jsx(l,{type:"text",onClick:()=>{},children:e.jsx(k,{})})})}):e.jsxs(e.Fragment,{children:[e.jsx("span",{children:o}),e.jsx(l,{className:"div18",type:"link",onClick:()=>j(!f),children:"修改"})]})})]})]})}),e.jsxs(b,{gutter:24,style:{position:"relative",top:"28rem",left:"13rem",width:"91.5rem"},children:[e.jsx(N,{span:12,style:{height:"355px",borderRadius:"24px",background:"linear-gradient(0deg, #FFF 0%, rgba(255, 255, 255, 0.40) 100%)",boxShadow:"0px 4px 4px 0px rgba(119, 146, 185, 0.10)"},children:e.jsx("div",{children:"2"})}),e.jsx(N,{span:12,children:"3"})]}),e.jsx(X,{visible:S,OnClose:()=>F(!1)}),e.jsx(Y,{visible:_,OnClose:()=>D(!1),userName:a})]})},ee=e=>Array.isArray(e)?e:null==e?void 0:e.fileList,se=t=>{const{inputContent:a}=t,[i,r]=s.useState(),[n]=B.useForm();s.useEffect((()=>{n.setFieldsValue({inputData:a,logoUrl:[]})}),[a]);const c=()=>{n.resetFields(),t.onClose()};return e.jsx("div",{className:"rename-input",children:e.jsxs(B,{form:n,onFinish:e=>{var s;if(!(null==(s=e.logoUrl)?void 0:s.length))return void o.error("请上传图片");const a={logoName:e.inputData,file:e.logoUrl[0].originFileObj};A(a).then((e=>{200===(null==e?void 0:e.status)&&(o.success("修改成功"),t.fetchAgain())})).catch((e=>{o.error("修改失败")})),c()},labelCol:{span:6},wrapperCol:{span:18,offset:2},children:[e.jsx(B.Item,{label:"重命名",name:"inputData",children:e.jsx(C,{value:i,onChange:e=>r(e.target.value)})}),e.jsx(B.Item,{label:"上传图片",name:"logoUrl",valuePropName:"fileList",getValueFromEvent:ee,rules:[{required:!0,message:"请选择上传文件"}],children:e.jsx(x,{name:"logo",listType:"picture",maxCount:1,accept:".png,.jpg,.jpeg",beforeUpload:()=>!1,children:e.jsx(l,{icon:e.jsx(I,{}),children:"选择文件"})})}),e.jsxs(B.Item,{wrapperCol:{offset:15},children:[e.jsx(l,{type:"primary",htmlType:"submit",style:{marginRight:14},children:"提交"}),e.jsx(l,{onClick:c,children:"取消"})]})]})})},te=()=>{const[t,i]=s.useState(!1),[r,n]=s.useState("EverReachAI Pollux"),[l,o]=s.useState(U),[c,d]=s.useState(),[p,x]=s.useState(0),[h,m]=s.useState(),[u,f]=s.useState(z),j=S(),v=F(),y=["/overview","/main/sourcedata","/main/task","/main/finetune"],[C,k]=s.useState(!1);s.useEffect((()=>{const e=sessionStorage.getItem("userInfo");e&&m(JSON.parse(e))}),[]),s.useEffect((()=>{h&&f(h.avatar)}),[h]),s.useEffect((()=>{y.forEach(((e,s)=>{v.pathname.indexOf(e)>-1&&x(s+1)}))}),[v]);const w=()=>{T().then((e=>{var s;if(e&&200===(null==(s=null==e?void 0:e.data)?void 0:s.code)){const{logoName:s,logoUrl:t}=e.data.data;n(s),o(t)}}))};s.useEffect((()=>{w()}),[r,l]);const b=()=>{i(!1)};return e.jsxs("div",{className:"hello-parent14 headerDefault",children:[e.jsxs("div",{onClick:()=>i(!0),style:{cursor:"pointer"},children:[e.jsx("img",{src:l,className:"logo37"}),e.jsx("div",{className:"everreachai-pollux20 enText",children:r})]}),e.jsxs("div",{className:"parent131",children:[e.jsx("div",{className:1===p?"div691 active-tab":"div691",onClick:()=>{C&&k(!1),x(1),j("/overview")},children:"概览"}),e.jsx("div",{className:2===p?"div691 active-tab":"div691",onClick:()=>{C&&k(!1),x(2),j("/main/sourcedata")},children:"源数据集"}),e.jsx("div",{className:3===p?"div691 active-tab":"div691",onClick:()=>{C&&k(!1),x(3),j("/main/task")},children:"训练数据"}),e.jsx("div",{className:4===p?"div691 active-tab":"div691",onClick:()=>{C&&k(!1),x(4),j("/main/finetune")},children:"模型训练"})]}),e.jsx("div",{className:"frame-child320",style:"/main/user"===v.pathname?{display:"inherit"}:{display:"none"}}),e.jsx(e.Fragment,{children:(()=>{const[t,a]=s.useState("");return s.useEffect((()=>{(async()=>{const e=await $();e&&e.data&&a(e.data.userName)})()}),[]),e.jsxs("b",{className:"hello16",children:["Hello, ",t]})})()}),e.jsx(_,{menu:{items:[{key:"logout",label:"退出登录"}],onClick:({key:e})=>{"logout"===e&&(j("/login"),W.remove("token"))}},children:e.jsx(g,{className:"ellipse-parent29",src:u})}),e.jsx(D,{mask:!1,height:"calc(100% - 4rem)",placement:"bottom",closable:!1,onClose:()=>k(!1),open:C,style:{backgroundColor:"transparent"},contentWrapperStyle:{boxShadow:"unset"},styles:{body:{borderRadius:"24px 24px 0px 0px",background:"linear-gradient(180deg, #E1F3FB 0%, #F3F7FB 13.54%, #F8FAFC 100%)"}},children:e.jsx(Q,{OnClose:()=>k(!1)})}),e.jsx(a,{title:"修改",open:t,okButtonProps:{style:{display:"none"}},cancelButtonProps:{style:{display:"none"}},onCancel:b,children:e.jsx(se,{inputContent:r,onClose:b,fetchAgain:w})})]})},{Content:ae}=O,ie={textAlign:"center",minHeight:"calc(100vh - 52px)",color:"#000",backgroundColor:"#fff"},re=()=>e.jsx(w,{direction:"vertical",style:{width:"100%"},size:[0,48],children:e.jsxs(O,{className:"pageLayout",children:[e.jsx(te,{}),e.jsx(ae,{style:ie,children:e.jsx("div",{className:"main-content",children:e.jsx(E,{})})})]})});export{re as default};

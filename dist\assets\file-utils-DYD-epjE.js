var A="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function e(A){return A&&A.__esModule&&Object.prototype.hasOwnProperty.call(A,"default")?A.default:A}function t(A){if(Object.prototype.hasOwnProperty.call(A,"__esModule"))return A;var e=A.default;if("function"==typeof e){var t=function A(){return this instanceof A?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};t.prototype=e.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(A).forEach((function(e){var r=Object.getOwnPropertyDescriptor(A,e);Object.defineProperty(t,e,r.get?r:{enumerable:!0,get:function(){return A[e]}})})),t}
/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 */
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var r=function(A,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,e){A.__proto__=e}||function(A,e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(A[t]=e[t])})(A,e)};function n(A,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=A}r(A,e),A.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t)}var s=function(){return s=Object.assign||function(A){for(var e,t=1,r=arguments.length;t<r;t++)for(var n in e=arguments[t])Object.prototype.hasOwnProperty.call(e,n)&&(A[n]=e[n]);return A},s.apply(this,arguments)};function i(A,e,t,r){return new(t||(t=Promise))((function(e,n){function s(A){try{o(r.next(A))}catch(e){n(e)}}function i(A){try{o(r.throw(A))}catch(e){n(e)}}function o(A){var r;A.done?e(A.value):(r=A.value,r instanceof t?r:new t((function(A){A(r)}))).then(s,i)}o((r=r.apply(A,[])).next())}))}function o(A,e){var t,r,n,s,i={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function o(s){return function(o){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;i;)try{if(t=1,r&&(n=2&s[0]?r.return:s[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,s[1])).done)return n;switch(r=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return i.label++,{value:s[1],done:!1};case 5:i.label++,r=s[1],s=[0];continue;case 7:s=i.ops.pop(),i.trys.pop();continue;default:if(!(n=i.trys,(n=n.length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){i=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){i.label=s[1];break}if(6===s[0]&&i.label<n[1]){i.label=n[1],n=s;break}if(n&&i.label<n[2]){i.label=n[2],i.ops.push(s);break}n[2]&&i.ops.pop(),i.trys.pop();continue}s=e.call(A,i)}catch(o){s=[6,o],r=0}finally{t=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,o])}}}for(var a=function(){function A(A,e,t,r){this.left=A,this.top=e,this.width=t,this.height=r}return A.prototype.add=function(e,t,r,n){return new A(this.left+e,this.top+t,this.width+r,this.height+n)},A.fromClientRect=function(e,t){return new A(t.left+e.windowBounds.left,t.top+e.windowBounds.top,t.width,t.height)},A.fromDOMRectList=function(e,t){var r=Array.from(t).find((function(A){return 0!==A.width}));return r?new A(r.left+e.windowBounds.left,r.top+e.windowBounds.top,r.width,r.height):A.EMPTY},A.EMPTY=new A(0,0,0,0),A}(),B=function(A,e){return a.fromClientRect(A,e.getBoundingClientRect())},c=function(A){for(var e=[],t=0,r=A.length;t<r;){var n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=A.charCodeAt(t++);56320==(64512&s)?e.push(((1023&n)<<10)+(1023&s)+65536):(e.push(n),t--)}else e.push(n)}return e},u=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var i=A[n];i<=65535?r.push(i):(i-=65536,r.push(55296+(i>>10),i%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",h="undefined"==typeof Uint8Array?[]:new Uint8Array(256),g=0;g<64;g++)h[l.charCodeAt(g)]=g;for(var w="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",d="undefined"==typeof Uint8Array?[]:new Uint8Array(256),f=0;f<64;f++)d[w.charCodeAt(f)]=f;for(var Q=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},C=function(){function A(A,e,t,r,n,s){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=s}return A.prototype.get=function(A){var e;if(A>=0){if(A<55296||A>56319&&A<=65535)return e=((e=this.index[A>>5])<<2)+(31&A),this.data[e];if(A<=65535)return e=((e=this.index[2048+(A-55296>>5)])<<2)+(31&A),this.data[e];if(A<this.highStart)return e=2080+(A>>11),e=this.index[e],e+=A>>5&63,e=((e=this.index[e])<<2)+(31&A),this.data[e];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),U="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",p="undefined"==typeof Uint8Array?[]:new Uint8Array(256),F=0;F<64;F++)p[U.charCodeAt(F)]=F;var m,y,H,E,I,b,v,K,L=10,_=13,x=15,D=17,k=18,S=19,O=20,T=21,M=22,R=24,G=25,V=26,N=27,P=28,X=30,J=32,W=33,Y=34,z=35,Z=37,j=38,q=39,$=40,AA=42,eA=[9001,65288],tA="×",rA="÷",nA=(E=function(A){var e,t,r,n,s,i=.75*A.length,o=A.length,a=0;"="===A[A.length-1]&&(i--,"="===A[A.length-2]&&i--);var B="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?new ArrayBuffer(i):new Array(i),c=Array.isArray(B)?B:new Uint8Array(B);for(e=0;e<o;e+=4)t=d[A.charCodeAt(e)],r=d[A.charCodeAt(e+1)],n=d[A.charCodeAt(e+2)],s=d[A.charCodeAt(e+3)],c[a++]=t<<2|r>>4,c[a++]=(15&r)<<4|n>>2,c[a++]=(3&n)<<6|63&s;return B}("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"),I=Array.isArray(E)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t}(E):new Uint32Array(E),b=Array.isArray(E)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t}(E):new Uint16Array(E),v=Q(b,12,I[4]/2),K=2===I[5]?Q(b,(24+I[4])/2):(m=I,y=Math.ceil((24+I[4])/4),m.slice?m.slice(y,H):new Uint32Array(Array.prototype.slice.call(m,y,H))),new C(I[0],I[1],I[2],I[3],v,K)),sA=[X,36],iA=[1,2,3,5],oA=[L,8],aA=[N,V],BA=iA.concat(oA),cA=[j,q,$,Y,z],uA=[x,_],lA=function(A,e,t,r){var n=r[t];if(Array.isArray(A)?-1!==A.indexOf(n):A===n)for(var s=t;s<=r.length;){if((a=r[++s])===e)return!0;if(a!==L)break}if(n===L)for(s=t;s>0;){var i=r[--s];if(Array.isArray(A)?-1!==A.indexOf(i):A===i)for(var o=t;o<=r.length;){var a;if((a=r[++o])===e)return!0;if(a!==L)break}if(i!==L)break}return!1},hA=function(A,e){for(var t=A;t>=0;){var r=e[t];if(r!==L)return r;t--}return 0},gA=function(A,e,t,r,n){if(0===t[r])return tA;var s=r-1;if(Array.isArray(n)&&!0===n[s])return tA;var i=s-1,o=s+1,a=e[s],B=i>=0?e[i]:0,c=e[o];if(2===a&&3===c)return tA;if(-1!==iA.indexOf(a))return"!";if(-1!==iA.indexOf(c))return tA;if(-1!==oA.indexOf(c))return tA;if(8===hA(s,e))return rA;if(11===nA.get(A[s]))return tA;if((a===J||a===W)&&11===nA.get(A[o]))return tA;if(7===a||7===c)return tA;if(9===a)return tA;if(-1===[L,_,x].indexOf(a)&&9===c)return tA;if(-1!==[D,k,S,R,P].indexOf(c))return tA;if(hA(s,e)===M)return tA;if(lA(23,M,s,e))return tA;if(lA([D,k],T,s,e))return tA;if(lA(12,12,s,e))return tA;if(a===L)return rA;if(23===a||23===c)return tA;if(16===c||16===a)return rA;if(-1!==[_,x,T].indexOf(c)||14===a)return tA;if(36===B&&-1!==uA.indexOf(a))return tA;if(a===P&&36===c)return tA;if(c===O)return tA;if(-1!==sA.indexOf(c)&&a===G||-1!==sA.indexOf(a)&&c===G)return tA;if(a===N&&-1!==[Z,J,W].indexOf(c)||-1!==[Z,J,W].indexOf(a)&&c===V)return tA;if(-1!==sA.indexOf(a)&&-1!==aA.indexOf(c)||-1!==aA.indexOf(a)&&-1!==sA.indexOf(c))return tA;if(-1!==[N,V].indexOf(a)&&(c===G||-1!==[M,x].indexOf(c)&&e[o+1]===G)||-1!==[M,x].indexOf(a)&&c===G||a===G&&-1!==[G,P,R].indexOf(c))return tA;if(-1!==[G,P,R,D,k].indexOf(c))for(var u=s;u>=0;){if((l=e[u])===G)return tA;if(-1===[P,R].indexOf(l))break;u--}if(-1!==[N,V].indexOf(c))for(u=-1!==[D,k].indexOf(a)?i:s;u>=0;){var l;if((l=e[u])===G)return tA;if(-1===[P,R].indexOf(l))break;u--}if(j===a&&-1!==[j,q,Y,z].indexOf(c)||-1!==[q,Y].indexOf(a)&&-1!==[q,$].indexOf(c)||-1!==[$,z].indexOf(a)&&c===$)return tA;if(-1!==cA.indexOf(a)&&-1!==[O,V].indexOf(c)||-1!==cA.indexOf(c)&&a===N)return tA;if(-1!==sA.indexOf(a)&&-1!==sA.indexOf(c))return tA;if(a===R&&-1!==sA.indexOf(c))return tA;if(-1!==sA.concat(G).indexOf(a)&&c===M&&-1===eA.indexOf(A[o])||-1!==sA.concat(G).indexOf(c)&&a===k)return tA;if(41===a&&41===c){for(var h=t[s],g=1;h>0&&41===e[--h];)g++;if(g%2!=0)return tA}return a===J&&c===W?tA:rA},wA=function(A,e){e||(e={lineBreak:"normal",wordBreak:"normal"});var t=function(A,e){void 0===e&&(e="strict");var t=[],r=[],n=[];return A.forEach((function(A,s){var i=nA.get(A);if(i>50?(n.push(!0),i-=50):n.push(!1),-1!==["normal","auto","loose"].indexOf(e)&&-1!==[8208,8211,12316,12448].indexOf(A))return r.push(s),t.push(16);if(4===i||11===i){if(0===s)return r.push(s),t.push(X);var o=t[s-1];return-1===BA.indexOf(o)?(r.push(r[s-1]),t.push(o)):(r.push(s),t.push(X))}return r.push(s),31===i?t.push("strict"===e?T:Z):i===AA||29===i?t.push(X):43===i?A>=131072&&A<=196605||A>=196608&&A<=262141?t.push(Z):t.push(X):void t.push(i)})),[r,t,n]}(A,e.lineBreak),r=t[0],n=t[1],s=t[2];"break-all"!==e.wordBreak&&"break-word"!==e.wordBreak||(n=n.map((function(A){return-1!==[G,X,AA].indexOf(A)?Z:A})));var i="keep-all"===e.wordBreak?s.map((function(e,t){return e&&A[t]>=19968&&A[t]<=40959})):void 0;return[r,n,i]},dA=function(){function A(A,e,t,r){this.codePoints=A,this.required="!"===e,this.start=t,this.end=r}return A.prototype.slice=function(){return u.apply(void 0,this.codePoints.slice(this.start,this.end))},A}(),fA=45,QA=43,CA=-1,UA=function(A){return A>=48&&A<=57},pA=function(A){return UA(A)||A>=65&&A<=70||A>=97&&A<=102},FA=function(A){return 10===A||9===A||32===A},mA=function(A){return function(A){return function(A){return A>=97&&A<=122}(A)||function(A){return A>=65&&A<=90}(A)}(A)||function(A){return A>=128}(A)||95===A},yA=function(A){return mA(A)||UA(A)||A===fA},HA=function(A){return A>=0&&A<=8||11===A||A>=14&&A<=31||127===A},EA=function(A,e){return 92===A&&10!==e},IA=function(A,e,t){return A===fA?mA(e)||EA(e,t):!!mA(A)||!(92!==A||!EA(A,e))},bA=function(A,e,t){return A===QA||A===fA?!!UA(e)||46===e&&UA(t):UA(46===A?e:A)},vA=function(A){var e=0,t=1;A[e]!==QA&&A[e]!==fA||(A[e]===fA&&(t=-1),e++);for(var r=[];UA(A[e]);)r.push(A[e++]);var n=r.length?parseInt(u.apply(void 0,r),10):0;46===A[e]&&e++;for(var s=[];UA(A[e]);)s.push(A[e++]);var i=s.length,o=i?parseInt(u.apply(void 0,s),10):0;69!==A[e]&&101!==A[e]||e++;var a=1;A[e]!==QA&&A[e]!==fA||(A[e]===fA&&(a=-1),e++);for(var B=[];UA(A[e]);)B.push(A[e++]);var c=B.length?parseInt(u.apply(void 0,B),10):0;return t*(n+o*Math.pow(10,-i))*Math.pow(10,a*c)},KA={type:2},LA={type:3},_A={type:4},xA={type:13},DA={type:8},kA={type:21},SA={type:9},OA={type:10},TA={type:11},MA={type:12},RA={type:14},GA={type:23},VA={type:1},NA={type:25},PA={type:24},XA={type:26},JA={type:27},WA={type:28},YA={type:29},zA={type:31},ZA={type:32},jA=function(){function A(){this._value=[]}return A.prototype.write=function(A){this._value=this._value.concat(c(A))},A.prototype.read=function(){for(var A=[],e=this.consumeToken();e!==ZA;)A.push(e),e=this.consumeToken();return A},A.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case 34:return this.consumeStringToken(34);case 35:var e=this.peekCodePoint(0),t=this.peekCodePoint(1),r=this.peekCodePoint(2);if(yA(e)||EA(t,r)){var n=IA(e,t,r)?2:1;return{type:5,value:this.consumeName(),flags:n}}break;case 36:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),xA;break;case 39:return this.consumeStringToken(39);case 40:return KA;case 41:return LA;case 42:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),RA;break;case QA:if(bA(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 44:return _A;case fA:var s=A,i=this.peekCodePoint(0),o=this.peekCodePoint(1);if(bA(s,i,o))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(IA(s,i,o))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(i===fA&&62===o)return this.consumeCodePoint(),this.consumeCodePoint(),PA;break;case 46:if(bA(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 47:if(42===this.peekCodePoint(0))for(this.consumeCodePoint();;){var a=this.consumeCodePoint();if(42===a&&47===(a=this.consumeCodePoint()))return this.consumeToken();if(a===CA)return this.consumeToken()}break;case 58:return XA;case 59:return JA;case 60:if(33===this.peekCodePoint(0)&&this.peekCodePoint(1)===fA&&this.peekCodePoint(2)===fA)return this.consumeCodePoint(),this.consumeCodePoint(),NA;break;case 64:var B=this.peekCodePoint(0),c=this.peekCodePoint(1),l=this.peekCodePoint(2);if(IA(B,c,l))return{type:7,value:this.consumeName()};break;case 91:return WA;case 92:if(EA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case 93:return YA;case 61:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),DA;break;case 123:return TA;case 125:return MA;case 117:case 85:var h=this.peekCodePoint(0),g=this.peekCodePoint(1);return h!==QA||!pA(g)&&63!==g||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case 124:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),SA;if(124===this.peekCodePoint(0))return this.consumeCodePoint(),kA;break;case 126:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),OA;break;case CA:return ZA}return FA(A)?(this.consumeWhiteSpace(),zA):UA(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):mA(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:u(A)}},A.prototype.consumeCodePoint=function(){var A=this._value.shift();return void 0===A?-1:A},A.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},A.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},A.prototype.consumeUnicodeRangeToken=function(){for(var A=[],e=this.consumeCodePoint();pA(e)&&A.length<6;)A.push(e),e=this.consumeCodePoint();for(var t=!1;63===e&&A.length<6;)A.push(e),e=this.consumeCodePoint(),t=!0;if(t)return{type:30,start:parseInt(u.apply(void 0,A.map((function(A){return 63===A?48:A}))),16),end:parseInt(u.apply(void 0,A.map((function(A){return 63===A?70:A}))),16)};var r=parseInt(u.apply(void 0,A),16);if(this.peekCodePoint(0)===fA&&pA(this.peekCodePoint(1))){this.consumeCodePoint(),e=this.consumeCodePoint();for(var n=[];pA(e)&&n.length<6;)n.push(e),e=this.consumeCodePoint();return{type:30,start:r,end:parseInt(u.apply(void 0,n),16)}}return{type:30,start:r,end:r}},A.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return"url"===A.toLowerCase()&&40===this.peekCodePoint(0)?(this.consumeCodePoint(),this.consumeUrlToken()):40===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},A.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===CA)return{type:22,value:""};var e=this.peekCodePoint(0);if(39===e||34===e){var t=this.consumeStringToken(this.consumeCodePoint());return 0===t.type&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===CA||41===this.peekCodePoint(0))?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),GA)}for(;;){var r=this.consumeCodePoint();if(r===CA||41===r)return{type:22,value:u.apply(void 0,A)};if(FA(r))return this.consumeWhiteSpace(),this.peekCodePoint(0)===CA||41===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:22,value:u.apply(void 0,A)}):(this.consumeBadUrlRemnants(),GA);if(34===r||39===r||40===r||HA(r))return this.consumeBadUrlRemnants(),GA;if(92===r){if(!EA(r,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),GA;A.push(this.consumeEscapedCodePoint())}else A.push(r)}},A.prototype.consumeWhiteSpace=function(){for(;FA(this.peekCodePoint(0));)this.consumeCodePoint()},A.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(41===A||A===CA)return;EA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},A.prototype.consumeStringSlice=function(A){for(var e="";A>0;){var t=Math.min(5e4,A);e+=u.apply(void 0,this._value.splice(0,t)),A-=t}return this._value.shift(),e},A.prototype.consumeStringToken=function(A){for(var e="",t=0;;){var r=this._value[t];if(r===CA||void 0===r||r===A)return{type:0,value:e+=this.consumeStringSlice(t)};if(10===r)return this._value.splice(0,t),VA;if(92===r){var n=this._value[t+1];n!==CA&&void 0!==n&&(10===n?(e+=this.consumeStringSlice(t),t=-1,this._value.shift()):EA(r,n)&&(e+=this.consumeStringSlice(t),e+=u(this.consumeEscapedCodePoint()),t=-1))}t++}},A.prototype.consumeNumber=function(){var A=[],e=4,t=this.peekCodePoint(0);for(t!==QA&&t!==fA||A.push(this.consumeCodePoint());UA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var r=this.peekCodePoint(1);if(46===t&&UA(r))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;UA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0),r=this.peekCodePoint(1);var n=this.peekCodePoint(2);if((69===t||101===t)&&((r===QA||r===fA)&&UA(n)||UA(r)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;UA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[vA(A),e]},A.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),e=A[0],t=A[1],r=this.peekCodePoint(0),n=this.peekCodePoint(1),s=this.peekCodePoint(2);return IA(r,n,s)?{type:15,number:e,flags:t,unit:this.consumeName()}:37===r?(this.consumeCodePoint(),{type:16,number:e,flags:t}):{type:17,number:e,flags:t}},A.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(pA(A)){for(var e=u(A);pA(this.peekCodePoint(0))&&e.length<6;)e+=u(this.consumeCodePoint());FA(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(e,16);return 0===t||function(A){return A>=55296&&A<=57343}(t)||t>1114111?65533:t}return A===CA?65533:A},A.prototype.consumeName=function(){for(var A="";;){var e=this.consumeCodePoint();if(yA(e))A+=u(e);else{if(!EA(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),A;A+=u(this.consumeEscapedCodePoint())}}},A}(),qA=function(){function A(A){this._tokens=A}return A.create=function(e){var t=new jA;return t.write(e),new A(t.read())},A.parseValue=function(e){return A.create(e).parseComponentValue()},A.parseValues=function(e){return A.create(e).parseComponentValues()},A.prototype.parseComponentValue=function(){for(var A=this.consumeToken();31===A.type;)A=this.consumeToken();if(32===A.type)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var e=this.consumeComponentValue();do{A=this.consumeToken()}while(31===A.type);if(32===A.type)return e;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},A.prototype.parseComponentValues=function(){for(var A=[];;){var e=this.consumeComponentValue();if(32===e.type)return A;A.push(e),A.push()}},A.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},A.prototype.consumeSimpleBlock=function(A){for(var e={type:A,values:[]},t=this.consumeToken();;){if(32===t.type||oe(t,A))return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue()),t=this.consumeToken()}},A.prototype.consumeFunction=function(A){for(var e={name:A.value,values:[],type:18};;){var t=this.consumeToken();if(32===t.type||3===t.type)return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue())}},A.prototype.consumeToken=function(){var A=this._tokens.shift();return void 0===A?ZA:A},A.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},A}(),$A=function(A){return 15===A.type},Ae=function(A){return 17===A.type},ee=function(A){return 20===A.type},te=function(A){return 0===A.type},re=function(A,e){return ee(A)&&A.value===e},ne=function(A){return 31!==A.type},se=function(A){return 31!==A.type&&4!==A.type},ie=function(A){var e=[],t=[];return A.forEach((function(A){if(4===A.type){if(0===t.length)throw new Error("Error parsing function args, zero tokens for arg");return e.push(t),void(t=[])}31!==A.type&&t.push(A)})),t.length&&e.push(t),e},oe=function(A,e){return 11===e&&12===A.type||(28===e&&29===A.type||2===e&&3===A.type)},ae=function(A){return 17===A.type||15===A.type},Be=function(A){return 16===A.type||ae(A)},ce=function(A){return A.length>1?[A[0],A[1]]:[A[0]]},ue={type:17,number:0,flags:4},le={type:16,number:50,flags:4},he={type:16,number:100,flags:4},ge=function(A,e,t){var r=A[0],n=A[1];return[we(r,e),we(void 0!==n?n:r,t)]},we=function(A,e){if(16===A.type)return A.number/100*e;if($A(A))switch(A.unit){case"rem":case"em":return 16*A.number;default:return A.number}return A.number},de="grad",fe="turn",Qe=function(A,e){if(15===e.type)switch(e.unit){case"deg":return Math.PI*e.number/180;case de:return Math.PI/200*e.number;case"rad":return e.number;case fe:return 2*Math.PI*e.number}throw new Error("Unsupported angle type")},Ce=function(A){return 15===A.type&&("deg"===A.unit||A.unit===de||"rad"===A.unit||A.unit===fe)},Ue=function(A){switch(A.filter(ee).map((function(A){return A.value})).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[ue,ue];case"to top":case"bottom":return pe(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[ue,he];case"to right":case"left":return pe(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[he,he];case"to bottom":case"top":return pe(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[he,ue];case"to left":case"right":return pe(270)}return 0},pe=function(A){return Math.PI*A/180},Fe=function(A,e){if(18===e.type){var t=Ke[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return t(A,e.values)}if(5===e.type){if(3===e.value.length){var r=e.value.substring(0,1),n=e.value.substring(1,2),s=e.value.substring(2,3);return He(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),1)}if(4===e.value.length){r=e.value.substring(0,1),n=e.value.substring(1,2),s=e.value.substring(2,3);var i=e.value.substring(3,4);return He(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),parseInt(i+i,16)/255)}if(6===e.value.length){r=e.value.substring(0,2),n=e.value.substring(2,4),s=e.value.substring(4,6);return He(parseInt(r,16),parseInt(n,16),parseInt(s,16),1)}if(8===e.value.length){r=e.value.substring(0,2),n=e.value.substring(2,4),s=e.value.substring(4,6),i=e.value.substring(6,8);return He(parseInt(r,16),parseInt(n,16),parseInt(s,16),parseInt(i,16)/255)}}if(20===e.type){var o=_e[e.value.toUpperCase()];if(void 0!==o)return o}return _e.TRANSPARENT},me=function(A){return 0==(255&A)},ye=function(A){var e=255&A,t=255&A>>8,r=255&A>>16,n=255&A>>24;return e<255?"rgba("+n+","+r+","+t+","+e/255+")":"rgb("+n+","+r+","+t+")"},He=function(A,e,t,r){return(A<<24|e<<16|t<<8|Math.round(255*r)<<0)>>>0},Ee=function(A,e){if(17===A.type)return A.number;if(16===A.type){var t=3===e?1:255;return 3===e?A.number/100*t:Math.round(A.number/100*t)}return 0},Ie=function(A,e){var t=e.filter(se);if(3===t.length){var r=t.map(Ee),n=r[0],s=r[1],i=r[2];return He(n,s,i,1)}if(4===t.length){var o=t.map(Ee),a=(n=o[0],s=o[1],i=o[2],o[3]);return He(n,s,i,a)}return 0};function be(A,e,t){return t<0&&(t+=1),t>=1&&(t-=1),t<1/6?(e-A)*t*6+A:t<.5?e:t<2/3?6*(e-A)*(2/3-t)+A:A}var ve=function(A,e){var t=e.filter(se),r=t[0],n=t[1],s=t[2],i=t[3],o=(17===r.type?pe(r.number):Qe(A,r))/(2*Math.PI),a=Be(n)?n.number/100:0,B=Be(s)?s.number/100:0,c=void 0!==i&&Be(i)?we(i,1):1;if(0===a)return He(255*B,255*B,255*B,1);var u=B<=.5?B*(a+1):B+a-B*a,l=2*B-u,h=be(l,u,o+1/3),g=be(l,u,o),w=be(l,u,o-1/3);return He(255*h,255*g,255*w,c)},Ke={hsl:ve,hsla:ve,rgb:Ie,rgba:Ie},Le=function(A,e){return Fe(A,qA.create(e).parseComponentValue())},_e={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},xe={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(ee(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},De={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},ke=function(A,e){var t=Fe(A,e[0]),r=e[1];return r&&Be(r)?{color:t,stop:r}:{color:t,stop:null}},Se=function(A,e){var t=A[0],r=A[A.length-1];null===t.stop&&(t.stop=ue),null===r.stop&&(r.stop=he);for(var n=[],s=0,i=0;i<A.length;i++){var o=A[i].stop;if(null!==o){var a=we(o,e);a>s?n.push(a):n.push(s),s=a}else n.push(null)}var B=null;for(i=0;i<n.length;i++){var c=n[i];if(null===c)null===B&&(B=i);else if(null!==B){for(var u=i-B,l=(c-n[B-1])/(u+1),h=1;h<=u;h++)n[B+h-1]=l*h;B=null}}return A.map((function(A,t){return{color:A.color,stop:Math.max(Math.min(1,n[t]/e),0)}}))},Oe=function(A,e,t){var r="number"==typeof A?A:function(A,e,t){var r=e/2,n=t/2,s=we(A[0],e)-r,i=n-we(A[1],t);return(Math.atan2(i,s)+2*Math.PI)%(2*Math.PI)}(A,e,t),n=Math.abs(e*Math.sin(r))+Math.abs(t*Math.cos(r)),s=e/2,i=t/2,o=n/2,a=Math.sin(r-Math.PI/2)*o,B=Math.cos(r-Math.PI/2)*o;return[n,s-B,s+B,i-a,i+a]},Te=function(A,e){return Math.sqrt(A*A+e*e)},Me=function(A,e,t,r,n){return[[0,0],[0,e],[A,0],[A,e]].reduce((function(A,e){var s=e[0],i=e[1],o=Te(t-s,r-i);return(n?o<A.optimumDistance:o>A.optimumDistance)?{optimumCorner:e,optimumDistance:o}:A}),{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},Re=function(A,e){var t=pe(180),r=[];return ie(e).forEach((function(e,n){if(0===n){var s=e[0];if(20===s.type&&-1!==["top","left","right","bottom"].indexOf(s.value))return void(t=Ue(e));if(Ce(s))return void(t=(Qe(A,s)+pe(270))%pe(360))}var i=ke(A,e);r.push(i)})),{angle:t,stops:r,type:1}},Ge="closest-side",Ve="farthest-side",Ne="closest-corner",Pe="farthest-corner",Xe="circle",Je="ellipse",We="cover",Ye="contain",ze=function(A,e){var t=0,r=3,n=[],s=[];return ie(e).forEach((function(e,i){var o=!0;if(0===i?o=e.reduce((function(A,e){if(ee(e))switch(e.value){case"center":return s.push(le),!1;case"top":case"left":return s.push(ue),!1;case"right":case"bottom":return s.push(he),!1}else if(Be(e)||ae(e))return s.push(e),!1;return A}),o):1===i&&(o=e.reduce((function(A,e){if(ee(e))switch(e.value){case Xe:return t=0,!1;case Je:return t=1,!1;case Ye:case Ge:return r=0,!1;case Ve:return r=1,!1;case Ne:return r=2,!1;case We:case Pe:return r=3,!1}else if(ae(e)||Be(e))return Array.isArray(r)||(r=[]),r.push(e),!1;return A}),o)),o){var a=ke(A,e);n.push(a)}})),{size:r,shape:t,stops:n,position:s,type:2}},Ze=function(A,e){if(22===e.type){var t={url:e.value,type:0};return A.cache.addImage(e.value),t}if(18===e.type){var r=$e[e.name];if(void 0===r)throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return r(A,e.values)}throw new Error("Unsupported image type "+e.type)};var je,qe,$e={"linear-gradient":function(A,e){var t=pe(180),r=[];return ie(e).forEach((function(e,n){if(0===n){var s=e[0];if(20===s.type&&"to"===s.value)return void(t=Ue(e));if(Ce(s))return void(t=Qe(A,s))}var i=ke(A,e);r.push(i)})),{angle:t,stops:r,type:1}},"-moz-linear-gradient":Re,"-ms-linear-gradient":Re,"-o-linear-gradient":Re,"-webkit-linear-gradient":Re,"radial-gradient":function(A,e){var t=0,r=3,n=[],s=[];return ie(e).forEach((function(e,i){var o=!0;if(0===i){var a=!1;o=e.reduce((function(A,e){if(a)if(ee(e))switch(e.value){case"center":return s.push(le),A;case"top":case"left":return s.push(ue),A;case"right":case"bottom":return s.push(he),A}else(Be(e)||ae(e))&&s.push(e);else if(ee(e))switch(e.value){case Xe:return t=0,!1;case Je:return t=1,!1;case"at":return a=!0,!1;case Ge:return r=0,!1;case We:case Ve:return r=1,!1;case Ye:case Ne:return r=2,!1;case Pe:return r=3,!1}else if(ae(e)||Be(e))return Array.isArray(r)||(r=[]),r.push(e),!1;return A}),o)}if(o){var B=ke(A,e);n.push(B)}})),{size:r,shape:t,stops:n,position:s,type:2}},"-moz-radial-gradient":ze,"-ms-radial-gradient":ze,"-o-radial-gradient":ze,"-webkit-radial-gradient":ze,"-webkit-gradient":function(A,e){var t=pe(180),r=[],n=1;return ie(e).forEach((function(e,t){var s=e[0];if(0===t){if(ee(s)&&"linear"===s.value)return void(n=1);if(ee(s)&&"radial"===s.value)return void(n=2)}if(18===s.type)if("from"===s.name){var i=Fe(A,s.values[0]);r.push({stop:ue,color:i})}else if("to"===s.name){i=Fe(A,s.values[0]);r.push({stop:he,color:i})}else if("color-stop"===s.name){var o=s.values.filter(se);if(2===o.length){i=Fe(A,o[1]);var a=o[0];Ae(a)&&r.push({stop:{type:16,number:100*a.number,flags:a.flags},color:i})}}})),1===n?{angle:(t+pe(180))%pe(360),stops:r,type:n}:{size:3,shape:0,stops:r,position:[],type:n}}},At={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e.filter((function(A){return se(A)&&function(A){return!(20===A.type&&"none"===A.value||18===A.type&&!$e[A.name])}(A)})).map((function(e){return Ze(A,e)}))}},et={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(ee(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},tt={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(A,e){return ie(e).map((function(A){return A.filter(Be)})).map(ce)}},rt={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(A,e){return ie(e).map((function(A){return A.filter(ee).map((function(A){return A.value})).join(" ")})).map(nt)}},nt=function(A){switch(A){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;default:return 0}};(qe=je||(je={})).AUTO="auto",qe.CONTAIN="contain",qe.COVER="cover";var st,it,ot={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(A,e){return ie(e).map((function(A){return A.filter(at)}))}},at=function(A){return ee(A)||Be(A)},Bt=function(A){return{name:"border-"+A+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},ct=Bt("top"),ut=Bt("right"),lt=Bt("bottom"),ht=Bt("left"),gt=function(A){return{name:"border-radius-"+A,initialValue:"0 0",prefix:!1,type:1,parse:function(A,e){return ce(e.filter(Be))}}},wt=gt("top-left"),dt=gt("top-right"),ft=gt("bottom-right"),Qt=gt("bottom-left"),Ct=function(A){return{name:"border-"+A+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,e){switch(e){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},Ut=Ct("top"),pt=Ct("right"),Ft=Ct("bottom"),mt=Ct("left"),yt=function(A){return{name:"border-"+A+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return $A(e)?e.number:0}}},Ht=yt("top"),Et=yt("right"),It=yt("bottom"),bt=yt("left"),vt={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Kt={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(A,e){return"rtl"===e?1:0}},Lt={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(A,e){return e.filter(ee).reduce((function(A,e){return A|_t(e.value)}),0)}},_t=function(A){switch(A){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},xt={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},Dt={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(A,e){return 20===e.type&&"normal"===e.value?0:17===e.type||15===e.type?e.number:0}};(it=st||(st={})).NORMAL="normal",it.STRICT="strict";var kt,St,Ot={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"strict"===e?st.STRICT:st.NORMAL}},Tt={name:"line-height",initialValue:"normal",prefix:!1,type:4},Mt=function(A,e){return ee(A)&&"normal"===A.value?1.2*e:17===A.type?e*A.number:Be(A)?we(A,e):e},Rt={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(A,e){return 20===e.type&&"none"===e.value?null:Ze(A,e)}},Gt={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(A,e){return"inside"===e?0:1}},Vt={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;default:return-1}}},Nt=function(A){return{name:"margin-"+A,initialValue:"0",prefix:!1,type:4}},Pt=Nt("top"),Xt=Nt("right"),Jt=Nt("bottom"),Wt=Nt("left"),Yt={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(A,e){return e.filter(ee).map((function(A){switch(A.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;default:return 0}}))}},zt={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"break-word"===e?"break-word":"normal"}},Zt=function(A){return{name:"padding-"+A,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},jt=Zt("top"),qt=Zt("right"),$t=Zt("bottom"),Ar=Zt("left"),er={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(A,e){switch(e){case"right":return 2;case"center":case"justify":return 1;default:return 0}}},tr={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(A,e){switch(e){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},rr={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&re(e[0],"none")?[]:ie(e).map((function(e){for(var t={color:_e.TRANSPARENT,offsetX:ue,offsetY:ue,blur:ue},r=0,n=0;n<e.length;n++){var s=e[n];ae(s)?(0===r?t.offsetX=s:1===r?t.offsetY=s:t.blur=s,r++):t.color=Fe(A,s)}return t}))}},nr={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},sr={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(A,e){if(20===e.type&&"none"===e.value)return null;if(18===e.type){var t=ir[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return t(e.values)}return null}},ir={matrix:function(A){var e=A.filter((function(A){return 17===A.type})).map((function(A){return A.number}));return 6===e.length?e:null},matrix3d:function(A){var e=A.filter((function(A){return 17===A.type})).map((function(A){return A.number})),t=e[0],r=e[1];e[2],e[3];var n=e[4],s=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var i=e[12],o=e[13];return e[14],e[15],16===e.length?[t,r,n,s,i,o]:null}},or={type:16,number:50,flags:4},ar=[or,or],Br={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(A,e){var t=e.filter(Be);return 2!==t.length?ar:[t[0],t[1]]}},cr={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"hidden":return 1;case"collapse":return 2;default:return 0}}};(St=kt||(kt={})).NORMAL="normal",St.BREAK_ALL="break-all",St.KEEP_ALL="keep-all";for(var ur={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"break-all":return kt.BREAK_ALL;case"keep-all":return kt.KEEP_ALL;default:return kt.NORMAL}}},lr={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(A,e){if(20===e.type)return{auto:!0,order:0};if(Ae(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},hr=function(A,e){if(15===e.type)switch(e.unit.toLowerCase()){case"s":return 1e3*e.number;case"ms":return e.number}throw new Error("Unsupported time type")},gr={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(A,e){return Ae(e)?e.number:1}},wr={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},dr={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(A,e){return e.filter(ee).map((function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0})).filter((function(A){return 0!==A}))}},fr={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(A,e){var t=[],r=[];return e.forEach((function(A){switch(A.type){case 20:case 0:t.push(A.value);break;case 17:t.push(A.number.toString());break;case 4:r.push(t.join(" ")),t.length=0}})),t.length&&r.push(t.join(" ")),r.map((function(A){return-1===A.indexOf(" ")?A:"'"+A+"'"}))}},Qr={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Cr={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(A,e){return Ae(e)?e.number:ee(e)&&"bold"===e.value?700:400}},Ur={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return e.filter(ee).map((function(A){return A.value}))}},pr={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"oblique":return"oblique";case"italic":return"italic";default:return"normal"}}},Fr=function(A,e){return 0!=(A&e)},mr={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e}},yr={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;for(var r=[],n=e.filter(ne),s=0;s<n.length;s++){var i=n[s],o=n[s+1];if(20===i.type){var a=o&&Ae(o)?o.number:1;r.push({counter:i.value,increment:a})}}return r}},Hr={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return[];for(var t=[],r=e.filter(ne),n=0;n<r.length;n++){var s=r[n],i=r[n+1];if(ee(s)&&"none"!==s.value){var o=i&&Ae(i)?i.number:0;t.push({counter:s.value,reset:o})}}return t}},Er={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(A,e){return e.filter($A).map((function(e){return hr(A,e)}))}},Ir={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;var r=[],n=e.filter(te);if(n.length%2!=0)return null;for(var s=0;s<n.length;s+=2){var i=n[s].value,o=n[s+1].value;r.push({open:i,close:o})}return r}},br=function(A,e,t){if(!A)return"";var r=A[Math.min(e,A.length-1)];return r?t?r.open:r.close:""},vr={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&re(e[0],"none")?[]:ie(e).map((function(e){for(var t={color:255,offsetX:ue,offsetY:ue,blur:ue,spread:ue,inset:!1},r=0,n=0;n<e.length;n++){var s=e[n];re(s,"inset")?t.inset=!0:ae(s)?(0===r?t.offsetX=s:1===r?t.offsetY=s:2===r?t.blur=s:t.spread=s,r++):t.color=Fe(A,s)}return t}))}},Kr={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(A,e){var t=[];return e.filter(ee).forEach((function(A){switch(A.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2)}})),[0,1,2].forEach((function(A){-1===t.indexOf(A)&&t.push(A)})),t}},Lr={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},_r={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return $A(e)?e.number:0}},xr=function(){function A(A,e){var t,r;this.animationDuration=Sr(A,Er,e.animationDuration),this.backgroundClip=Sr(A,xe,e.backgroundClip),this.backgroundColor=Sr(A,De,e.backgroundColor),this.backgroundImage=Sr(A,At,e.backgroundImage),this.backgroundOrigin=Sr(A,et,e.backgroundOrigin),this.backgroundPosition=Sr(A,tt,e.backgroundPosition),this.backgroundRepeat=Sr(A,rt,e.backgroundRepeat),this.backgroundSize=Sr(A,ot,e.backgroundSize),this.borderTopColor=Sr(A,ct,e.borderTopColor),this.borderRightColor=Sr(A,ut,e.borderRightColor),this.borderBottomColor=Sr(A,lt,e.borderBottomColor),this.borderLeftColor=Sr(A,ht,e.borderLeftColor),this.borderTopLeftRadius=Sr(A,wt,e.borderTopLeftRadius),this.borderTopRightRadius=Sr(A,dt,e.borderTopRightRadius),this.borderBottomRightRadius=Sr(A,ft,e.borderBottomRightRadius),this.borderBottomLeftRadius=Sr(A,Qt,e.borderBottomLeftRadius),this.borderTopStyle=Sr(A,Ut,e.borderTopStyle),this.borderRightStyle=Sr(A,pt,e.borderRightStyle),this.borderBottomStyle=Sr(A,Ft,e.borderBottomStyle),this.borderLeftStyle=Sr(A,mt,e.borderLeftStyle),this.borderTopWidth=Sr(A,Ht,e.borderTopWidth),this.borderRightWidth=Sr(A,Et,e.borderRightWidth),this.borderBottomWidth=Sr(A,It,e.borderBottomWidth),this.borderLeftWidth=Sr(A,bt,e.borderLeftWidth),this.boxShadow=Sr(A,vr,e.boxShadow),this.color=Sr(A,vt,e.color),this.direction=Sr(A,Kt,e.direction),this.display=Sr(A,Lt,e.display),this.float=Sr(A,xt,e.cssFloat),this.fontFamily=Sr(A,fr,e.fontFamily),this.fontSize=Sr(A,Qr,e.fontSize),this.fontStyle=Sr(A,pr,e.fontStyle),this.fontVariant=Sr(A,Ur,e.fontVariant),this.fontWeight=Sr(A,Cr,e.fontWeight),this.letterSpacing=Sr(A,Dt,e.letterSpacing),this.lineBreak=Sr(A,Ot,e.lineBreak),this.lineHeight=Sr(A,Tt,e.lineHeight),this.listStyleImage=Sr(A,Rt,e.listStyleImage),this.listStylePosition=Sr(A,Gt,e.listStylePosition),this.listStyleType=Sr(A,Vt,e.listStyleType),this.marginTop=Sr(A,Pt,e.marginTop),this.marginRight=Sr(A,Xt,e.marginRight),this.marginBottom=Sr(A,Jt,e.marginBottom),this.marginLeft=Sr(A,Wt,e.marginLeft),this.opacity=Sr(A,gr,e.opacity);var n=Sr(A,Yt,e.overflow);this.overflowX=n[0],this.overflowY=n[n.length>1?1:0],this.overflowWrap=Sr(A,zt,e.overflowWrap),this.paddingTop=Sr(A,jt,e.paddingTop),this.paddingRight=Sr(A,qt,e.paddingRight),this.paddingBottom=Sr(A,$t,e.paddingBottom),this.paddingLeft=Sr(A,Ar,e.paddingLeft),this.paintOrder=Sr(A,Kr,e.paintOrder),this.position=Sr(A,tr,e.position),this.textAlign=Sr(A,er,e.textAlign),this.textDecorationColor=Sr(A,wr,null!==(t=e.textDecorationColor)&&void 0!==t?t:e.color),this.textDecorationLine=Sr(A,dr,null!==(r=e.textDecorationLine)&&void 0!==r?r:e.textDecoration),this.textShadow=Sr(A,rr,e.textShadow),this.textTransform=Sr(A,nr,e.textTransform),this.transform=Sr(A,sr,e.transform),this.transformOrigin=Sr(A,Br,e.transformOrigin),this.visibility=Sr(A,cr,e.visibility),this.webkitTextStrokeColor=Sr(A,Lr,e.webkitTextStrokeColor),this.webkitTextStrokeWidth=Sr(A,_r,e.webkitTextStrokeWidth),this.wordBreak=Sr(A,ur,e.wordBreak),this.zIndex=Sr(A,lr,e.zIndex)}return A.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&0===this.visibility},A.prototype.isTransparent=function(){return me(this.backgroundColor)},A.prototype.isTransformed=function(){return null!==this.transform},A.prototype.isPositioned=function(){return 0!==this.position},A.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},A.prototype.isFloating=function(){return 0!==this.float},A.prototype.isInlineLevel=function(){return Fr(this.display,4)||Fr(this.display,33554432)||Fr(this.display,268435456)||Fr(this.display,536870912)||Fr(this.display,67108864)||Fr(this.display,134217728)},A}(),Dr=function(){return function(A,e){this.content=Sr(A,mr,e.content),this.quotes=Sr(A,Ir,e.quotes)}}(),kr=function(){return function(A,e){this.counterIncrement=Sr(A,yr,e.counterIncrement),this.counterReset=Sr(A,Hr,e.counterReset)}}(),Sr=function(A,e,t){var r=new jA,n=null!=t?t.toString():e.initialValue;r.write(n);var s=new qA(r.read());switch(e.type){case 2:var i=s.parseComponentValue();return e.parse(A,ee(i)?i.value:e.initialValue);case 0:return e.parse(A,s.parseComponentValue());case 1:return e.parse(A,s.parseComponentValues());case 4:return s.parseComponentValue();case 3:switch(e.format){case"angle":return Qe(A,s.parseComponentValue());case"color":return Fe(A,s.parseComponentValue());case"image":return Ze(A,s.parseComponentValue());case"length":var o=s.parseComponentValue();return ae(o)?o:ue;case"length-percentage":var a=s.parseComponentValue();return Be(a)?a:ue;case"time":return hr(A,s.parseComponentValue())}}},Or=function(A,e){var t=function(A){switch(A.getAttribute("data-html2canvas-debug")){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}}(A);return 1===t||e===t},Tr=function(){return function(A,e){this.context=A,this.textNodes=[],this.elements=[],this.flags=0,Or(e,3),this.styles=new xr(A,window.getComputedStyle(e,null)),Pn(e)&&(this.styles.animationDuration.some((function(A){return A>0}))&&(e.style.animationDuration="0s"),null!==this.styles.transform&&(e.style.transform="none")),this.bounds=B(this.context,e),Or(e,4)&&(this.flags|=16)}}(),Mr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Rr="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Gr=0;Gr<64;Gr++)Rr[Mr.charCodeAt(Gr)]=Gr;for(var Vr=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},Nr=function(){function A(A,e,t,r,n,s){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=s}return A.prototype.get=function(A){var e;if(A>=0){if(A<55296||A>56319&&A<=65535)return e=((e=this.index[A>>5])<<2)+(31&A),this.data[e];if(A<=65535)return e=((e=this.index[2048+(A-55296>>5)])<<2)+(31&A),this.data[e];if(A<this.highStart)return e=2080+(A>>11),e=this.index[e],e+=A>>5&63,e=((e=this.index[e])<<2)+(31&A),this.data[e];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),Pr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Xr="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Jr=0;Jr<64;Jr++)Xr[Pr.charCodeAt(Jr)]=Jr;var Wr,Yr,zr=8,Zr=9,jr=11,qr=12,$r=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var i=A[n];i<=65535?r.push(i):(i-=65536,r.push(55296+(i>>10),i%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},An=function(A,e){var t=function(A){var e,t,r,n,s,i=.75*A.length,o=A.length,a=0;"="===A[A.length-1]&&(i--,"="===A[A.length-2]&&i--);var B="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?new ArrayBuffer(i):new Array(i),c=Array.isArray(B)?B:new Uint8Array(B);for(e=0;e<o;e+=4)t=Rr[A.charCodeAt(e)],r=Rr[A.charCodeAt(e+1)],n=Rr[A.charCodeAt(e+2)],s=Rr[A.charCodeAt(e+3)],c[a++]=t<<2|r>>4,c[a++]=(15&r)<<4|n>>2,c[a++]=(3&n)<<6|63&s;return B}(A),r=Array.isArray(t)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t}(t):new Uint32Array(t),n=Array.isArray(t)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t}(t):new Uint16Array(t),s=Vr(n,12,r[4]/2),i=2===r[5]?Vr(n,(24+r[4])/2):function(A,e,t){return A.slice?A.slice(e,t):new Uint32Array(Array.prototype.slice.call(A,e,t))}(r,Math.ceil((24+r[4])/4));return new Nr(r[0],r[1],r[2],r[3],s,i)}("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"),en="×",tn=function(A){return An.get(A)},rn=function(A,e,t){var r=t-2,n=e[r],s=e[t-1],i=e[t];if(2===s&&3===i)return en;if(2===s||3===s||4===s)return"÷";if(2===i||3===i||4===i)return"÷";if(s===zr&&-1!==[zr,Zr,jr,qr].indexOf(i))return en;if(!(s!==jr&&s!==Zr||i!==Zr&&10!==i))return en;if((s===qr||10===s)&&10===i)return en;if(13===i||5===i)return en;if(7===i)return en;if(1===s)return en;if(13===s&&14===i){for(;5===n;)n=e[--r];if(14===n)return en}if(15===s&&15===i){for(var o=0;15===n;)o++,n=e[--r];if(o%2==0)return en}return"÷"},nn=function(A){var e=function(A){for(var e=[],t=0,r=A.length;t<r;){var n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=A.charCodeAt(t++);56320==(64512&s)?e.push(((1023&n)<<10)+(1023&s)+65536):(e.push(n),t--)}else e.push(n)}return e}(A),t=e.length,r=0,n=0,s=e.map(tn);return{next:function(){if(r>=t)return{done:!0,value:null};for(var A=en;r<t&&(A=rn(0,s,++r))===en;);if(A!==en||r===t){var i=$r.apply(null,e.slice(n,r));return n=r,{value:i,done:!1}}return{done:!0,value:null}}}},sn=function(A){return 0===A[0]&&255===A[1]&&0===A[2]&&255===A[3]},on=function(A,e,t,r,n){var s="http://www.w3.org/2000/svg",i=document.createElementNS(s,"svg"),o=document.createElementNS(s,"foreignObject");return i.setAttributeNS(null,"width",A.toString()),i.setAttributeNS(null,"height",e.toString()),o.setAttributeNS(null,"width","100%"),o.setAttributeNS(null,"height","100%"),o.setAttributeNS(null,"x",t.toString()),o.setAttributeNS(null,"y",r.toString()),o.setAttributeNS(null,"externalResourcesRequired","true"),i.appendChild(o),o.appendChild(n),i},an=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){return e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},Bn={get SUPPORT_RANGE_BOUNDS(){var A=function(A){if(A.createRange){var e=A.createRange();if(e.getBoundingClientRect){var t=A.createElement("boundtest");t.style.height="123px",t.style.display="block",A.body.appendChild(t),e.selectNode(t);var r=e.getBoundingClientRect(),n=Math.round(r.height);if(A.body.removeChild(t),123===n)return!0}}return!1}(document);return Object.defineProperty(Bn,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_WORD_BREAKING(){var A=Bn.SUPPORT_RANGE_BOUNDS&&function(A){var e=A.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",A.body.appendChild(e);var t=A.createRange();e.innerHTML="function"==typeof"".repeat?"&#128104;".repeat(10):"";var r=e.firstChild,n=c(r.data).map((function(A){return u(A)})),s=0,i={},o=n.every((function(A,e){t.setStart(r,s),t.setEnd(r,s+A.length);var n=t.getBoundingClientRect();s+=A.length;var o=n.x>i.x||n.y>i.y;return i=n,0===e||o}));return A.body.removeChild(e),o}(document);return Object.defineProperty(Bn,"SUPPORT_WORD_BREAKING",{value:A}),A},get SUPPORT_SVG_DRAWING(){var A=function(A){var e=new Image,t=A.createElement("canvas"),r=t.getContext("2d");if(!r)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(e,0,0),t.toDataURL()}catch(n){return!1}return!0}(document);return Object.defineProperty(Bn,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){var A="function"==typeof Array.from&&"function"==typeof window.fetch?function(A){var e=A.createElement("canvas"),t=100;e.width=t,e.height=t;var r=e.getContext("2d");if(!r)return Promise.reject(!1);r.fillStyle="rgb(0, 255, 0)",r.fillRect(0,0,t,t);var n=new Image,s=e.toDataURL();n.src=s;var i=on(t,t,0,0,n);return r.fillStyle="red",r.fillRect(0,0,t,t),an(i).then((function(e){r.drawImage(e,0,0);var n=r.getImageData(0,0,t,t).data;r.fillStyle="red",r.fillRect(0,0,t,t);var i=A.createElement("div");return i.style.backgroundImage="url("+s+")",i.style.height=t+"px",sn(n)?an(on(t,t,0,0,i)):Promise.reject(!1)})).then((function(A){return r.drawImage(A,0,0),sn(r.getImageData(0,0,t,t).data)})).catch((function(){return!1}))}(document):Promise.resolve(!1);return Object.defineProperty(Bn,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){var A=void 0!==(new Image).crossOrigin;return Object.defineProperty(Bn,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){var A="string"==typeof(new XMLHttpRequest).responseType;return Object.defineProperty(Bn,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){var A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(Bn,"SUPPORT_CORS_XHR",{value:A}),A},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var A=!("undefined"==typeof Intl||!Intl.Segmenter);return Object.defineProperty(Bn,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:A}),A}},cn=function(){return function(A,e){this.text=A,this.bounds=e}}(),un=function(A,e){var t=e.ownerDocument;if(t){var r=t.createElement("html2canvaswrapper");r.appendChild(e.cloneNode(!0));var n=e.parentNode;if(n){n.replaceChild(r,e);var s=B(A,r);return r.firstChild&&n.replaceChild(r.firstChild,r),s}}return a.EMPTY},ln=function(A,e,t){var r=A.ownerDocument;if(!r)throw new Error("Node has no owner document");var n=r.createRange();return n.setStart(A,e),n.setEnd(A,e+t),n},hn=function(A){if(Bn.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(e.segment(A)).map((function(A){return A.segment}))}return function(A){for(var e,t=nn(A),r=[];!(e=t.next()).done;)e.value&&r.push(e.value.slice());return r}(A)},gn=function(A,e){return 0!==e.letterSpacing?hn(A):function(A,e){if(Bn.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(t.segment(A)).map((function(A){return A.segment}))}return dn(A,e)}(A,e)},wn=[32,160,4961,65792,65793,4153,4241],dn=function(A,e){for(var t,r=function(A,e){var t=c(A),r=wA(t,e),n=r[0],s=r[1],i=r[2],o=t.length,a=0,B=0;return{next:function(){if(B>=o)return{done:!0,value:null};for(var A=tA;B<o&&(A=gA(t,s,n,++B,i))===tA;);if(A!==tA||B===o){var e=new dA(t,A,a,B);return a=B,{value:e,done:!1}}return{done:!0,value:null}}}}(A,{lineBreak:e.lineBreak,wordBreak:"break-word"===e.overflowWrap?"break-word":e.wordBreak}),n=[],s=function(){if(t.value){var A=t.value.slice(),e=c(A),r="";e.forEach((function(A){-1===wn.indexOf(A)?r+=u(A):(r.length&&n.push(r),n.push(u(A)),r="")})),r.length&&n.push(r)}};!(t=r.next()).done;)s();return n},fn=function(){return function(A,e,t){this.text=Qn(e.data,t.textTransform),this.textBounds=function(A,e,t,r){var n=gn(e,t),s=[],i=0;return n.forEach((function(e){if(t.textDecorationLine.length||e.trim().length>0)if(Bn.SUPPORT_RANGE_BOUNDS){var n=ln(r,i,e.length).getClientRects();if(n.length>1){var o=hn(e),B=0;o.forEach((function(e){s.push(new cn(e,a.fromDOMRectList(A,ln(r,B+i,e.length).getClientRects()))),B+=e.length}))}else s.push(new cn(e,a.fromDOMRectList(A,n)))}else{var c=r.splitText(e.length);s.push(new cn(e,un(A,r))),r=c}else Bn.SUPPORT_RANGE_BOUNDS||(r=r.splitText(e.length));i+=e.length})),s}(A,this.text,t,e)}}(),Qn=function(A,e){switch(e){case 1:return A.toLowerCase();case 3:return A.replace(Cn,Un);case 2:return A.toUpperCase();default:return A}},Cn=/(^|\s|:|-|\(|\))([a-z])/g,Un=function(A,e,t){return A.length>0?e+t.toUpperCase():A},pn=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.src=t.currentSrc||t.src,r.intrinsicWidth=t.naturalWidth,r.intrinsicHeight=t.naturalHeight,r.context.cache.addImage(r.src),r}return n(e,A),e}(Tr),Fn=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.canvas=t,r.intrinsicWidth=t.width,r.intrinsicHeight=t.height,r}return n(e,A),e}(Tr),mn=function(A){function e(e,t){var r=A.call(this,e,t)||this,n=new XMLSerializer,s=B(e,t);return t.setAttribute("width",s.width+"px"),t.setAttribute("height",s.height+"px"),r.svg="data:image/svg+xml,"+encodeURIComponent(n.serializeToString(t)),r.intrinsicWidth=t.width.baseVal.value,r.intrinsicHeight=t.height.baseVal.value,r.context.cache.addImage(r.svg),r}return n(e,A),e}(Tr),yn=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.value=t.value,r}return n(e,A),e}(Tr),Hn=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.start=t.start,r.reversed="boolean"==typeof t.reversed&&!0===t.reversed,r}return n(e,A),e}(Tr),En=[{type:15,flags:0,unit:"px",number:3}],In=[{type:16,flags:0,number:50}],bn="checkbox",vn="radio",Kn="password",Ln=707406591,_n=function(A){function e(e,t){var r,n,s,i=A.call(this,e,t)||this;switch(i.type=t.type.toLowerCase(),i.checked=t.checked,i.value=0===(n=(r=t).type===Kn?new Array(r.value.length+1).join("•"):r.value).length?r.placeholder||"":n,i.type!==bn&&i.type!==vn||(i.styles.backgroundColor=3739148031,i.styles.borderTopColor=i.styles.borderRightColor=i.styles.borderBottomColor=i.styles.borderLeftColor=2779096575,i.styles.borderTopWidth=i.styles.borderRightWidth=i.styles.borderBottomWidth=i.styles.borderLeftWidth=1,i.styles.borderTopStyle=i.styles.borderRightStyle=i.styles.borderBottomStyle=i.styles.borderLeftStyle=1,i.styles.backgroundClip=[0],i.styles.backgroundOrigin=[0],i.bounds=(s=i.bounds).width>s.height?new a(s.left+(s.width-s.height)/2,s.top,s.height,s.height):s.width<s.height?new a(s.left,s.top+(s.height-s.width)/2,s.width,s.width):s),i.type){case bn:i.styles.borderTopRightRadius=i.styles.borderTopLeftRadius=i.styles.borderBottomRightRadius=i.styles.borderBottomLeftRadius=En;break;case vn:i.styles.borderTopRightRadius=i.styles.borderTopLeftRadius=i.styles.borderBottomRightRadius=i.styles.borderBottomLeftRadius=In}return i}return n(e,A),e}(Tr),xn=function(A){function e(e,t){var r=A.call(this,e,t)||this,n=t.options[t.selectedIndex||0];return r.value=n&&n.text||"",r}return n(e,A),e}(Tr),Dn=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.value=t.value,r}return n(e,A),e}(Tr),kn=function(A){function e(e,t){var r=A.call(this,e,t)||this;r.src=t.src,r.width=parseInt(t.width,10)||0,r.height=parseInt(t.height,10)||0,r.backgroundColor=r.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){r.tree=Mn(e,t.contentWindow.document.documentElement);var n=t.contentWindow.document.documentElement?Le(e,getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):_e.TRANSPARENT,s=t.contentWindow.document.body?Le(e,getComputedStyle(t.contentWindow.document.body).backgroundColor):_e.TRANSPARENT;r.backgroundColor=me(n)?me(s)?r.styles.backgroundColor:s:n}}catch(i){}return r}return n(e,A),e}(Tr),Sn=["OL","UL","MENU"],On=function(A,e,t,r){for(var n=e.firstChild,s=void 0;n;n=s)if(s=n.nextSibling,Vn(n)&&n.data.trim().length>0)t.textNodes.push(new fn(A,n,t.styles));else if(Nn(n))if(ns(n)&&n.assignedNodes)n.assignedNodes().forEach((function(e){return On(A,e,t,r)}));else{var i=Tn(A,n);i.styles.isVisible()&&(Rn(n,i,r)?i.flags|=4:Gn(i.styles)&&(i.flags|=2),-1!==Sn.indexOf(n.tagName)&&(i.flags|=8),t.elements.push(i),n.slot,n.shadowRoot?On(A,n.shadowRoot,i,r):ts(n)||zn(n)||rs(n)||On(A,n,i,r))}},Tn=function(A,e){return $n(e)?new pn(A,e):jn(e)?new Fn(A,e):zn(e)?new mn(A,e):Jn(e)?new yn(A,e):Wn(e)?new Hn(A,e):Yn(e)?new _n(A,e):rs(e)?new xn(A,e):ts(e)?new Dn(A,e):As(e)?new kn(A,e):new Tr(A,e)},Mn=function(A,e){var t=Tn(A,e);return t.flags|=4,On(A,e,t,t),t},Rn=function(A,e,t){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||Zn(A)&&t.styles.isTransparent()},Gn=function(A){return A.isPositioned()||A.isFloating()},Vn=function(A){return A.nodeType===Node.TEXT_NODE},Nn=function(A){return A.nodeType===Node.ELEMENT_NODE},Pn=function(A){return Nn(A)&&void 0!==A.style&&!Xn(A)},Xn=function(A){return"object"==typeof A.className},Jn=function(A){return"LI"===A.tagName},Wn=function(A){return"OL"===A.tagName},Yn=function(A){return"INPUT"===A.tagName},zn=function(A){return"svg"===A.tagName},Zn=function(A){return"BODY"===A.tagName},jn=function(A){return"CANVAS"===A.tagName},qn=function(A){return"VIDEO"===A.tagName},$n=function(A){return"IMG"===A.tagName},As=function(A){return"IFRAME"===A.tagName},es=function(A){return"STYLE"===A.tagName},ts=function(A){return"TEXTAREA"===A.tagName},rs=function(A){return"SELECT"===A.tagName},ns=function(A){return"SLOT"===A.tagName},ss=function(A){return A.tagName.indexOf("-")>0},is=function(){function A(){this.counters={}}return A.prototype.getCounterValue=function(A){var e=this.counters[A];return e&&e.length?e[e.length-1]:1},A.prototype.getCounterValues=function(A){var e=this.counters[A];return e||[]},A.prototype.pop=function(A){var e=this;A.forEach((function(A){return e.counters[A].pop()}))},A.prototype.parse=function(A){var e=this,t=A.counterIncrement,r=A.counterReset,n=!0;null!==t&&t.forEach((function(A){var t=e.counters[A.counter];t&&0!==A.increment&&(n=!1,t.length||t.push(1),t[Math.max(0,t.length-1)]+=A.increment)}));var s=[];return n&&r.forEach((function(A){var t=e.counters[A.counter];s.push(A.counter),t||(t=e.counters[A.counter]=[]),t.push(A.reset)})),s},A}(),os={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},as={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},Bs={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},cs={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},us=function(A,e,t,r,n,s){return A<e||A>t?Us(A,n,s.length>0):r.integers.reduce((function(e,t,n){for(;A>=t;)A-=t,e+=r.values[n];return e}),"")+s},ls=function(A,e,t,r){var n="";do{t||A--,n=r(A)+n,A/=e}while(A*e>=e);return n},hs=function(A,e,t,r,n){var s=t-e+1;return(A<0?"-":"")+(ls(Math.abs(A),s,r,(function(A){return u(Math.floor(A%s)+e)}))+n)},gs=function(A,e,t){void 0===t&&(t=". ");var r=e.length;return ls(Math.abs(A),r,!1,(function(A){return e[Math.floor(A%r)]}))+t},ws=function(A,e,t,r,n,s){if(A<-9999||A>9999)return Us(A,4,n.length>0);var i=Math.abs(A),o=n;if(0===i)return e[0]+o;for(var a=0;i>0&&a<=4;a++){var B=i%10;0===B&&Fr(s,1)&&""!==o?o=e[B]+o:B>1||1===B&&0===a||1===B&&1===a&&Fr(s,2)||1===B&&1===a&&Fr(s,4)&&A>100||1===B&&a>1&&Fr(s,8)?o=e[B]+(a>0?t[a-1]:"")+o:1===B&&a>0&&(o=t[a-1]+o),i=Math.floor(i/10)}return(A<0?r:"")+o},ds="十百千萬",fs="拾佰仟萬",Qs="マイナス",Cs="마이너스",Us=function(A,e,t){var r=t?". ":"",n=t?"、":"",s=t?", ":"",i=t?" ":"";switch(e){case 0:return"•"+i;case 1:return"◦"+i;case 2:return"◾"+i;case 5:var o=hs(A,48,57,!0,r);return o.length<4?"0"+o:o;case 4:return gs(A,"〇一二三四五六七八九",n);case 6:return us(A,1,3999,os,3,r).toLowerCase();case 7:return us(A,1,3999,os,3,r);case 8:return hs(A,945,969,!1,r);case 9:return hs(A,97,122,!1,r);case 10:return hs(A,65,90,!1,r);case 11:return hs(A,1632,1641,!0,r);case 12:case 49:return us(A,1,9999,as,3,r);case 35:return us(A,1,9999,as,3,r).toLowerCase();case 13:return hs(A,2534,2543,!0,r);case 14:case 30:return hs(A,6112,6121,!0,r);case 15:return gs(A,"子丑寅卯辰巳午未申酉戌亥",n);case 16:return gs(A,"甲乙丙丁戊己庚辛壬癸",n);case 17:case 48:return ws(A,"零一二三四五六七八九",ds,"負",n,14);case 47:return ws(A,"零壹貳參肆伍陸柒捌玖",fs,"負",n,15);case 42:return ws(A,"零一二三四五六七八九",ds,"负",n,14);case 41:return ws(A,"零壹贰叁肆伍陆柒捌玖",fs,"负",n,15);case 26:return ws(A,"〇一二三四五六七八九","十百千万",Qs,n,0);case 25:return ws(A,"零壱弐参四伍六七八九","拾百千万",Qs,n,7);case 31:return ws(A,"영일이삼사오육칠팔구","십백천만",Cs,s,7);case 33:return ws(A,"零一二三四五六七八九","十百千萬",Cs,s,0);case 32:return ws(A,"零壹貳參四五六七八九","拾百千",Cs,s,7);case 18:return hs(A,2406,2415,!0,r);case 20:return us(A,1,19999,cs,3,r);case 21:return hs(A,2790,2799,!0,r);case 22:return hs(A,2662,2671,!0,r);case 22:return us(A,1,10999,Bs,3,r);case 23:return gs(A,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return gs(A,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return hs(A,3302,3311,!0,r);case 28:return gs(A,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case 29:return gs(A,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case 34:return hs(A,3792,3801,!0,r);case 37:return hs(A,6160,6169,!0,r);case 38:return hs(A,4160,4169,!0,r);case 39:return hs(A,2918,2927,!0,r);case 40:return hs(A,1776,1785,!0,r);case 43:return hs(A,3046,3055,!0,r);case 44:return hs(A,3174,3183,!0,r);case 45:return hs(A,3664,3673,!0,r);case 46:return hs(A,3872,3881,!0,r);default:return hs(A,48,57,!0,r)}},ps="data-html2canvas-ignore",Fs=function(){function A(A,e,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=e,this.counters=new is,this.quoteDepth=0,!e.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(e.ownerDocument.documentElement,!1)}return A.prototype.toIFrame=function(A,e){var t=this,r=Hs(A,e);if(!r.contentWindow)return Promise.reject("Unable to find iframe window");var n=A.defaultView.pageXOffset,s=A.defaultView.pageYOffset,a=r.contentWindow,B=a.document,c=bs(r).then((function(){return i(t,0,void 0,(function(){var A,t;return o(this,(function(n){switch(n.label){case 0:return this.scrolledElements.forEach(xs),a&&(a.scrollTo(e.left,e.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||a.scrollY===e.top&&a.scrollX===e.left||(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(a.scrollX-e.left,a.scrollY-e.top,0,0))),A=this.options.onclone,void 0===(t=this.clonedReferenceElement)?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:B.fonts&&B.fonts.ready?[4,B.fonts.ready]:[3,2];case 1:n.sent(),n.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,Is(B)]:[3,4];case 3:n.sent(),n.label=4;case 4:return"function"==typeof A?[2,Promise.resolve().then((function(){return A(B,t)})).then((function(){return r}))]:[2,r]}}))}))}));return B.open(),B.write(Ls(document.doctype)+"<html></html>"),_s(this.referenceElement.ownerDocument,n,s),B.replaceChild(B.adoptNode(this.documentElement),B.documentElement),B.close(),c},A.prototype.createElementClone=function(A){if(Or(A,2),jn(A))return this.createCanvasClone(A);if(qn(A))return this.createVideoClone(A);if(es(A))return this.createStyleClone(A);var e=A.cloneNode(!1);return $n(e)&&($n(A)&&A.currentSrc&&A.currentSrc!==A.src&&(e.src=A.currentSrc,e.srcset=""),"lazy"===e.loading&&(e.loading="eager")),ss(e)?this.createCustomElementClone(e):e},A.prototype.createCustomElementClone=function(A){var e=document.createElement("html2canvascustomelement");return Ks(A.style,e),e},A.prototype.createStyleClone=function(A){try{var e=A.sheet;if(e&&e.cssRules){var t=[].slice.call(e.cssRules,0).reduce((function(A,e){return e&&"string"==typeof e.cssText?A+e.cssText:A}),""),r=A.cloneNode(!1);return r.textContent=t,r}}catch(n){if(this.context.logger.error("Unable to access cssRules property",n),"SecurityError"!==n.name)throw n}return A.cloneNode(!1)},A.prototype.createCanvasClone=function(A){var e;if(this.options.inlineImages&&A.ownerDocument){var t=A.ownerDocument.createElement("img");try{return t.src=A.toDataURL(),t}catch(a){this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var r=A.cloneNode(!1);try{r.width=A.width,r.height=A.height;var n=A.getContext("2d"),s=r.getContext("2d");if(s)if(!this.options.allowTaint&&n)s.putImageData(n.getImageData(0,0,A.width,A.height),0,0);else{var i=null!==(e=A.getContext("webgl2"))&&void 0!==e?e:A.getContext("webgl");if(i){var o=i.getContextAttributes();!1===(null==o?void 0:o.preserveDrawingBuffer)&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}s.drawImage(A,0,0)}return r}catch(a){this.context.logger.info("Unable to clone canvas as it is tainted",A)}return r},A.prototype.createVideoClone=function(A){var e=A.ownerDocument.createElement("canvas");e.width=A.offsetWidth,e.height=A.offsetHeight;var t=e.getContext("2d");try{return t&&(t.drawImage(A,0,0,e.width,e.height),this.options.allowTaint||t.getImageData(0,0,e.width,e.height)),e}catch(n){this.context.logger.info("Unable to clone video as it is tainted",A)}var r=A.ownerDocument.createElement("canvas");return r.width=A.offsetWidth,r.height=A.offsetHeight,r},A.prototype.appendChildNode=function(A,e,t){Nn(e)&&("SCRIPT"===e.tagName||e.hasAttribute(ps)||"function"==typeof this.options.ignoreElements&&this.options.ignoreElements(e))||this.options.copyStyles&&Nn(e)&&es(e)||A.appendChild(this.cloneNode(e,t))},A.prototype.cloneChildNodes=function(A,e,t){for(var r=this,n=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;n;n=n.nextSibling)if(Nn(n)&&ns(n)&&"function"==typeof n.assignedNodes){var s=n.assignedNodes();s.length&&s.forEach((function(A){return r.appendChildNode(e,A,t)}))}else this.appendChildNode(e,n,t)},A.prototype.cloneNode=function(A,e){if(Vn(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var t=A.ownerDocument.defaultView;if(t&&Nn(A)&&(Pn(A)||Xn(A))){var r=this.createElementClone(A);r.style.transitionProperty="none";var n=t.getComputedStyle(A),s=t.getComputedStyle(A,":before"),i=t.getComputedStyle(A,":after");this.referenceElement===A&&Pn(r)&&(this.clonedReferenceElement=r),Zn(r)&&Os(r);var o=this.counters.parse(new kr(this.context,n)),a=this.resolvePseudoContent(A,r,s,Wr.BEFORE);ss(A)&&(e=!0),qn(A)||this.cloneChildNodes(A,r,e),a&&r.insertBefore(a,r.firstChild);var B=this.resolvePseudoContent(A,r,i,Wr.AFTER);return B&&r.appendChild(B),this.counters.pop(o),(n&&(this.options.copyStyles||Xn(A))&&!As(A)||e)&&Ks(n,r),0===A.scrollTop&&0===A.scrollLeft||this.scrolledElements.push([r,A.scrollLeft,A.scrollTop]),(ts(A)||rs(A))&&(ts(r)||rs(r))&&(r.value=A.value),r}return A.cloneNode(!1)},A.prototype.resolvePseudoContent=function(A,e,t,r){var n=this;if(t){var s=t.content,i=e.ownerDocument;if(i&&s&&"none"!==s&&"-moz-alt-content"!==s&&"none"!==t.display){this.counters.parse(new kr(this.context,t));var o=new Dr(this.context,t),a=i.createElement("html2canvaspseudoelement");Ks(t,a),o.content.forEach((function(e){if(0===e.type)a.appendChild(i.createTextNode(e.value));else if(22===e.type){var t=i.createElement("img");t.src=e.value,t.style.opacity="1",a.appendChild(t)}else if(18===e.type){if("attr"===e.name){var r=e.values.filter(ee);r.length&&a.appendChild(i.createTextNode(A.getAttribute(r[0].value)||""))}else if("counter"===e.name){var s=e.values.filter(se),B=s[0],c=s[1];if(B&&ee(B)){var u=n.counters.getCounterValue(B.value),l=c&&ee(c)?Vt.parse(n.context,c.value):3;a.appendChild(i.createTextNode(Us(u,l,!1)))}}else if("counters"===e.name){var h=e.values.filter(se),g=(B=h[0],h[1]);c=h[2];if(B&&ee(B)){var w=n.counters.getCounterValues(B.value),d=c&&ee(c)?Vt.parse(n.context,c.value):3,f=g&&0===g.type?g.value:"",Q=w.map((function(A){return Us(A,d,!1)})).join(f);a.appendChild(i.createTextNode(Q))}}}else if(20===e.type)switch(e.value){case"open-quote":a.appendChild(i.createTextNode(br(o.quotes,n.quoteDepth++,!0)));break;case"close-quote":a.appendChild(i.createTextNode(br(o.quotes,--n.quoteDepth,!1)));break;default:a.appendChild(i.createTextNode(e.value))}})),a.className=Ds+" "+ks;var B=r===Wr.BEFORE?" "+Ds:" "+ks;return Xn(e)?e.className.baseValue+=B:e.className+=B,a}}},A.destroy=function(A){return!!A.parentNode&&(A.parentNode.removeChild(A),!0)},A}();(Yr=Wr||(Wr={}))[Yr.BEFORE=0]="BEFORE",Yr[Yr.AFTER=1]="AFTER";var ms,ys,Hs=function(A,e){var t=A.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=e.width.toString(),t.height=e.height.toString(),t.scrolling="no",t.setAttribute(ps,"true"),A.body.appendChild(t),t},Es=function(A){return new Promise((function(e){A.complete?e():A.src?(A.onload=e,A.onerror=e):e()}))},Is=function(A){return Promise.all([].slice.call(A.images,0).map(Es))},bs=function(A){return new Promise((function(e,t){var r=A.contentWindow;if(!r)return t("No window assigned for iframe");var n=r.document;r.onload=A.onload=function(){r.onload=A.onload=null;var t=setInterval((function(){n.body.childNodes.length>0&&"complete"===n.readyState&&(clearInterval(t),e(A))}),50)}}))},vs=["all","d","content"],Ks=function(A,e){for(var t=A.length-1;t>=0;t--){var r=A.item(t);-1===vs.indexOf(r)&&e.style.setProperty(r,A.getPropertyValue(r))}return e},Ls=function(A){var e="";return A&&(e+="<!DOCTYPE ",A.name&&(e+=A.name),A.internalSubset&&(e+=A.internalSubset),A.publicId&&(e+='"'+A.publicId+'"'),A.systemId&&(e+='"'+A.systemId+'"'),e+=">"),e},_s=function(A,e,t){A&&A.defaultView&&(e!==A.defaultView.pageXOffset||t!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(e,t)},xs=function(A){var e=A[0],t=A[1],r=A[2];e.scrollLeft=t,e.scrollTop=r},Ds="___html2canvas___pseudoelement_before",ks="___html2canvas___pseudoelement_after",Ss='{\n    content: "" !important;\n    display: none !important;\n}',Os=function(A){Ts(A,"."+Ds+":before"+Ss+"\n         ."+ks+":after"+Ss)},Ts=function(A,e){var t=A.ownerDocument;if(t){var r=t.createElement("style");r.textContent=e,A.appendChild(r)}},Ms=function(){function A(){}return A.getOrigin=function(e){var t=A._link;return t?(t.href=e,t.href=t.href,t.protocol+t.hostname+t.port):"about:blank"},A.isSameOrigin=function(e){return A.getOrigin(e)===A._origin},A.setContext=function(e){A._link=e.document.createElement("a"),A._origin=A.getOrigin(e.location.href)},A._origin="about:blank",A}(),Rs=function(){function A(A,e){this.context=A,this._options=e,this._cache={}}return A.prototype.addImage=function(A){var e=Promise.resolve();return this.has(A)?e:Ws(A)||Ps(A)?((this._cache[A]=this.loadImage(A)).catch((function(){})),e):e},A.prototype.match=function(A){return this._cache[A]},A.prototype.loadImage=function(A){return i(this,0,void 0,(function(){var e,t,r,n,s=this;return o(this,(function(i){switch(i.label){case 0:return e=Ms.isSameOrigin(A),t=!Xs(A)&&!0===this._options.useCORS&&Bn.SUPPORT_CORS_IMAGES&&!e,r=!Xs(A)&&!e&&!Ws(A)&&"string"==typeof this._options.proxy&&Bn.SUPPORT_CORS_XHR&&!t,e||!1!==this._options.allowTaint||Xs(A)||Ws(A)||r||t?(n=A,r?[4,this.proxy(n)]:[3,2]):[2];case 1:n=i.sent(),i.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise((function(A,e){var r=new Image;r.onload=function(){return A(r)},r.onerror=e,(Js(n)||t)&&(r.crossOrigin="anonymous"),r.src=n,!0===r.complete&&setTimeout((function(){return A(r)}),500),s._options.imageTimeout>0&&setTimeout((function(){return e("Timed out ("+s._options.imageTimeout+"ms) loading image")}),s._options.imageTimeout)}))];case 3:return[2,i.sent()]}}))}))},A.prototype.has=function(A){return void 0!==this._cache[A]},A.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},A.prototype.proxy=function(A){var e=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var r=A.substring(0,256);return new Promise((function(n,s){var i=Bn.SUPPORT_RESPONSE_TYPE?"blob":"text",o=new XMLHttpRequest;o.onload=function(){if(200===o.status)if("text"===i)n(o.response);else{var A=new FileReader;A.addEventListener("load",(function(){return n(A.result)}),!1),A.addEventListener("error",(function(A){return s(A)}),!1),A.readAsDataURL(o.response)}else s("Failed to proxy resource "+r+" with status code "+o.status)},o.onerror=s;var a=t.indexOf("?")>-1?"&":"?";if(o.open("GET",""+t+a+"url="+encodeURIComponent(A)+"&responseType="+i),"text"!==i&&o instanceof XMLHttpRequest&&(o.responseType=i),e._options.imageTimeout){var B=e._options.imageTimeout;o.timeout=B,o.ontimeout=function(){return s("Timed out ("+B+"ms) proxying "+r)}}o.send()}))},A}(),Gs=/^data:image\/svg\+xml/i,Vs=/^data:image\/.*;base64,/i,Ns=/^data:image\/.*/i,Ps=function(A){return Bn.SUPPORT_SVG_DRAWING||!Ys(A)},Xs=function(A){return Ns.test(A)},Js=function(A){return Vs.test(A)},Ws=function(A){return"blob"===A.substr(0,4)},Ys=function(A){return"svg"===A.substr(-3).toLowerCase()||Gs.test(A)},zs=function(){function A(A,e){this.type=0,this.x=A,this.y=e}return A.prototype.add=function(e,t){return new A(this.x+e,this.y+t)},A}(),Zs=function(A,e,t){return new zs(A.x+(e.x-A.x)*t,A.y+(e.y-A.y)*t)},js=function(){function A(A,e,t,r){this.type=1,this.start=A,this.startControl=e,this.endControl=t,this.end=r}return A.prototype.subdivide=function(e,t){var r=Zs(this.start,this.startControl,e),n=Zs(this.startControl,this.endControl,e),s=Zs(this.endControl,this.end,e),i=Zs(r,n,e),o=Zs(n,s,e),a=Zs(i,o,e);return t?new A(this.start,r,i,a):new A(a,o,s,this.end)},A.prototype.add=function(e,t){return new A(this.start.add(e,t),this.startControl.add(e,t),this.endControl.add(e,t),this.end.add(e,t))},A.prototype.reverse=function(){return new A(this.end,this.endControl,this.startControl,this.start)},A}(),qs=function(A){return 1===A.type},$s=function(){return function(A){var e=A.styles,t=A.bounds,r=ge(e.borderTopLeftRadius,t.width,t.height),n=r[0],s=r[1],i=ge(e.borderTopRightRadius,t.width,t.height),o=i[0],a=i[1],B=ge(e.borderBottomRightRadius,t.width,t.height),c=B[0],u=B[1],l=ge(e.borderBottomLeftRadius,t.width,t.height),h=l[0],g=l[1],w=[];w.push((n+o)/t.width),w.push((h+c)/t.width),w.push((s+g)/t.height),w.push((a+u)/t.height);var d=Math.max.apply(Math,w);d>1&&(n/=d,s/=d,o/=d,a/=d,c/=d,u/=d,h/=d,g/=d);var f=t.width-o,Q=t.height-u,C=t.width-c,U=t.height-g,p=e.borderTopWidth,F=e.borderRightWidth,m=e.borderBottomWidth,y=e.borderLeftWidth,H=we(e.paddingTop,A.bounds.width),E=we(e.paddingRight,A.bounds.width),I=we(e.paddingBottom,A.bounds.width),b=we(e.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=n>0||s>0?Ai(t.left+y/3,t.top+p/3,n-y/3,s-p/3,ms.TOP_LEFT):new zs(t.left+y/3,t.top+p/3),this.topRightBorderDoubleOuterBox=n>0||s>0?Ai(t.left+f,t.top+p/3,o-F/3,a-p/3,ms.TOP_RIGHT):new zs(t.left+t.width-F/3,t.top+p/3),this.bottomRightBorderDoubleOuterBox=c>0||u>0?Ai(t.left+C,t.top+Q,c-F/3,u-m/3,ms.BOTTOM_RIGHT):new zs(t.left+t.width-F/3,t.top+t.height-m/3),this.bottomLeftBorderDoubleOuterBox=h>0||g>0?Ai(t.left+y/3,t.top+U,h-y/3,g-m/3,ms.BOTTOM_LEFT):new zs(t.left+y/3,t.top+t.height-m/3),this.topLeftBorderDoubleInnerBox=n>0||s>0?Ai(t.left+2*y/3,t.top+2*p/3,n-2*y/3,s-2*p/3,ms.TOP_LEFT):new zs(t.left+2*y/3,t.top+2*p/3),this.topRightBorderDoubleInnerBox=n>0||s>0?Ai(t.left+f,t.top+2*p/3,o-2*F/3,a-2*p/3,ms.TOP_RIGHT):new zs(t.left+t.width-2*F/3,t.top+2*p/3),this.bottomRightBorderDoubleInnerBox=c>0||u>0?Ai(t.left+C,t.top+Q,c-2*F/3,u-2*m/3,ms.BOTTOM_RIGHT):new zs(t.left+t.width-2*F/3,t.top+t.height-2*m/3),this.bottomLeftBorderDoubleInnerBox=h>0||g>0?Ai(t.left+2*y/3,t.top+U,h-2*y/3,g-2*m/3,ms.BOTTOM_LEFT):new zs(t.left+2*y/3,t.top+t.height-2*m/3),this.topLeftBorderStroke=n>0||s>0?Ai(t.left+y/2,t.top+p/2,n-y/2,s-p/2,ms.TOP_LEFT):new zs(t.left+y/2,t.top+p/2),this.topRightBorderStroke=n>0||s>0?Ai(t.left+f,t.top+p/2,o-F/2,a-p/2,ms.TOP_RIGHT):new zs(t.left+t.width-F/2,t.top+p/2),this.bottomRightBorderStroke=c>0||u>0?Ai(t.left+C,t.top+Q,c-F/2,u-m/2,ms.BOTTOM_RIGHT):new zs(t.left+t.width-F/2,t.top+t.height-m/2),this.bottomLeftBorderStroke=h>0||g>0?Ai(t.left+y/2,t.top+U,h-y/2,g-m/2,ms.BOTTOM_LEFT):new zs(t.left+y/2,t.top+t.height-m/2),this.topLeftBorderBox=n>0||s>0?Ai(t.left,t.top,n,s,ms.TOP_LEFT):new zs(t.left,t.top),this.topRightBorderBox=o>0||a>0?Ai(t.left+f,t.top,o,a,ms.TOP_RIGHT):new zs(t.left+t.width,t.top),this.bottomRightBorderBox=c>0||u>0?Ai(t.left+C,t.top+Q,c,u,ms.BOTTOM_RIGHT):new zs(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=h>0||g>0?Ai(t.left,t.top+U,h,g,ms.BOTTOM_LEFT):new zs(t.left,t.top+t.height),this.topLeftPaddingBox=n>0||s>0?Ai(t.left+y,t.top+p,Math.max(0,n-y),Math.max(0,s-p),ms.TOP_LEFT):new zs(t.left+y,t.top+p),this.topRightPaddingBox=o>0||a>0?Ai(t.left+Math.min(f,t.width-F),t.top+p,f>t.width+F?0:Math.max(0,o-F),Math.max(0,a-p),ms.TOP_RIGHT):new zs(t.left+t.width-F,t.top+p),this.bottomRightPaddingBox=c>0||u>0?Ai(t.left+Math.min(C,t.width-y),t.top+Math.min(Q,t.height-m),Math.max(0,c-F),Math.max(0,u-m),ms.BOTTOM_RIGHT):new zs(t.left+t.width-F,t.top+t.height-m),this.bottomLeftPaddingBox=h>0||g>0?Ai(t.left+y,t.top+Math.min(U,t.height-m),Math.max(0,h-y),Math.max(0,g-m),ms.BOTTOM_LEFT):new zs(t.left+y,t.top+t.height-m),this.topLeftContentBox=n>0||s>0?Ai(t.left+y+b,t.top+p+H,Math.max(0,n-(y+b)),Math.max(0,s-(p+H)),ms.TOP_LEFT):new zs(t.left+y+b,t.top+p+H),this.topRightContentBox=o>0||a>0?Ai(t.left+Math.min(f,t.width+y+b),t.top+p+H,f>t.width+y+b?0:o-y+b,a-(p+H),ms.TOP_RIGHT):new zs(t.left+t.width-(F+E),t.top+p+H),this.bottomRightContentBox=c>0||u>0?Ai(t.left+Math.min(C,t.width-(y+b)),t.top+Math.min(Q,t.height+p+H),Math.max(0,c-(F+E)),u-(m+I),ms.BOTTOM_RIGHT):new zs(t.left+t.width-(F+E),t.top+t.height-(m+I)),this.bottomLeftContentBox=h>0||g>0?Ai(t.left+y+b,t.top+U,Math.max(0,h-(y+b)),g-(m+I),ms.BOTTOM_LEFT):new zs(t.left+y+b,t.top+t.height-(m+I))}}();(ys=ms||(ms={}))[ys.TOP_LEFT=0]="TOP_LEFT",ys[ys.TOP_RIGHT=1]="TOP_RIGHT",ys[ys.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",ys[ys.BOTTOM_LEFT=3]="BOTTOM_LEFT";var Ai=function(A,e,t,r,n){var s=(Math.sqrt(2)-1)/3*4,i=t*s,o=r*s,a=A+t,B=e+r;switch(n){case ms.TOP_LEFT:return new js(new zs(A,B),new zs(A,B-o),new zs(a-i,e),new zs(a,e));case ms.TOP_RIGHT:return new js(new zs(A,e),new zs(A+i,e),new zs(a,B-o),new zs(a,B));case ms.BOTTOM_RIGHT:return new js(new zs(a,e),new zs(a,e+o),new zs(A+i,B),new zs(A,B));case ms.BOTTOM_LEFT:default:return new js(new zs(a,B),new zs(a-i,B),new zs(A,e+o),new zs(A,e))}},ei=function(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]},ti=function(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]},ri=function(){return function(A,e,t){this.offsetX=A,this.offsetY=e,this.matrix=t,this.type=0,this.target=6}}(),ni=function(){return function(A,e){this.path=A,this.target=e,this.type=1}}(),si=function(){return function(A){this.opacity=A,this.type=2,this.target=6}}(),ii=function(A){return 1===A.type},oi=function(A,e){return A.length===e.length&&A.some((function(A,t){return A===e[t]}))},ai=function(){return function(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}}(),Bi=function(){function A(A,e){if(this.container=A,this.parent=e,this.effects=[],this.curves=new $s(this.container),this.container.styles.opacity<1&&this.effects.push(new si(this.container.styles.opacity)),null!==this.container.styles.transform){var t=this.container.bounds.left+this.container.styles.transformOrigin[0].number,r=this.container.bounds.top+this.container.styles.transformOrigin[1].number,n=this.container.styles.transform;this.effects.push(new ri(t,r,n))}if(0!==this.container.styles.overflowX){var s=ei(this.curves),i=ti(this.curves);oi(s,i)?this.effects.push(new ni(s,6)):(this.effects.push(new ni(s,2)),this.effects.push(new ni(i,4)))}}return A.prototype.getEffects=function(A){for(var e=-1===[2,3].indexOf(this.container.styles.position),t=this.parent,r=this.effects.slice(0);t;){var n=t.effects.filter((function(A){return!ii(A)}));if(e||0!==t.container.styles.position||!t.parent){if(r.unshift.apply(r,n),e=-1===[2,3].indexOf(t.container.styles.position),0!==t.container.styles.overflowX){var s=ei(t.curves),i=ti(t.curves);oi(s,i)||r.unshift(new ni(i,6))}}else r.unshift.apply(r,n);t=t.parent}return r.filter((function(e){return Fr(e.target,A)}))},A}(),ci=function(A,e,t,r){A.container.elements.forEach((function(n){var s=Fr(n.flags,4),i=Fr(n.flags,2),o=new Bi(n,A);Fr(n.styles.display,2048)&&r.push(o);var a=Fr(n.flags,8)?[]:r;if(s||i){var B=s||n.styles.isPositioned()?t:e,c=new ai(o);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var u=n.styles.zIndex.order;if(u<0){var l=0;B.negativeZIndex.some((function(A,e){return u>A.element.container.styles.zIndex.order?(l=e,!1):l>0})),B.negativeZIndex.splice(l,0,c)}else if(u>0){var h=0;B.positiveZIndex.some((function(A,e){return u>=A.element.container.styles.zIndex.order?(h=e+1,!1):h>0})),B.positiveZIndex.splice(h,0,c)}else B.zeroOrAutoZIndexOrTransformedOrOpacity.push(c)}else n.styles.isFloating()?B.nonPositionedFloats.push(c):B.nonPositionedInlineLevel.push(c);ci(o,c,s?c:t,a)}else n.styles.isInlineLevel()?e.inlineLevel.push(o):e.nonInlineLevel.push(o),ci(o,e,t,a);Fr(n.flags,8)&&ui(n,a)}))},ui=function(A,e){for(var t=A instanceof Hn?A.start:1,r=A instanceof Hn&&A.reversed,n=0;n<e.length;n++){var s=e[n];s.container instanceof yn&&"number"==typeof s.container.value&&0!==s.container.value&&(t=s.container.value),s.listValue=Us(t,s.container.styles.listStyleType,!0),t+=r?-1:1}},li=function(A,e){switch(e){case 0:return gi(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return gi(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return gi(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);default:return gi(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}},hi=function(A,e){var t=[];return qs(A)?t.push(A.subdivide(.5,!1)):t.push(A),qs(e)?t.push(e.subdivide(.5,!0)):t.push(e),t},gi=function(A,e,t,r){var n=[];return qs(A)?n.push(A.subdivide(.5,!1)):n.push(A),qs(t)?n.push(t.subdivide(.5,!0)):n.push(t),qs(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),qs(e)?n.push(e.subdivide(.5,!1).reverse()):n.push(e),n},wi=function(A){var e=A.bounds,t=A.styles;return e.add(t.borderLeftWidth,t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth),-(t.borderTopWidth+t.borderBottomWidth))},di=function(A){var e=A.styles,t=A.bounds,r=we(e.paddingLeft,t.width),n=we(e.paddingRight,t.width),s=we(e.paddingTop,t.width),i=we(e.paddingBottom,t.width);return t.add(r+e.borderLeftWidth,s+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+r+n),-(e.borderTopWidth+e.borderBottomWidth+s+i))},fi=function(A,e,t){var r,n,s=(r=pi(A.styles.backgroundOrigin,e),n=A,0===r?n.bounds:2===r?di(n):wi(n)),i=function(A,e){return 0===A?e.bounds:2===A?di(e):wi(e)}(pi(A.styles.backgroundClip,e),A),o=Ui(pi(A.styles.backgroundSize,e),t,s),a=o[0],B=o[1],c=ge(pi(A.styles.backgroundPosition,e),s.width-a,s.height-B);return[Fi(pi(A.styles.backgroundRepeat,e),c,o,s,i),Math.round(s.left+c[0]),Math.round(s.top+c[1]),a,B]},Qi=function(A){return ee(A)&&A.value===je.AUTO},Ci=function(A){return"number"==typeof A},Ui=function(A,e,t){var r=e[0],n=e[1],s=e[2],i=A[0],o=A[1];if(!i)return[0,0];if(Be(i)&&o&&Be(o))return[we(i,t.width),we(o,t.height)];var a=Ci(s);if(ee(i)&&(i.value===je.CONTAIN||i.value===je.COVER))return Ci(s)?t.width/t.height<s!=(i.value===je.COVER)?[t.width,t.width/s]:[t.height*s,t.height]:[t.width,t.height];var B=Ci(r),c=Ci(n),u=B||c;if(Qi(i)&&(!o||Qi(o)))return B&&c?[r,n]:a||u?u&&a?[B?r:n*s,c?n:r/s]:[B?r:t.width,c?n:t.height]:[t.width,t.height];if(a){var l=0,h=0;return Be(i)?l=we(i,t.width):Be(o)&&(h=we(o,t.height)),Qi(i)?l=h*s:o&&!Qi(o)||(h=l/s),[l,h]}var g=null,w=null;if(Be(i)?g=we(i,t.width):o&&Be(o)&&(w=we(o,t.height)),null===g||o&&!Qi(o)||(w=B&&c?g/r*n:t.height),null!==w&&Qi(i)&&(g=B&&c?w/n*r:t.width),null!==g&&null!==w)return[g,w];throw new Error("Unable to calculate background-size for element")},pi=function(A,e){var t=A[e];return void 0===t?A[0]:t},Fi=function(A,e,t,r,n){var s=e[0],i=e[1],o=t[0],a=t[1];switch(A){case 2:return[new zs(Math.round(r.left),Math.round(r.top+i)),new zs(Math.round(r.left+r.width),Math.round(r.top+i)),new zs(Math.round(r.left+r.width),Math.round(a+r.top+i)),new zs(Math.round(r.left),Math.round(a+r.top+i))];case 3:return[new zs(Math.round(r.left+s),Math.round(r.top)),new zs(Math.round(r.left+s+o),Math.round(r.top)),new zs(Math.round(r.left+s+o),Math.round(r.height+r.top)),new zs(Math.round(r.left+s),Math.round(r.height+r.top))];case 1:return[new zs(Math.round(r.left+s),Math.round(r.top+i)),new zs(Math.round(r.left+s+o),Math.round(r.top+i)),new zs(Math.round(r.left+s+o),Math.round(r.top+i+a)),new zs(Math.round(r.left+s),Math.round(r.top+i+a))];default:return[new zs(Math.round(n.left),Math.round(n.top)),new zs(Math.round(n.left+n.width),Math.round(n.top)),new zs(Math.round(n.left+n.width),Math.round(n.height+n.top)),new zs(Math.round(n.left),Math.round(n.height+n.top))]}},mi="Hidden Text",yi=function(){function A(A){this._data={},this._document=A}return A.prototype.parseMetrics=function(A,e){var t=this._document.createElement("div"),r=this._document.createElement("img"),n=this._document.createElement("span"),s=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=e,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",s.appendChild(t),r.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",r.width=1,r.height=1,r.style.margin="0",r.style.padding="0",r.style.verticalAlign="baseline",n.style.fontFamily=A,n.style.fontSize=e,n.style.margin="0",n.style.padding="0",n.appendChild(this._document.createTextNode(mi)),t.appendChild(n),t.appendChild(r);var i=r.offsetTop-n.offsetTop+2;t.removeChild(n),t.appendChild(this._document.createTextNode(mi)),t.style.lineHeight="normal",r.style.verticalAlign="super";var o=r.offsetTop-t.offsetTop+2;return s.removeChild(t),{baseline:i,middle:o}},A.prototype.getMetrics=function(A,e){var t=A+" "+e;return void 0===this._data[t]&&(this._data[t]=this.parseMetrics(A,e)),this._data[t]},A}(),Hi=function(){return function(A,e){this.context=A,this.options=e}}(),Ei=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r._activeEffects=[],r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),t.canvas||(r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px"),r.fontMetrics=new yi(document),r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.ctx.textBaseline="bottom",r._activeEffects=[],r.context.logger.debug("Canvas renderer initialized ("+t.width+"x"+t.height+") with scale "+t.scale),r}return n(e,A),e.prototype.applyEffects=function(A){for(var e=this;this._activeEffects.length;)this.popEffect();A.forEach((function(A){return e.applyEffect(A)}))},e.prototype.applyEffect=function(A){this.ctx.save(),function(A){return 2===A.type}(A)&&(this.ctx.globalAlpha=A.opacity),function(A){return 0===A.type}(A)&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),ii(A)&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},e.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},e.prototype.renderStack=function(A){return i(this,0,void 0,(function(){return o(this,(function(e){switch(e.label){case 0:return A.element.container.styles.isVisible()?[4,this.renderStackContent(A)]:[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}}))}))},e.prototype.renderNode=function(A){return i(this,0,void 0,(function(){return o(this,(function(e){switch(e.label){case 0:return Fr(A.container.flags,16),A.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(A)]:[3,3];case 1:return e.sent(),[4,this.renderNodeContent(A)];case 2:e.sent(),e.label=3;case 3:return[2]}}))}))},e.prototype.renderTextWithLetterSpacing=function(A,e,t){var r=this;0===e?this.ctx.fillText(A.text,A.bounds.left,A.bounds.top+t):hn(A.text).reduce((function(e,n){return r.ctx.fillText(n,e,A.bounds.top+t),e+r.ctx.measureText(n).width}),A.bounds.left)},e.prototype.createFontStyle=function(A){var e=A.fontVariant.filter((function(A){return"normal"===A||"small-caps"===A})).join(""),t=Li(A.fontFamily).join(", "),r=$A(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,e,A.fontWeight,r,t].join(" "),t,r]},e.prototype.renderTextNode=function(A,e){return i(this,0,void 0,(function(){var t,r,n,s,i,a,B,c,u=this;return o(this,(function(o){return t=this.createFontStyle(e),r=t[0],n=t[1],s=t[2],this.ctx.font=r,this.ctx.direction=1===e.direction?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",i=this.fontMetrics.getMetrics(n,s),a=i.baseline,B=i.middle,c=e.paintOrder,A.textBounds.forEach((function(A){c.forEach((function(t){switch(t){case 0:u.ctx.fillStyle=ye(e.color),u.renderTextWithLetterSpacing(A,e.letterSpacing,a);var r=e.textShadow;r.length&&A.text.trim().length&&(r.slice(0).reverse().forEach((function(t){u.ctx.shadowColor=ye(t.color),u.ctx.shadowOffsetX=t.offsetX.number*u.options.scale,u.ctx.shadowOffsetY=t.offsetY.number*u.options.scale,u.ctx.shadowBlur=t.blur.number,u.renderTextWithLetterSpacing(A,e.letterSpacing,a)})),u.ctx.shadowColor="",u.ctx.shadowOffsetX=0,u.ctx.shadowOffsetY=0,u.ctx.shadowBlur=0),e.textDecorationLine.length&&(u.ctx.fillStyle=ye(e.textDecorationColor||e.color),e.textDecorationLine.forEach((function(e){switch(e){case 1:u.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top+a),A.bounds.width,1);break;case 2:u.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top),A.bounds.width,1);break;case 3:u.ctx.fillRect(A.bounds.left,Math.ceil(A.bounds.top+B),A.bounds.width,1)}})));break;case 1:e.webkitTextStrokeWidth&&A.text.trim().length&&(u.ctx.strokeStyle=ye(e.webkitTextStrokeColor),u.ctx.lineWidth=e.webkitTextStrokeWidth,u.ctx.lineJoin=window.chrome?"miter":"round",u.ctx.strokeText(A.text,A.bounds.left,A.bounds.top+a)),u.ctx.strokeStyle="",u.ctx.lineWidth=0,u.ctx.lineJoin="miter"}}))})),[2]}))}))},e.prototype.renderReplacedElement=function(A,e,t){if(t&&A.intrinsicWidth>0&&A.intrinsicHeight>0){var r=di(A),n=ti(e);this.path(n),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(t,0,0,A.intrinsicWidth,A.intrinsicHeight,r.left,r.top,r.width,r.height),this.ctx.restore()}},e.prototype.renderNodeContent=function(A){return i(this,0,void 0,(function(){var t,r,n,s,i,B,c,u,l,h,g,w,d,f,Q,C,U,p;return o(this,(function(o){switch(o.label){case 0:this.applyEffects(A.getEffects(4)),t=A.container,r=A.curves,n=t.styles,s=0,i=t.textNodes,o.label=1;case 1:return s<i.length?(B=i[s],[4,this.renderTextNode(B,n)]):[3,4];case 2:o.sent(),o.label=3;case 3:return s++,[3,1];case 4:if(!(t instanceof pn))return[3,8];o.label=5;case 5:return o.trys.push([5,7,,8]),[4,this.context.cache.match(t.src)];case 6:return Q=o.sent(),this.renderReplacedElement(t,r,Q),[3,8];case 7:return o.sent(),this.context.logger.error("Error loading image "+t.src),[3,8];case 8:if(t instanceof Fn&&this.renderReplacedElement(t,r,t.canvas),!(t instanceof mn))return[3,12];o.label=9;case 9:return o.trys.push([9,11,,12]),[4,this.context.cache.match(t.svg)];case 10:return Q=o.sent(),this.renderReplacedElement(t,r,Q),[3,12];case 11:return o.sent(),this.context.logger.error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof kn&&t.tree?[4,new e(this.context,{scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,width:t.width,height:t.height}).render(t.tree)]:[3,14];case 13:c=o.sent(),t.width&&t.height&&this.ctx.drawImage(c,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),o.label=14;case 14:if(t instanceof _n&&(u=Math.min(t.bounds.width,t.bounds.height),t.type===bn?t.checked&&(this.ctx.save(),this.path([new zs(t.bounds.left+.39363*u,t.bounds.top+.79*u),new zs(t.bounds.left+.16*u,t.bounds.top+.5549*u),new zs(t.bounds.left+.27347*u,t.bounds.top+.44071*u),new zs(t.bounds.left+.39694*u,t.bounds.top+.5649*u),new zs(t.bounds.left+.72983*u,t.bounds.top+.23*u),new zs(t.bounds.left+.84*u,t.bounds.top+.34085*u),new zs(t.bounds.left+.39363*u,t.bounds.top+.79*u)]),this.ctx.fillStyle=ye(Ln),this.ctx.fill(),this.ctx.restore()):t.type===vn&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+u/2,t.bounds.top+u/2,u/4,0,2*Math.PI,!0),this.ctx.fillStyle=ye(Ln),this.ctx.fill(),this.ctx.restore())),Ii(t)&&t.value.length){switch(l=this.createFontStyle(n),U=l[0],h=l[1],g=this.fontMetrics.getMetrics(U,h).baseline,this.ctx.font=U,this.ctx.fillStyle=ye(n.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=vi(t.styles.textAlign),p=di(t),w=0,t.styles.textAlign){case 1:w+=p.width/2;break;case 2:w+=p.width}d=p.add(w,0,0,-p.height/2+1),this.ctx.save(),this.path([new zs(p.left,p.top),new zs(p.left+p.width,p.top),new zs(p.left+p.width,p.top+p.height),new zs(p.left,p.top+p.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new cn(t.value,d),n.letterSpacing,g),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!Fr(t.styles.display,2048))return[3,20];if(null===t.styles.listStyleImage)return[3,19];if(0!==(f=t.styles.listStyleImage).type)return[3,18];Q=void 0,C=f.url,o.label=15;case 15:return o.trys.push([15,17,,18]),[4,this.context.cache.match(C)];case 16:return Q=o.sent(),this.ctx.drawImage(Q,t.bounds.left-(Q.width+10),t.bounds.top),[3,18];case 17:return o.sent(),this.context.logger.error("Error loading list-style-image "+C),[3,18];case 18:return[3,20];case 19:A.listValue&&-1!==t.styles.listStyleType&&(U=this.createFontStyle(n)[0],this.ctx.font=U,this.ctx.fillStyle=ye(n.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",p=new a(t.bounds.left,t.bounds.top+we(t.styles.paddingTop,t.bounds.width),t.bounds.width,Mt(n.lineHeight,n.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new cn(A.listValue,p),n.letterSpacing,Mt(n.lineHeight,n.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),o.label=20;case 20:return[2]}}))}))},e.prototype.renderStackContent=function(A){return i(this,0,void 0,(function(){var e,t,r,n,s,i,a,B,c,u,l,h,g,w,d;return o(this,(function(o){switch(o.label){case 0:return Fr(A.element.container.flags,16),[4,this.renderNodeBackgroundAndBorders(A.element)];case 1:o.sent(),e=0,t=A.negativeZIndex,o.label=2;case 2:return e<t.length?(d=t[e],[4,this.renderStack(d)]):[3,5];case 3:o.sent(),o.label=4;case 4:return e++,[3,2];case 5:return[4,this.renderNodeContent(A.element)];case 6:o.sent(),r=0,n=A.nonInlineLevel,o.label=7;case 7:return r<n.length?(d=n[r],[4,this.renderNode(d)]):[3,10];case 8:o.sent(),o.label=9;case 9:return r++,[3,7];case 10:s=0,i=A.nonPositionedFloats,o.label=11;case 11:return s<i.length?(d=i[s],[4,this.renderStack(d)]):[3,14];case 12:o.sent(),o.label=13;case 13:return s++,[3,11];case 14:a=0,B=A.nonPositionedInlineLevel,o.label=15;case 15:return a<B.length?(d=B[a],[4,this.renderStack(d)]):[3,18];case 16:o.sent(),o.label=17;case 17:return a++,[3,15];case 18:c=0,u=A.inlineLevel,o.label=19;case 19:return c<u.length?(d=u[c],[4,this.renderNode(d)]):[3,22];case 20:o.sent(),o.label=21;case 21:return c++,[3,19];case 22:l=0,h=A.zeroOrAutoZIndexOrTransformedOrOpacity,o.label=23;case 23:return l<h.length?(d=h[l],[4,this.renderStack(d)]):[3,26];case 24:o.sent(),o.label=25;case 25:return l++,[3,23];case 26:g=0,w=A.positiveZIndex,o.label=27;case 27:return g<w.length?(d=w[g],[4,this.renderStack(d)]):[3,30];case 28:o.sent(),o.label=29;case 29:return g++,[3,27];case 30:return[2]}}))}))},e.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(A.slice(0).reverse()),this.ctx.closePath()},e.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},e.prototype.formatPath=function(A){var e=this;A.forEach((function(A,t){var r=qs(A)?A.start:A;0===t?e.ctx.moveTo(r.x,r.y):e.ctx.lineTo(r.x,r.y),qs(A)&&e.ctx.bezierCurveTo(A.startControl.x,A.startControl.y,A.endControl.x,A.endControl.y,A.end.x,A.end.y)}))},e.prototype.renderRepeat=function(A,e,t,r){this.path(A),this.ctx.fillStyle=e,this.ctx.translate(t,r),this.ctx.fill(),this.ctx.translate(-t,-r)},e.prototype.resizeImage=function(A,e,t){var r;if(A.width===e&&A.height===t)return A;var n=(null!==(r=this.canvas.ownerDocument)&&void 0!==r?r:document).createElement("canvas");return n.width=Math.max(1,e),n.height=Math.max(1,t),n.getContext("2d").drawImage(A,0,0,A.width,A.height,0,0,e,t),n},e.prototype.renderBackgroundImage=function(A){return i(this,0,void 0,(function(){var e,t,r,n,s,i;return o(this,(function(a){switch(a.label){case 0:e=A.styles.backgroundImage.length-1,t=function(t){var n,s,i,a,B,c,u,l,h,g,w,d,f,Q,C,U,p,F,m,y,H,E,I,b,v,K,L,_,x,D,k;return o(this,(function(o){switch(o.label){case 0:if(0!==t.type)return[3,5];n=void 0,s=t.url,o.label=1;case 1:return o.trys.push([1,3,,4]),[4,r.context.cache.match(s)];case 2:return n=o.sent(),[3,4];case 3:return o.sent(),r.context.logger.error("Error loading background-image "+s),[3,4];case 4:return n&&(i=fi(A,e,[n.width,n.height,n.width/n.height]),U=i[0],E=i[1],I=i[2],m=i[3],y=i[4],Q=r.ctx.createPattern(r.resizeImage(n,m,y),"repeat"),r.renderRepeat(U,Q,E,I)),[3,6];case 5:1===t.type?(a=fi(A,e,[null,null,null]),U=a[0],E=a[1],I=a[2],m=a[3],y=a[4],B=Oe(t.angle,m,y),c=B[0],u=B[1],l=B[2],h=B[3],g=B[4],(w=document.createElement("canvas")).width=m,w.height=y,d=w.getContext("2d"),f=d.createLinearGradient(u,h,l,g),Se(t.stops,c).forEach((function(A){return f.addColorStop(A.stop,ye(A.color))})),d.fillStyle=f,d.fillRect(0,0,m,y),m>0&&y>0&&(Q=r.ctx.createPattern(w,"repeat"),r.renderRepeat(U,Q,E,I))):function(A){return 2===A.type}(t)&&(C=fi(A,e,[null,null,null]),U=C[0],p=C[1],F=C[2],m=C[3],y=C[4],H=0===t.position.length?[le]:t.position,E=we(H[0],m),I=we(H[H.length-1],y),b=function(A,e,t,r,n){var s=0,i=0;switch(A.size){case 0:0===A.shape?s=i=Math.min(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(s=Math.min(Math.abs(e),Math.abs(e-r)),i=Math.min(Math.abs(t),Math.abs(t-n)));break;case 2:if(0===A.shape)s=i=Math.min(Te(e,t),Te(e,t-n),Te(e-r,t),Te(e-r,t-n));else if(1===A.shape){var o=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(e),Math.abs(e-r)),a=Me(r,n,e,t,!0),B=a[0],c=a[1];i=o*(s=Te(B-e,(c-t)/o))}break;case 1:0===A.shape?s=i=Math.max(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(s=Math.max(Math.abs(e),Math.abs(e-r)),i=Math.max(Math.abs(t),Math.abs(t-n)));break;case 3:if(0===A.shape)s=i=Math.max(Te(e,t),Te(e,t-n),Te(e-r,t),Te(e-r,t-n));else if(1===A.shape){o=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(e),Math.abs(e-r));var u=Me(r,n,e,t,!1);B=u[0],c=u[1],i=o*(s=Te(B-e,(c-t)/o))}}return Array.isArray(A.size)&&(s=we(A.size[0],r),i=2===A.size.length?we(A.size[1],n):s),[s,i]}(t,E,I,m,y),v=b[0],K=b[1],v>0&&K>0&&(L=r.ctx.createRadialGradient(p+E,F+I,0,p+E,F+I,v),Se(t.stops,2*v).forEach((function(A){return L.addColorStop(A.stop,ye(A.color))})),r.path(U),r.ctx.fillStyle=L,v!==K?(_=A.bounds.left+.5*A.bounds.width,x=A.bounds.top+.5*A.bounds.height,k=1/(D=K/v),r.ctx.save(),r.ctx.translate(_,x),r.ctx.transform(1,0,0,D,0,0),r.ctx.translate(-_,-x),r.ctx.fillRect(p,k*(F-x)+x,m,y*k),r.ctx.restore()):r.ctx.fill())),o.label=6;case 6:return e--,[2]}}))},r=this,n=0,s=A.styles.backgroundImage.slice(0).reverse(),a.label=1;case 1:return n<s.length?(i=s[n],[5,t(i)]):[3,4];case 2:a.sent(),a.label=3;case 3:return n++,[3,1];case 4:return[2]}}))}))},e.prototype.renderSolidBorder=function(A,e,t){return i(this,0,void 0,(function(){return o(this,(function(r){return this.path(li(t,e)),this.ctx.fillStyle=ye(A),this.ctx.fill(),[2]}))}))},e.prototype.renderDoubleBorder=function(A,e,t,r){return i(this,0,void 0,(function(){var n,s;return o(this,(function(i){switch(i.label){case 0:return e<3?[4,this.renderSolidBorder(A,t,r)]:[3,2];case 1:return i.sent(),[2];case 2:return n=function(A,e){switch(e){case 0:return gi(A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox,A.topRightBorderBox,A.topRightBorderDoubleOuterBox);case 1:return gi(A.topRightBorderBox,A.topRightBorderDoubleOuterBox,A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox);case 2:return gi(A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox,A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox);default:return gi(A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox,A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox)}}(r,t),this.path(n),this.ctx.fillStyle=ye(A),this.ctx.fill(),s=function(A,e){switch(e){case 0:return gi(A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox,A.topRightBorderDoubleInnerBox,A.topRightPaddingBox);case 1:return gi(A.topRightBorderDoubleInnerBox,A.topRightPaddingBox,A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox);case 2:return gi(A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox,A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox);default:return gi(A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox,A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox)}}(r,t),this.path(s),this.ctx.fill(),[2]}}))}))},e.prototype.renderNodeBackgroundAndBorders=function(A){return i(this,0,void 0,(function(){var e,t,r,n,s,i,a,B,c=this;return o(this,(function(o){switch(o.label){case 0:return this.applyEffects(A.getEffects(2)),e=A.container.styles,t=!me(e.backgroundColor)||e.backgroundImage.length,r=[{style:e.borderTopStyle,color:e.borderTopColor,width:e.borderTopWidth},{style:e.borderRightStyle,color:e.borderRightColor,width:e.borderRightWidth},{style:e.borderBottomStyle,color:e.borderBottomColor,width:e.borderBottomWidth},{style:e.borderLeftStyle,color:e.borderLeftColor,width:e.borderLeftWidth}],n=bi(pi(e.backgroundClip,0),A.curves),t||e.boxShadow.length?(this.ctx.save(),this.path(n),this.ctx.clip(),me(e.backgroundColor)||(this.ctx.fillStyle=ye(e.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(A.container)]):[3,2];case 1:o.sent(),this.ctx.restore(),e.boxShadow.slice(0).reverse().forEach((function(e){c.ctx.save();var t,r,n,s,i,o=ei(A.curves),a=e.inset?0:1e4,B=(t=o,r=-a+(e.inset?1:-1)*e.spread.number,n=(e.inset?1:-1)*e.spread.number,s=e.spread.number*(e.inset?-2:2),i=e.spread.number*(e.inset?-2:2),t.map((function(A,e){switch(e){case 0:return A.add(r,n);case 1:return A.add(r+s,n);case 2:return A.add(r+s,n+i);case 3:return A.add(r,n+i)}return A})));e.inset?(c.path(o),c.ctx.clip(),c.mask(B)):(c.mask(o),c.ctx.clip(),c.path(B)),c.ctx.shadowOffsetX=e.offsetX.number+a,c.ctx.shadowOffsetY=e.offsetY.number,c.ctx.shadowColor=ye(e.color),c.ctx.shadowBlur=e.blur.number,c.ctx.fillStyle=e.inset?ye(e.color):"rgba(0,0,0,1)",c.ctx.fill(),c.ctx.restore()})),o.label=2;case 2:s=0,i=0,a=r,o.label=3;case 3:return i<a.length?0!==(B=a[i]).style&&!me(B.color)&&B.width>0?2!==B.style?[3,5]:[4,this.renderDashedDottedBorder(B.color,B.width,s,A.curves,2)]:[3,11]:[3,13];case 4:return o.sent(),[3,11];case 5:return 3!==B.style?[3,7]:[4,this.renderDashedDottedBorder(B.color,B.width,s,A.curves,3)];case 6:return o.sent(),[3,11];case 7:return 4!==B.style?[3,9]:[4,this.renderDoubleBorder(B.color,B.width,s,A.curves)];case 8:return o.sent(),[3,11];case 9:return[4,this.renderSolidBorder(B.color,s,A.curves)];case 10:o.sent(),o.label=11;case 11:s++,o.label=12;case 12:return i++,[3,3];case 13:return[2]}}))}))},e.prototype.renderDashedDottedBorder=function(A,e,t,r,n){return i(this,0,void 0,(function(){var s,i,a,B,c,u,l,h,g,w,d,f,Q,C,U,p;return o(this,(function(o){return this.ctx.save(),s=function(A,e){switch(e){case 0:return hi(A.topLeftBorderStroke,A.topRightBorderStroke);case 1:return hi(A.topRightBorderStroke,A.bottomRightBorderStroke);case 2:return hi(A.bottomRightBorderStroke,A.bottomLeftBorderStroke);default:return hi(A.bottomLeftBorderStroke,A.topLeftBorderStroke)}}(r,t),i=li(r,t),2===n&&(this.path(i),this.ctx.clip()),qs(i[0])?(a=i[0].start.x,B=i[0].start.y):(a=i[0].x,B=i[0].y),qs(i[1])?(c=i[1].end.x,u=i[1].end.y):(c=i[1].x,u=i[1].y),l=0===t||2===t?Math.abs(a-c):Math.abs(B-u),this.ctx.beginPath(),3===n?this.formatPath(s):this.formatPath(i.slice(0,2)),h=e<3?3*e:2*e,g=e<3?2*e:e,3===n&&(h=e,g=e),w=!0,l<=2*h?w=!1:l<=2*h+g?(h*=d=l/(2*h+g),g*=d):(f=Math.floor((l+g)/(h+g)),Q=(l-f*h)/(f-1),g=(C=(l-(f+1)*h)/f)<=0||Math.abs(g-Q)<Math.abs(g-C)?Q:C),w&&(3===n?this.ctx.setLineDash([0,h+g]):this.ctx.setLineDash([h,g])),3===n?(this.ctx.lineCap="round",this.ctx.lineWidth=e):this.ctx.lineWidth=2*e+1.1,this.ctx.strokeStyle=ye(A),this.ctx.stroke(),this.ctx.setLineDash([]),2===n&&(qs(i[0])&&(U=i[3],p=i[0],this.ctx.beginPath(),this.formatPath([new zs(U.end.x,U.end.y),new zs(p.start.x,p.start.y)]),this.ctx.stroke()),qs(i[1])&&(U=i[1],p=i[2],this.ctx.beginPath(),this.formatPath([new zs(U.end.x,U.end.y),new zs(p.start.x,p.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]}))}))},e.prototype.render=function(A){return i(this,0,void 0,(function(){var e;return o(this,(function(t){switch(t.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=ye(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),r=new Bi(A,null),n=new ai(r),ci(r,n,n,s=[]),ui(r.container,s),e=n,[4,this.renderStack(e)];case 1:return t.sent(),this.applyEffects([]),[2,this.canvas]}var r,n,s}))}))},e}(Hi),Ii=function(A){return A instanceof Dn||(A instanceof xn||A instanceof _n&&A.type!==vn&&A.type!==bn)},bi=function(A,e){switch(A){case 0:return ei(e);case 2:return function(A){return[A.topLeftContentBox,A.topRightContentBox,A.bottomRightContentBox,A.bottomLeftContentBox]}(e);default:return ti(e)}},vi=function(A){switch(A){case 1:return"center";case 2:return"right";default:return"left"}},Ki=["-apple-system","system-ui"],Li=function(A){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?A.filter((function(A){return-1===Ki.indexOf(A)})):A},_i=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),r.options=t,r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px",r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale),r}return n(e,A),e.prototype.render=function(A){return i(this,0,void 0,(function(){var e,t;return o(this,(function(r){switch(r.label){case 0:return e=on(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,A),[4,xi(e)];case 1:return t=r.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=ye(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(t,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}}))}))},e}(Hi),xi=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},Di=function(){function A(A){var e=A.id,t=A.enabled;this.id=e,this.enabled=t,this.start=Date.now()}return A.prototype.debug=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.debug||this.info.apply(this,A))},A.prototype.getTime=function(){return Date.now()-this.start},A.prototype.info=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&"undefined"!=typeof window&&window.console&&console.info},A.prototype.warn=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.warn||this.info.apply(this,A))},A.prototype.error=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.error||this.info.apply(this,A))},A.instances={},A}(),ki=function(){function A(e,t){var r;this.windowBounds=t,this.instanceName="#"+A.instanceCount++,this.logger=new Di({id:this.instanceName,enabled:e.logging}),this.cache=null!==(r=e.cache)&&void 0!==r?r:new Rs(this,e)}return A.instanceCount=1,A}(),Si=function(A,e){return void 0===e&&(e={}),Ti(A,e)};"undefined"!=typeof window&&Ms.setContext(window);var Oi,Ti=function(A,e){return i(void 0,0,void 0,(function(){var t,r,n,i,c,u,l,h,g,w,d,f,Q,C,U,p,F,m,y,H,E,I,b,v,K,L,_,x,D,k,S,O,T,M,R,G,V,N;return o(this,(function(o){switch(o.label){case 0:if(!A||"object"!=typeof A)return[2,Promise.reject("Invalid element provided as first argument")];if(!(t=A.ownerDocument))throw new Error("Element is not attached to a Document");if(!(r=t.defaultView))throw new Error("Document is not attached to a Window");return n={allowTaint:null!==(I=e.allowTaint)&&void 0!==I&&I,imageTimeout:null!==(b=e.imageTimeout)&&void 0!==b?b:15e3,proxy:e.proxy,useCORS:null!==(v=e.useCORS)&&void 0!==v&&v},i=s({logging:null===(K=e.logging)||void 0===K||K,cache:e.cache},n),c={windowWidth:null!==(L=e.windowWidth)&&void 0!==L?L:r.innerWidth,windowHeight:null!==(_=e.windowHeight)&&void 0!==_?_:r.innerHeight,scrollX:null!==(x=e.scrollX)&&void 0!==x?x:r.pageXOffset,scrollY:null!==(D=e.scrollY)&&void 0!==D?D:r.pageYOffset},u=new a(c.scrollX,c.scrollY,c.windowWidth,c.windowHeight),l=new ki(i,u),h=null!==(k=e.foreignObjectRendering)&&void 0!==k&&k,g={allowTaint:null!==(S=e.allowTaint)&&void 0!==S&&S,onclone:e.onclone,ignoreElements:e.ignoreElements,inlineImages:h,copyStyles:h},l.logger.debug("Starting document clone with size "+u.width+"x"+u.height+" scrolled to "+-u.left+","+-u.top),w=new Fs(l,A,g),(d=w.clonedReferenceElement)?[4,w.toIFrame(t,u)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return f=o.sent(),Q=Zn(d)||"HTML"===d.tagName?function(A){var e=A.body,t=A.documentElement;if(!e||!t)throw new Error("Unable to get document size");var r=Math.max(Math.max(e.scrollWidth,t.scrollWidth),Math.max(e.offsetWidth,t.offsetWidth),Math.max(e.clientWidth,t.clientWidth)),n=Math.max(Math.max(e.scrollHeight,t.scrollHeight),Math.max(e.offsetHeight,t.offsetHeight),Math.max(e.clientHeight,t.clientHeight));return new a(0,0,r,n)}(d.ownerDocument):B(l,d),C=Q.width,U=Q.height,p=Q.left,F=Q.top,m=Mi(l,d,e.backgroundColor),y={canvas:e.canvas,backgroundColor:m,scale:null!==(T=null!==(O=e.scale)&&void 0!==O?O:r.devicePixelRatio)&&void 0!==T?T:1,x:(null!==(M=e.x)&&void 0!==M?M:0)+p,y:(null!==(R=e.y)&&void 0!==R?R:0)+F,width:null!==(G=e.width)&&void 0!==G?G:Math.ceil(C),height:null!==(V=e.height)&&void 0!==V?V:Math.ceil(U)},h?(l.logger.debug("Document cloned, using foreign object rendering"),[4,new _i(l,y).render(d)]):[3,3];case 2:return H=o.sent(),[3,5];case 3:return l.logger.debug("Document cloned, element located at "+p+","+F+" with size "+C+"x"+U+" using computed rendering"),l.logger.debug("Starting DOM parsing"),E=Mn(l,d),m===E.styles.backgroundColor&&(E.styles.backgroundColor=_e.TRANSPARENT),l.logger.debug("Starting renderer for element at "+y.x+","+y.y+" with size "+y.width+"x"+y.height),[4,new Ei(l,y).render(E)];case 4:H=o.sent(),o.label=5;case 5:return(null===(N=e.removeContainer)||void 0===N||N)&&(Fs.destroy(f)||l.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),l.logger.debug("Finished rendering"),[2,H]}}))}))},Mi=function(A,e,t){var r=e.ownerDocument,n=r.documentElement?Le(A,getComputedStyle(r.documentElement).backgroundColor):_e.TRANSPARENT,s=r.body?Le(A,getComputedStyle(r.body).backgroundColor):_e.TRANSPARENT,i="string"==typeof t?Le(A,t):null===t?_e.TRANSPARENT:4294967295;return e===r.documentElement?me(n)?me(s)?i:s:n:i},Ri={exports:{}};var Gi,Vi=(Oi||(Oi=1,Gi=Ri,function(){function e(A,e){return void 0===e?e={autoBom:!1}:"object"!=typeof e&&(e={autoBom:!e}),e.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(A.type)?new Blob(["\ufeff",A],{type:A.type}):A}function t(A,e,t){var r=new XMLHttpRequest;r.open("GET",A),r.responseType="blob",r.onload=function(){o(r.response,e,t)},r.onerror=function(){},r.send()}function r(A){var e=new XMLHttpRequest;e.open("HEAD",A,!1);try{e.send()}catch(t){}return 200<=e.status&&299>=e.status}function n(A){try{A.dispatchEvent(new MouseEvent("click"))}catch(t){var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),A.dispatchEvent(e)}}var s="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof A&&A.global===A?A:void 0,i=s.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),o=s.saveAs||("object"!=typeof window||window!==s?function(){}:"download"in HTMLAnchorElement.prototype&&!i?function(A,e,i){var o=s.URL||s.webkitURL,a=document.createElement("a");e=e||A.name||"download",a.download=e,a.rel="noopener","string"==typeof A?(a.href=A,a.origin===location.origin?n(a):r(a.href)?t(A,e,i):n(a,a.target="_blank")):(a.href=o.createObjectURL(A),setTimeout((function(){o.revokeObjectURL(a.href)}),4e4),setTimeout((function(){n(a)}),0))}:"msSaveOrOpenBlob"in navigator?function(A,s,i){if(s=s||A.name||"download","string"!=typeof A)navigator.msSaveOrOpenBlob(e(A,i),s);else if(r(A))t(A,s,i);else{var o=document.createElement("a");o.href=A,o.target="_blank",setTimeout((function(){n(o)}))}}:function(A,e,r,n){if((n=n||open("","_blank"))&&(n.document.title=n.document.body.innerText="downloading..."),"string"==typeof A)return t(A,e,r);var o="application/octet-stream"===A.type,a=/constructor/i.test(s.HTMLElement)||s.safari,B=/CriOS\/[\d]+/.test(navigator.userAgent);if((B||o&&a||i)&&"undefined"!=typeof FileReader){var c=new FileReader;c.onloadend=function(){var A=c.result;A=B?A:A.replace(/^data:[^;]*;/,"data:attachment/file;"),n?n.location.href=A:location=A,n=null},c.readAsDataURL(A)}else{var u=s.URL||s.webkitURL,l=u.createObjectURL(A);n?n.location=l:location.href=l,n=null,setTimeout((function(){u.revokeObjectURL(l)}),4e4)}});s.saveAs=o.saveAs=o,Gi.exports=o}()),Ri.exports);function Ni(A){throw new Error('Could not dynamically require "'+A+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Pi,Xi={exports:{}};
/*!

JSZip v3.10.1 - A JavaScript class for generating and reading zip files
<http://stuartk.com/jszip>

(c) 2009-2016 Stuart Knightley <stuart [at] stuartk.com>
Dual licenced under the MIT license or GPLv3. See https://raw.github.com/Stuk/jszip/main/LICENSE.markdown.

JSZip uses the library pako released under the MIT license :
https://github.com/nodeca/pako/blob/main/LICENSE
*/var Ji=(Pi||(Pi=1,Xi.exports=function A(e,t,r){function n(i,o){if(!t[i]){if(!e[i]){if(!o&&Ni)return Ni(i);if(s)return s(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var B=t[i]={exports:{}};e[i][0].call(B.exports,(function(A){return n(e[i][1][A]||A)}),B,B.exports,A,e,t,r)}return t[i].exports}for(var s=Ni,i=0;i<r.length;i++)n(r[i]);return n}({1:[function(A,e,t){var r=A("./utils"),n=A("./support"),s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";t.encode=function(A){for(var e,t,n,i,o,a,B,c=[],u=0,l=A.length,h=l,g="string"!==r.getTypeOf(A);u<A.length;)h=l-u,n=g?(e=A[u++],t=u<l?A[u++]:0,u<l?A[u++]:0):(e=A.charCodeAt(u++),t=u<l?A.charCodeAt(u++):0,u<l?A.charCodeAt(u++):0),i=e>>2,o=(3&e)<<4|t>>4,a=1<h?(15&t)<<2|n>>6:64,B=2<h?63&n:64,c.push(s.charAt(i)+s.charAt(o)+s.charAt(a)+s.charAt(B));return c.join("")},t.decode=function(A){var e,t,r,i,o,a,B=0,c=0,u="data:";if(A.substr(0,u.length)===u)throw new Error("Invalid base64 input, it looks like a data url.");var l,h=3*(A=A.replace(/[^A-Za-z0-9+/=]/g,"")).length/4;if(A.charAt(A.length-1)===s.charAt(64)&&h--,A.charAt(A.length-2)===s.charAt(64)&&h--,h%1!=0)throw new Error("Invalid base64 input, bad content length.");for(l=n.uint8array?new Uint8Array(0|h):new Array(0|h);B<A.length;)e=s.indexOf(A.charAt(B++))<<2|(i=s.indexOf(A.charAt(B++)))>>4,t=(15&i)<<4|(o=s.indexOf(A.charAt(B++)))>>2,r=(3&o)<<6|(a=s.indexOf(A.charAt(B++))),l[c++]=e,64!==o&&(l[c++]=t),64!==a&&(l[c++]=r);return l}},{"./support":30,"./utils":32}],2:[function(A,e,t){var r=A("./external"),n=A("./stream/DataWorker"),s=A("./stream/Crc32Probe"),i=A("./stream/DataLengthProbe");function o(A,e,t,r,n){this.compressedSize=A,this.uncompressedSize=e,this.crc32=t,this.compression=r,this.compressedContent=n}o.prototype={getContentWorker:function(){var A=new n(r.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new i("data_length")),e=this;return A.on("end",(function(){if(this.streamInfo.data_length!==e.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")})),A},getCompressedWorker:function(){return new n(r.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},o.createWorkerFrom=function(A,e,t){return A.pipe(new s).pipe(new i("uncompressedSize")).pipe(e.compressWorker(t)).pipe(new i("compressedSize")).withStreamInfo("compression",e)},e.exports=o},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(A,e,t){var r=A("./stream/GenericWorker");t.STORE={magic:"\0\0",compressWorker:function(){return new r("STORE compression")},uncompressWorker:function(){return new r("STORE decompression")}},t.DEFLATE=A("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(A,e,t){var r=A("./utils"),n=function(){for(var A,e=[],t=0;t<256;t++){A=t;for(var r=0;r<8;r++)A=1&A?3988292384^A>>>1:A>>>1;e[t]=A}return e}();e.exports=function(A,e){return void 0!==A&&A.length?"string"!==r.getTypeOf(A)?function(A,e,t,r){var s=n,i=r+t;A^=-1;for(var o=r;o<i;o++)A=A>>>8^s[255&(A^e[o])];return-1^A}(0|e,A,A.length,0):function(A,e,t,r){var s=n,i=r+t;A^=-1;for(var o=r;o<i;o++)A=A>>>8^s[255&(A^e.charCodeAt(o))];return-1^A}(0|e,A,A.length,0):0}},{"./utils":32}],5:[function(A,e,t){t.base64=!1,t.binary=!1,t.dir=!1,t.createFolders=!0,t.date=null,t.compression=null,t.compressionOptions=null,t.comment=null,t.unixPermissions=null,t.dosPermissions=null},{}],6:[function(A,e,t){var r=null;r="undefined"!=typeof Promise?Promise:A("lie"),e.exports={Promise:r}},{lie:37}],7:[function(A,e,t){var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,n=A("pako"),s=A("./utils"),i=A("./stream/GenericWorker"),o=r?"uint8array":"array";function a(A,e){i.call(this,"FlateWorker/"+A),this._pako=null,this._pakoAction=A,this._pakoOptions=e,this.meta={}}t.magic="\b\0",s.inherits(a,i),a.prototype.processChunk=function(A){this.meta=A.meta,null===this._pako&&this._createPako(),this._pako.push(s.transformTo(o,A.data),!1)},a.prototype.flush=function(){i.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},a.prototype.cleanUp=function(){i.prototype.cleanUp.call(this),this._pako=null},a.prototype._createPako=function(){this._pako=new n[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var A=this;this._pako.onData=function(e){A.push({data:e,meta:A.meta})}},t.compressWorker=function(A){return new a("Deflate",A)},t.uncompressWorker=function(){return new a("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:38}],8:[function(A,e,t){function r(A,e){var t,r="";for(t=0;t<e;t++)r+=String.fromCharCode(255&A),A>>>=8;return r}function n(A,e,t,n,i,c){var u,l,h=A.file,g=A.compression,w=c!==o.utf8encode,d=s.transformTo("string",c(h.name)),f=s.transformTo("string",o.utf8encode(h.name)),Q=h.comment,C=s.transformTo("string",c(Q)),U=s.transformTo("string",o.utf8encode(Q)),p=f.length!==h.name.length,F=U.length!==Q.length,m="",y="",H="",E=h.dir,I=h.date,b={crc32:0,compressedSize:0,uncompressedSize:0};e&&!t||(b.crc32=A.crc32,b.compressedSize=A.compressedSize,b.uncompressedSize=A.uncompressedSize);var v=0;e&&(v|=8),w||!p&&!F||(v|=2048);var K,L,_,x=0,D=0;E&&(x|=16),"UNIX"===i?(D=798,x|=(K=h.unixPermissions,L=E,_=K,K||(_=L?16893:33204),(65535&_)<<16)):(D=20,x|=function(A){return 63&(A||0)}(h.dosPermissions)),u=I.getUTCHours(),u<<=6,u|=I.getUTCMinutes(),u<<=5,u|=I.getUTCSeconds()/2,l=I.getUTCFullYear()-1980,l<<=4,l|=I.getUTCMonth()+1,l<<=5,l|=I.getUTCDate(),p&&(y=r(1,1)+r(a(d),4)+f,m+="up"+r(y.length,2)+y),F&&(H=r(1,1)+r(a(C),4)+U,m+="uc"+r(H.length,2)+H);var k="";return k+="\n\0",k+=r(v,2),k+=g.magic,k+=r(u,2),k+=r(l,2),k+=r(b.crc32,4),k+=r(b.compressedSize,4),k+=r(b.uncompressedSize,4),k+=r(d.length,2),k+=r(m.length,2),{fileRecord:B.LOCAL_FILE_HEADER+k+d+m,dirRecord:B.CENTRAL_FILE_HEADER+r(D,2)+k+r(C.length,2)+"\0\0\0\0"+r(x,4)+r(n,4)+d+m+C}}var s=A("../utils"),i=A("../stream/GenericWorker"),o=A("../utf8"),a=A("../crc32"),B=A("../signature");function c(A,e,t,r){i.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=e,this.zipPlatform=t,this.encodeFileName=r,this.streamFiles=A,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}s.inherits(c,i),c.prototype.push=function(A){var e=A.meta.percent||0,t=this.entriesCount,r=this._sources.length;this.accumulate?this.contentBuffer.push(A):(this.bytesWritten+=A.data.length,i.prototype.push.call(this,{data:A.data,meta:{currentFile:this.currentFile,percent:t?(e+100*(t-r-1))/t:100}}))},c.prototype.openedSource=function(A){this.currentSourceOffset=this.bytesWritten,this.currentFile=A.file.name;var e=this.streamFiles&&!A.file.dir;if(e){var t=n(A,e,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:t.fileRecord,meta:{percent:0}})}else this.accumulate=!0},c.prototype.closedSource=function(A){this.accumulate=!1;var e,t=this.streamFiles&&!A.file.dir,s=n(A,t,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(s.dirRecord),t)this.push({data:(e=A,B.DATA_DESCRIPTOR+r(e.crc32,4)+r(e.compressedSize,4)+r(e.uncompressedSize,4)),meta:{percent:100}});else for(this.push({data:s.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},c.prototype.flush=function(){for(var A=this.bytesWritten,e=0;e<this.dirRecords.length;e++)this.push({data:this.dirRecords[e],meta:{percent:100}});var t,n,i,o,a,c,u=this.bytesWritten-A,l=(t=this.dirRecords.length,n=u,i=A,o=this.zipComment,a=this.encodeFileName,c=s.transformTo("string",a(o)),B.CENTRAL_DIRECTORY_END+"\0\0\0\0"+r(t,2)+r(t,2)+r(n,4)+r(i,4)+r(c.length,2)+c);this.push({data:l,meta:{percent:100}})},c.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},c.prototype.registerPrevious=function(A){this._sources.push(A);var e=this;return A.on("data",(function(A){e.processChunk(A)})),A.on("end",(function(){e.closedSource(e.previous.streamInfo),e._sources.length?e.prepareNextSource():e.end()})),A.on("error",(function(A){e.error(A)})),this},c.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},c.prototype.error=function(A){var e=this._sources;if(!i.prototype.error.call(this,A))return!1;for(var t=0;t<e.length;t++)try{e[t].error(A)}catch(r){}return!0},c.prototype.lock=function(){i.prototype.lock.call(this);for(var A=this._sources,e=0;e<A.length;e++)A[e].lock()},e.exports=c},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(A,e,t){var r=A("../compressions"),n=A("./ZipFileWorker");t.generateWorker=function(A,e,t){var s=new n(e.streamFiles,t,e.platform,e.encodeFileName),i=0;try{A.forEach((function(A,t){i++;var n=function(A,e){var t=A||e,n=r[t];if(!n)throw new Error(t+" is not a valid compression method !");return n}(t.options.compression,e.compression),o=t.options.compressionOptions||e.compressionOptions||{},a=t.dir,B=t.date;t._compressWorker(n,o).withStreamInfo("file",{name:A,dir:a,date:B,comment:t.comment||"",unixPermissions:t.unixPermissions,dosPermissions:t.dosPermissions}).pipe(s)})),s.entriesCount=i}catch(o){s.error(o)}return s}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(A,e,t){function r(){if(!(this instanceof r))return new r;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var A=new r;for(var e in this)"function"!=typeof this[e]&&(A[e]=this[e]);return A}}(r.prototype=A("./object")).loadAsync=A("./load"),r.support=A("./support"),r.defaults=A("./defaults"),r.version="3.10.1",r.loadAsync=function(A,e){return(new r).loadAsync(A,e)},r.external=A("./external"),e.exports=r},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(A,e,t){var r=A("./utils"),n=A("./external"),s=A("./utf8"),i=A("./zipEntries"),o=A("./stream/Crc32Probe"),a=A("./nodejsUtils");function B(A){return new n.Promise((function(e,t){var r=A.decompressed.getContentWorker().pipe(new o);r.on("error",(function(A){t(A)})).on("end",(function(){r.streamInfo.crc32!==A.decompressed.crc32?t(new Error("Corrupted zip : CRC32 mismatch")):e()})).resume()}))}e.exports=function(A,e){var t=this;return e=r.extend(e||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:s.utf8decode}),a.isNode&&a.isStream(A)?n.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):r.prepareContent("the loaded zip file",A,!0,e.optimizedBinaryString,e.base64).then((function(A){var t=new i(e);return t.load(A),t})).then((function(A){var t=[n.Promise.resolve(A)],r=A.files;if(e.checkCRC32)for(var s=0;s<r.length;s++)t.push(B(r[s]));return n.Promise.all(t)})).then((function(A){for(var n=A.shift(),s=n.files,i=0;i<s.length;i++){var o=s[i],a=o.fileNameStr,B=r.resolve(o.fileNameStr);t.file(B,o.decompressed,{binary:!0,optimizedBinaryString:!0,date:o.date,dir:o.dir,comment:o.fileCommentStr.length?o.fileCommentStr:null,unixPermissions:o.unixPermissions,dosPermissions:o.dosPermissions,createFolders:e.createFolders}),o.dir||(t.file(B).unsafeOriginalName=a)}return n.zipComment.length&&(t.comment=n.zipComment),t}))}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(A,e,t){var r=A("../utils"),n=A("../stream/GenericWorker");function s(A,e){n.call(this,"Nodejs stream input adapter for "+A),this._upstreamEnded=!1,this._bindStream(e)}r.inherits(s,n),s.prototype._bindStream=function(A){var e=this;(this._stream=A).pause(),A.on("data",(function(A){e.push({data:A,meta:{percent:0}})})).on("error",(function(A){e.isPaused?this.generatedError=A:e.error(A)})).on("end",(function(){e.isPaused?e._upstreamEnded=!0:e.end()}))},s.prototype.pause=function(){return!!n.prototype.pause.call(this)&&(this._stream.pause(),!0)},s.prototype.resume=function(){return!!n.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},e.exports=s},{"../stream/GenericWorker":28,"../utils":32}],13:[function(A,e,t){var r=A("readable-stream").Readable;function n(A,e,t){r.call(this,e),this._helper=A;var n=this;A.on("data",(function(A,e){n.push(A)||n._helper.pause(),t&&t(e)})).on("error",(function(A){n.emit("error",A)})).on("end",(function(){n.push(null)}))}A("../utils").inherits(n,r),n.prototype._read=function(){this._helper.resume()},e.exports=n},{"../utils":32,"readable-stream":16}],14:[function(A,e,t){e.exports={isNode:"undefined"!=typeof Buffer,newBufferFrom:function(A,e){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(A,e);if("number"==typeof A)throw new Error('The "data" argument must not be a number');return new Buffer(A,e)},allocBuffer:function(A){if(Buffer.alloc)return Buffer.alloc(A);var e=new Buffer(A);return e.fill(0),e},isBuffer:function(A){return Buffer.isBuffer(A)},isStream:function(A){return A&&"function"==typeof A.on&&"function"==typeof A.pause&&"function"==typeof A.resume}}},{}],15:[function(A,e,t){function r(A,e,t){var r,n=s.getTypeOf(e),o=s.extend(t||{},a);o.date=o.date||new Date,null!==o.compression&&(o.compression=o.compression.toUpperCase()),"string"==typeof o.unixPermissions&&(o.unixPermissions=parseInt(o.unixPermissions,8)),o.unixPermissions&&16384&o.unixPermissions&&(o.dir=!0),o.dosPermissions&&16&o.dosPermissions&&(o.dir=!0),o.dir&&(A=w(A)),o.createFolders&&(r=g(A))&&d.call(this,r,!0);var u="string"===n&&!1===o.binary&&!1===o.base64;t&&void 0!==t.binary||(o.binary=!u),(e instanceof B&&0===e.uncompressedSize||o.dir||!e||0===e.length)&&(o.base64=!1,o.binary=!0,e="",o.compression="STORE",n="string");var f=null;f=e instanceof B||e instanceof i?e:l.isNode&&l.isStream(e)?new h(A,e):s.prepareContent(A,e,o.binary,o.optimizedBinaryString,o.base64);var Q=new c(A,f,o);this.files[A]=Q}var n=A("./utf8"),s=A("./utils"),i=A("./stream/GenericWorker"),o=A("./stream/StreamHelper"),a=A("./defaults"),B=A("./compressedObject"),c=A("./zipObject"),u=A("./generate"),l=A("./nodejsUtils"),h=A("./nodejs/NodejsStreamInputAdapter"),g=function(A){"/"===A.slice(-1)&&(A=A.substring(0,A.length-1));var e=A.lastIndexOf("/");return 0<e?A.substring(0,e):""},w=function(A){return"/"!==A.slice(-1)&&(A+="/"),A},d=function(A,e){return e=void 0!==e?e:a.createFolders,A=w(A),this.files[A]||r.call(this,A,null,{dir:!0,createFolders:e}),this.files[A]};function f(A){return"[object RegExp]"===Object.prototype.toString.call(A)}var Q={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(A){var e,t,r;for(e in this.files)r=this.files[e],(t=e.slice(this.root.length,e.length))&&e.slice(0,this.root.length)===this.root&&A(t,r)},filter:function(A){var e=[];return this.forEach((function(t,r){A(t,r)&&e.push(r)})),e},file:function(A,e,t){if(1!==arguments.length)return A=this.root+A,r.call(this,A,e,t),this;if(f(A)){var n=A;return this.filter((function(A,e){return!e.dir&&n.test(A)}))}var s=this.files[this.root+A];return s&&!s.dir?s:null},folder:function(A){if(!A)return this;if(f(A))return this.filter((function(e,t){return t.dir&&A.test(e)}));var e=this.root+A,t=d.call(this,e),r=this.clone();return r.root=t.name,r},remove:function(A){A=this.root+A;var e=this.files[A];if(e||("/"!==A.slice(-1)&&(A+="/"),e=this.files[A]),e&&!e.dir)delete this.files[A];else for(var t=this.filter((function(e,t){return t.name.slice(0,A.length)===A})),r=0;r<t.length;r++)delete this.files[t[r].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(A){var e,t={};try{if((t=s.extend(A||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:n.utf8encode})).type=t.type.toLowerCase(),t.compression=t.compression.toUpperCase(),"binarystring"===t.type&&(t.type="string"),!t.type)throw new Error("No output type specified.");s.checkSupport(t.type),"darwin"!==t.platform&&"freebsd"!==t.platform&&"linux"!==t.platform&&"sunos"!==t.platform||(t.platform="UNIX"),"win32"===t.platform&&(t.platform="DOS");var r=t.comment||this.comment||"";e=u.generateWorker(this,t,r)}catch(a){(e=new i("error")).error(a)}return new o(e,t.type||"string",t.mimeType)},generateAsync:function(A,e){return this.generateInternalStream(A).accumulate(e)},generateNodeStream:function(A,e){return(A=A||{}).type||(A.type="nodebuffer"),this.generateInternalStream(A).toNodejsStream(e)}};e.exports=Q},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(A,e,t){e.exports=A("stream")},{stream:void 0}],17:[function(A,e,t){var r=A("./DataReader");function n(A){r.call(this,A);for(var e=0;e<this.data.length;e++)A[e]=255&A[e]}A("../utils").inherits(n,r),n.prototype.byteAt=function(A){return this.data[this.zero+A]},n.prototype.lastIndexOfSignature=function(A){for(var e=A.charCodeAt(0),t=A.charCodeAt(1),r=A.charCodeAt(2),n=A.charCodeAt(3),s=this.length-4;0<=s;--s)if(this.data[s]===e&&this.data[s+1]===t&&this.data[s+2]===r&&this.data[s+3]===n)return s-this.zero;return-1},n.prototype.readAndCheckSignature=function(A){var e=A.charCodeAt(0),t=A.charCodeAt(1),r=A.charCodeAt(2),n=A.charCodeAt(3),s=this.readData(4);return e===s[0]&&t===s[1]&&r===s[2]&&n===s[3]},n.prototype.readData=function(A){if(this.checkOffset(A),0===A)return[];var e=this.data.slice(this.zero+this.index,this.zero+this.index+A);return this.index+=A,e},e.exports=n},{"../utils":32,"./DataReader":18}],18:[function(A,e,t){var r=A("../utils");function n(A){this.data=A,this.length=A.length,this.index=0,this.zero=0}n.prototype={checkOffset:function(A){this.checkIndex(this.index+A)},checkIndex:function(A){if(this.length<this.zero+A||A<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+A+"). Corrupted zip ?")},setIndex:function(A){this.checkIndex(A),this.index=A},skip:function(A){this.setIndex(this.index+A)},byteAt:function(){},readInt:function(A){var e,t=0;for(this.checkOffset(A),e=this.index+A-1;e>=this.index;e--)t=(t<<8)+this.byteAt(e);return this.index+=A,t},readString:function(A){return r.transformTo("string",this.readData(A))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var A=this.readInt(4);return new Date(Date.UTC(1980+(A>>25&127),(A>>21&15)-1,A>>16&31,A>>11&31,A>>5&63,(31&A)<<1))}},e.exports=n},{"../utils":32}],19:[function(A,e,t){var r=A("./Uint8ArrayReader");function n(A){r.call(this,A)}A("../utils").inherits(n,r),n.prototype.readData=function(A){this.checkOffset(A);var e=this.data.slice(this.zero+this.index,this.zero+this.index+A);return this.index+=A,e},e.exports=n},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(A,e,t){var r=A("./DataReader");function n(A){r.call(this,A)}A("../utils").inherits(n,r),n.prototype.byteAt=function(A){return this.data.charCodeAt(this.zero+A)},n.prototype.lastIndexOfSignature=function(A){return this.data.lastIndexOf(A)-this.zero},n.prototype.readAndCheckSignature=function(A){return A===this.readData(4)},n.prototype.readData=function(A){this.checkOffset(A);var e=this.data.slice(this.zero+this.index,this.zero+this.index+A);return this.index+=A,e},e.exports=n},{"../utils":32,"./DataReader":18}],21:[function(A,e,t){var r=A("./ArrayReader");function n(A){r.call(this,A)}A("../utils").inherits(n,r),n.prototype.readData=function(A){if(this.checkOffset(A),0===A)return new Uint8Array(0);var e=this.data.subarray(this.zero+this.index,this.zero+this.index+A);return this.index+=A,e},e.exports=n},{"../utils":32,"./ArrayReader":17}],22:[function(A,e,t){var r=A("../utils"),n=A("../support"),s=A("./ArrayReader"),i=A("./StringReader"),o=A("./NodeBufferReader"),a=A("./Uint8ArrayReader");e.exports=function(A){var e=r.getTypeOf(A);return r.checkSupport(e),"string"!==e||n.uint8array?"nodebuffer"===e?new o(A):n.uint8array?new a(r.transformTo("uint8array",A)):new s(r.transformTo("array",A)):new i(A)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(A,e,t){t.LOCAL_FILE_HEADER="PK",t.CENTRAL_FILE_HEADER="PK",t.CENTRAL_DIRECTORY_END="PK",t.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",t.ZIP64_CENTRAL_DIRECTORY_END="PK",t.DATA_DESCRIPTOR="PK\b"},{}],24:[function(A,e,t){var r=A("./GenericWorker"),n=A("../utils");function s(A){r.call(this,"ConvertWorker to "+A),this.destType=A}n.inherits(s,r),s.prototype.processChunk=function(A){this.push({data:n.transformTo(this.destType,A.data),meta:A.meta})},e.exports=s},{"../utils":32,"./GenericWorker":28}],25:[function(A,e,t){var r=A("./GenericWorker"),n=A("../crc32");function s(){r.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}A("../utils").inherits(s,r),s.prototype.processChunk=function(A){this.streamInfo.crc32=n(A.data,this.streamInfo.crc32||0),this.push(A)},e.exports=s},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(A,e,t){var r=A("../utils"),n=A("./GenericWorker");function s(A){n.call(this,"DataLengthProbe for "+A),this.propName=A,this.withStreamInfo(A,0)}r.inherits(s,n),s.prototype.processChunk=function(A){if(A){var e=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=e+A.data.length}n.prototype.processChunk.call(this,A)},e.exports=s},{"../utils":32,"./GenericWorker":28}],27:[function(A,e,t){var r=A("../utils"),n=A("./GenericWorker");function s(A){n.call(this,"DataWorker");var e=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,A.then((function(A){e.dataIsReady=!0,e.data=A,e.max=A&&A.length||0,e.type=r.getTypeOf(A),e.isPaused||e._tickAndRepeat()}),(function(A){e.error(A)}))}r.inherits(s,n),s.prototype.cleanUp=function(){n.prototype.cleanUp.call(this),this.data=null},s.prototype.resume=function(){return!!n.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,r.delay(this._tickAndRepeat,[],this)),!0)},s.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(r.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},s.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var A=null,e=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":A=this.data.substring(this.index,e);break;case"uint8array":A=this.data.subarray(this.index,e);break;case"array":case"nodebuffer":A=this.data.slice(this.index,e)}return this.index=e,this.push({data:A,meta:{percent:this.max?this.index/this.max*100:0}})},e.exports=s},{"../utils":32,"./GenericWorker":28}],28:[function(A,e,t){function r(A){this.name=A||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}r.prototype={push:function(A){this.emit("data",A)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(A){this.emit("error",A)}return!0},error:function(A){return!this.isFinished&&(this.isPaused?this.generatedError=A:(this.isFinished=!0,this.emit("error",A),this.previous&&this.previous.error(A),this.cleanUp()),!0)},on:function(A,e){return this._listeners[A].push(e),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(A,e){if(this._listeners[A])for(var t=0;t<this._listeners[A].length;t++)this._listeners[A][t].call(this,e)},pipe:function(A){return A.registerPrevious(this)},registerPrevious:function(A){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=A.streamInfo,this.mergeStreamInfo(),this.previous=A;var e=this;return A.on("data",(function(A){e.processChunk(A)})),A.on("end",(function(){e.end()})),A.on("error",(function(A){e.error(A)})),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;var A=this.isPaused=!1;return this.generatedError&&(this.error(this.generatedError),A=!0),this.previous&&this.previous.resume(),!A},flush:function(){},processChunk:function(A){this.push(A)},withStreamInfo:function(A,e){return this.extraStreamInfo[A]=e,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var A in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,A)&&(this.streamInfo[A]=this.extraStreamInfo[A])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var A="Worker "+this.name;return this.previous?this.previous+" -> "+A:A}},e.exports=r},{}],29:[function(A,e,t){var r=A("../utils"),n=A("./ConvertWorker"),s=A("./GenericWorker"),i=A("../base64"),o=A("../support"),a=A("../external"),B=null;if(o.nodestream)try{B=A("../nodejs/NodejsStreamOutputAdapter")}catch(l){}function c(A,e){return new a.Promise((function(t,n){var s=[],o=A._internalType,a=A._outputType,B=A._mimeType;A.on("data",(function(A,t){s.push(A),e&&e(t)})).on("error",(function(A){s=[],n(A)})).on("end",(function(){try{var A=function(A,e,t){switch(A){case"blob":return r.newBlob(r.transformTo("arraybuffer",e),t);case"base64":return i.encode(e);default:return r.transformTo(A,e)}}(a,function(A,e){var t,r=0,n=null,s=0;for(t=0;t<e.length;t++)s+=e[t].length;switch(A){case"string":return e.join("");case"array":return Array.prototype.concat.apply([],e);case"uint8array":for(n=new Uint8Array(s),t=0;t<e.length;t++)n.set(e[t],r),r+=e[t].length;return n;case"nodebuffer":return Buffer.concat(e);default:throw new Error("concat : unsupported type '"+A+"'")}}(o,s),B);t(A)}catch(e){n(e)}s=[]})).resume()}))}function u(A,e,t){var i=e;switch(e){case"blob":case"arraybuffer":i="uint8array";break;case"base64":i="string"}try{this._internalType=i,this._outputType=e,this._mimeType=t,r.checkSupport(i),this._worker=A.pipe(new n(i)),A.lock()}catch(o){this._worker=new s("error"),this._worker.error(o)}}u.prototype={accumulate:function(A){return c(this,A)},on:function(A,e){var t=this;return"data"===A?this._worker.on(A,(function(A){e.call(t,A.data,A.meta)})):this._worker.on(A,(function(){r.delay(e,arguments,t)})),this},resume:function(){return r.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(A){if(r.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw new Error(this._outputType+" is not supported by this method");return new B(this,{objectMode:"nodebuffer"!==this._outputType},A)}},e.exports=u},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(A,e,t){if(t.base64=!0,t.array=!0,t.string=!0,t.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,t.nodebuffer="undefined"!=typeof Buffer,t.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)t.blob=!1;else{var r=new ArrayBuffer(0);try{t.blob=0===new Blob([r],{type:"application/zip"}).size}catch(s){try{var n=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);n.append(r),t.blob=0===n.getBlob("application/zip").size}catch(i){t.blob=!1}}}try{t.nodestream=!!A("readable-stream").Readable}catch(s){t.nodestream=!1}},{"readable-stream":16}],31:[function(A,e,t){for(var r=A("./utils"),n=A("./support"),s=A("./nodejsUtils"),i=A("./stream/GenericWorker"),o=new Array(256),a=0;a<256;a++)o[a]=252<=a?6:248<=a?5:240<=a?4:224<=a?3:192<=a?2:1;function B(){i.call(this,"utf-8 decode"),this.leftOver=null}function c(){i.call(this,"utf-8 encode")}o[254]=o[254]=1,t.utf8encode=function(A){return n.nodebuffer?s.newBufferFrom(A,"utf-8"):function(A){var e,t,r,s,i,o=A.length,a=0;for(s=0;s<o;s++)55296==(64512&(t=A.charCodeAt(s)))&&s+1<o&&56320==(64512&(r=A.charCodeAt(s+1)))&&(t=65536+(t-55296<<10)+(r-56320),s++),a+=t<128?1:t<2048?2:t<65536?3:4;for(e=n.uint8array?new Uint8Array(a):new Array(a),s=i=0;i<a;s++)55296==(64512&(t=A.charCodeAt(s)))&&s+1<o&&56320==(64512&(r=A.charCodeAt(s+1)))&&(t=65536+(t-55296<<10)+(r-56320),s++),t<128?e[i++]=t:(t<2048?e[i++]=192|t>>>6:(t<65536?e[i++]=224|t>>>12:(e[i++]=240|t>>>18,e[i++]=128|t>>>12&63),e[i++]=128|t>>>6&63),e[i++]=128|63&t);return e}(A)},t.utf8decode=function(A){return n.nodebuffer?r.transformTo("nodebuffer",A).toString("utf-8"):function(A){var e,t,n,s,i=A.length,a=new Array(2*i);for(e=t=0;e<i;)if((n=A[e++])<128)a[t++]=n;else if(4<(s=o[n]))a[t++]=65533,e+=s-1;else{for(n&=2===s?31:3===s?15:7;1<s&&e<i;)n=n<<6|63&A[e++],s--;1<s?a[t++]=65533:n<65536?a[t++]=n:(n-=65536,a[t++]=55296|n>>10&1023,a[t++]=56320|1023&n)}return a.length!==t&&(a.subarray?a=a.subarray(0,t):a.length=t),r.applyFromCharCode(a)}(A=r.transformTo(n.uint8array?"uint8array":"array",A))},r.inherits(B,i),B.prototype.processChunk=function(A){var e=r.transformTo(n.uint8array?"uint8array":"array",A.data);if(this.leftOver&&this.leftOver.length){if(n.uint8array){var s=e;(e=new Uint8Array(s.length+this.leftOver.length)).set(this.leftOver,0),e.set(s,this.leftOver.length)}else e=this.leftOver.concat(e);this.leftOver=null}var i=function(A,e){var t;for((e=e||A.length)>A.length&&(e=A.length),t=e-1;0<=t&&128==(192&A[t]);)t--;return t<0||0===t?e:t+o[A[t]]>e?t:e}(e),a=e;i!==e.length&&(n.uint8array?(a=e.subarray(0,i),this.leftOver=e.subarray(i,e.length)):(a=e.slice(0,i),this.leftOver=e.slice(i,e.length))),this.push({data:t.utf8decode(a),meta:A.meta})},B.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:t.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},t.Utf8DecodeWorker=B,r.inherits(c,i),c.prototype.processChunk=function(A){this.push({data:t.utf8encode(A.data),meta:A.meta})},t.Utf8EncodeWorker=c},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(A,e,t){var r=A("./support"),n=A("./base64"),s=A("./nodejsUtils"),i=A("./external");function o(A){return A}function a(A,e){for(var t=0;t<A.length;++t)e[t]=255&A.charCodeAt(t);return e}A("setimmediate"),t.newBlob=function(A,e){t.checkSupport("blob");try{return new Blob([A],{type:e})}catch(n){try{var r=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return r.append(A),r.getBlob(e)}catch(s){throw new Error("Bug : can't construct the Blob.")}}};var B={stringifyByChunk:function(A,e,t){var r=[],n=0,s=A.length;if(s<=t)return String.fromCharCode.apply(null,A);for(;n<s;)"array"===e||"nodebuffer"===e?r.push(String.fromCharCode.apply(null,A.slice(n,Math.min(n+t,s)))):r.push(String.fromCharCode.apply(null,A.subarray(n,Math.min(n+t,s)))),n+=t;return r.join("")},stringifyByChar:function(A){for(var e="",t=0;t<A.length;t++)e+=String.fromCharCode(A[t]);return e},applyCanBeUsed:{uint8array:function(){try{return r.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(A){return!1}}(),nodebuffer:function(){try{return r.nodebuffer&&1===String.fromCharCode.apply(null,s.allocBuffer(1)).length}catch(A){return!1}}()}};function c(A){var e=65536,r=t.getTypeOf(A),n=!0;if("uint8array"===r?n=B.applyCanBeUsed.uint8array:"nodebuffer"===r&&(n=B.applyCanBeUsed.nodebuffer),n)for(;1<e;)try{return B.stringifyByChunk(A,r,e)}catch(s){e=Math.floor(e/2)}return B.stringifyByChar(A)}function u(A,e){for(var t=0;t<A.length;t++)e[t]=A[t];return e}t.applyFromCharCode=c;var l={};l.string={string:o,array:function(A){return a(A,new Array(A.length))},arraybuffer:function(A){return l.string.uint8array(A).buffer},uint8array:function(A){return a(A,new Uint8Array(A.length))},nodebuffer:function(A){return a(A,s.allocBuffer(A.length))}},l.array={string:c,array:o,arraybuffer:function(A){return new Uint8Array(A).buffer},uint8array:function(A){return new Uint8Array(A)},nodebuffer:function(A){return s.newBufferFrom(A)}},l.arraybuffer={string:function(A){return c(new Uint8Array(A))},array:function(A){return u(new Uint8Array(A),new Array(A.byteLength))},arraybuffer:o,uint8array:function(A){return new Uint8Array(A)},nodebuffer:function(A){return s.newBufferFrom(new Uint8Array(A))}},l.uint8array={string:c,array:function(A){return u(A,new Array(A.length))},arraybuffer:function(A){return A.buffer},uint8array:o,nodebuffer:function(A){return s.newBufferFrom(A)}},l.nodebuffer={string:c,array:function(A){return u(A,new Array(A.length))},arraybuffer:function(A){return l.nodebuffer.uint8array(A).buffer},uint8array:function(A){return u(A,new Uint8Array(A.length))},nodebuffer:o},t.transformTo=function(A,e){if(e=e||"",!A)return e;t.checkSupport(A);var r=t.getTypeOf(e);return l[r][A](e)},t.resolve=function(A){for(var e=A.split("/"),t=[],r=0;r<e.length;r++){var n=e[r];"."===n||""===n&&0!==r&&r!==e.length-1||(".."===n?t.pop():t.push(n))}return t.join("/")},t.getTypeOf=function(A){return"string"==typeof A?"string":"[object Array]"===Object.prototype.toString.call(A)?"array":r.nodebuffer&&s.isBuffer(A)?"nodebuffer":r.uint8array&&A instanceof Uint8Array?"uint8array":r.arraybuffer&&A instanceof ArrayBuffer?"arraybuffer":void 0},t.checkSupport=function(A){if(!r[A.toLowerCase()])throw new Error(A+" is not supported by this platform")},t.MAX_VALUE_16BITS=65535,t.MAX_VALUE_32BITS=-1,t.pretty=function(A){var e,t,r="";for(t=0;t<(A||"").length;t++)r+="\\x"+((e=A.charCodeAt(t))<16?"0":"")+e.toString(16).toUpperCase();return r},t.delay=function(A,e,t){setImmediate((function(){A.apply(t||null,e||[])}))},t.inherits=function(A,e){function t(){}t.prototype=e.prototype,A.prototype=new t},t.extend=function(){var A,e,t={};for(A=0;A<arguments.length;A++)for(e in arguments[A])Object.prototype.hasOwnProperty.call(arguments[A],e)&&void 0===t[e]&&(t[e]=arguments[A][e]);return t},t.prepareContent=function(A,e,s,o,B){return i.Promise.resolve(e).then((function(A){return r.blob&&(A instanceof Blob||-1!==["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(A)))&&"undefined"!=typeof FileReader?new i.Promise((function(e,t){var r=new FileReader;r.onload=function(A){e(A.target.result)},r.onerror=function(A){t(A.target.error)},r.readAsArrayBuffer(A)})):A})).then((function(e){var c,u=t.getTypeOf(e);return u?("arraybuffer"===u?e=t.transformTo("uint8array",e):"string"===u&&(B?e=n.decode(e):s&&!0!==o&&(e=a(c=e,r.uint8array?new Uint8Array(c.length):new Array(c.length)))),e):i.Promise.reject(new Error("Can't read the data of '"+A+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))}))}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,setimmediate:54}],33:[function(A,e,t){var r=A("./reader/readerFor"),n=A("./utils"),s=A("./signature"),i=A("./zipEntry"),o=A("./support");function a(A){this.files=[],this.loadOptions=A}a.prototype={checkSignature:function(A){if(!this.reader.readAndCheckSignature(A)){this.reader.index-=4;var e=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+n.pretty(e)+", expected "+n.pretty(A)+")")}},isSignature:function(A,e){var t=this.reader.index;this.reader.setIndex(A);var r=this.reader.readString(4)===e;return this.reader.setIndex(t),r},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var A=this.reader.readData(this.zipCommentLength),e=o.uint8array?"uint8array":"array",t=n.transformTo(e,A);this.zipComment=this.loadOptions.decodeFileName(t)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var A,e,t,r=this.zip64EndOfCentralSize-44;0<r;)A=this.reader.readInt(2),e=this.reader.readInt(4),t=this.reader.readData(e),this.zip64ExtensibleData[A]={id:A,length:e,value:t}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var A,e;for(A=0;A<this.files.length;A++)e=this.files[A],this.reader.setIndex(e.localHeaderOffset),this.checkSignature(s.LOCAL_FILE_HEADER),e.readLocalPart(this.reader),e.handleUTF8(),e.processAttributes()},readCentralDir:function(){var A;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(s.CENTRAL_FILE_HEADER);)(A=new i({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(A);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var A=this.reader.lastIndexOfSignature(s.CENTRAL_DIRECTORY_END);if(A<0)throw this.isSignature(0,s.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(A);var e=A;if(this.checkSignature(s.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===n.MAX_VALUE_16BITS||this.diskWithCentralDirStart===n.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===n.MAX_VALUE_16BITS||this.centralDirRecords===n.MAX_VALUE_16BITS||this.centralDirSize===n.MAX_VALUE_32BITS||this.centralDirOffset===n.MAX_VALUE_32BITS){if(this.zip64=!0,(A=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(A),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,s.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var t=this.centralDirOffset+this.centralDirSize;this.zip64&&(t+=20,t+=12+this.zip64EndOfCentralSize);var r=e-t;if(0<r)this.isSignature(e,s.CENTRAL_FILE_HEADER)||(this.reader.zero=r);else if(r<0)throw new Error("Corrupted zip: missing "+Math.abs(r)+" bytes.")},prepareReader:function(A){this.reader=r(A)},load:function(A){this.prepareReader(A),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},e.exports=a},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utils":32,"./zipEntry":34}],34:[function(A,e,t){var r=A("./reader/readerFor"),n=A("./utils"),s=A("./compressedObject"),i=A("./crc32"),o=A("./utf8"),a=A("./compressions"),B=A("./support");function c(A,e){this.options=A,this.loadOptions=e}c.prototype={isEncrypted:function(){return 1==(1&this.bitFlag)},useUTF8:function(){return 2048==(2048&this.bitFlag)},readLocalPart:function(A){var e,t;if(A.skip(22),this.fileNameLength=A.readInt(2),t=A.readInt(2),this.fileName=A.readData(this.fileNameLength),A.skip(t),-1===this.compressedSize||-1===this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(null===(e=function(A){for(var e in a)if(Object.prototype.hasOwnProperty.call(a,e)&&a[e].magic===A)return a[e];return null}(this.compressionMethod)))throw new Error("Corrupted zip : compression "+n.pretty(this.compressionMethod)+" unknown (inner file : "+n.transformTo("string",this.fileName)+")");this.decompressed=new s(this.compressedSize,this.uncompressedSize,this.crc32,e,A.readData(this.compressedSize))},readCentralPart:function(A){this.versionMadeBy=A.readInt(2),A.skip(2),this.bitFlag=A.readInt(2),this.compressionMethod=A.readString(2),this.date=A.readDate(),this.crc32=A.readInt(4),this.compressedSize=A.readInt(4),this.uncompressedSize=A.readInt(4);var e=A.readInt(2);if(this.extraFieldsLength=A.readInt(2),this.fileCommentLength=A.readInt(2),this.diskNumberStart=A.readInt(2),this.internalFileAttributes=A.readInt(2),this.externalFileAttributes=A.readInt(4),this.localHeaderOffset=A.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");A.skip(e),this.readExtraFields(A),this.parseZIP64ExtraField(A),this.fileComment=A.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var A=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0==A&&(this.dosPermissions=63&this.externalFileAttributes),3==A&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var A=r(this.extraFields[1].value);this.uncompressedSize===n.MAX_VALUE_32BITS&&(this.uncompressedSize=A.readInt(8)),this.compressedSize===n.MAX_VALUE_32BITS&&(this.compressedSize=A.readInt(8)),this.localHeaderOffset===n.MAX_VALUE_32BITS&&(this.localHeaderOffset=A.readInt(8)),this.diskNumberStart===n.MAX_VALUE_32BITS&&(this.diskNumberStart=A.readInt(4))}},readExtraFields:function(A){var e,t,r,n=A.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});A.index+4<n;)e=A.readInt(2),t=A.readInt(2),r=A.readData(t),this.extraFields[e]={id:e,length:t,value:r};A.setIndex(n)},handleUTF8:function(){var A=B.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=o.utf8decode(this.fileName),this.fileCommentStr=o.utf8decode(this.fileComment);else{var e=this.findExtraFieldUnicodePath();if(null!==e)this.fileNameStr=e;else{var t=n.transformTo(A,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(t)}var r=this.findExtraFieldUnicodeComment();if(null!==r)this.fileCommentStr=r;else{var s=n.transformTo(A,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(s)}}},findExtraFieldUnicodePath:function(){var A=this.extraFields[28789];if(A){var e=r(A.value);return 1!==e.readInt(1)||i(this.fileName)!==e.readInt(4)?null:o.utf8decode(e.readData(A.length-5))}return null},findExtraFieldUnicodeComment:function(){var A=this.extraFields[25461];if(A){var e=r(A.value);return 1!==e.readInt(1)||i(this.fileComment)!==e.readInt(4)?null:o.utf8decode(e.readData(A.length-5))}return null}},e.exports=c},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(A,e,t){function r(A,e,t){this.name=A,this.dir=t.dir,this.date=t.date,this.comment=t.comment,this.unixPermissions=t.unixPermissions,this.dosPermissions=t.dosPermissions,this._data=e,this._dataBinary=t.binary,this.options={compression:t.compression,compressionOptions:t.compressionOptions}}var n=A("./stream/StreamHelper"),s=A("./stream/DataWorker"),i=A("./utf8"),o=A("./compressedObject"),a=A("./stream/GenericWorker");r.prototype={internalStream:function(A){var e=null,t="string";try{if(!A)throw new Error("No output type specified.");var r="string"===(t=A.toLowerCase())||"text"===t;"binarystring"!==t&&"text"!==t||(t="string"),e=this._decompressWorker();var s=!this._dataBinary;s&&!r&&(e=e.pipe(new i.Utf8EncodeWorker)),!s&&r&&(e=e.pipe(new i.Utf8DecodeWorker))}catch(o){(e=new a("error")).error(o)}return new n(e,t,"")},async:function(A,e){return this.internalStream(A).accumulate(e)},nodeStream:function(A,e){return this.internalStream(A||"nodebuffer").toNodejsStream(e)},_compressWorker:function(A,e){if(this._data instanceof o&&this._data.compression.magic===A.magic)return this._data.getCompressedWorker();var t=this._decompressWorker();return this._dataBinary||(t=t.pipe(new i.Utf8EncodeWorker)),o.createWorkerFrom(t,A,e)},_decompressWorker:function(){return this._data instanceof o?this._data.getContentWorker():this._data instanceof a?this._data:new s(this._data)}};for(var B=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],c=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},u=0;u<B.length;u++)r.prototype[B[u]]=c;e.exports=r},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(e,t,r){(function(A){var e,r,n=A.MutationObserver||A.WebKitMutationObserver;if(n){var s=0,i=new n(c),o=A.document.createTextNode("");i.observe(o,{characterData:!0}),e=function(){o.data=s=++s%2}}else if(A.setImmediate||void 0===A.MessageChannel)e="document"in A&&"onreadystatechange"in A.document.createElement("script")?function(){var e=A.document.createElement("script");e.onreadystatechange=function(){c(),e.onreadystatechange=null,e.parentNode.removeChild(e),e=null},A.document.documentElement.appendChild(e)}:function(){setTimeout(c,0)};else{var a=new A.MessageChannel;a.port1.onmessage=c,e=function(){a.port2.postMessage(0)}}var B=[];function c(){var A,e;r=!0;for(var t=B.length;t;){for(e=B,B=[],A=-1;++A<t;)e[A]();t=B.length}r=!1}t.exports=function(A){1!==B.push(A)||r||e()}}).call(this,void 0!==A?A:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],37:[function(A,e,t){var r=A("immediate");function n(){}var s={},i=["REJECTED"],o=["FULFILLED"],a=["PENDING"];function B(A){if("function"!=typeof A)throw new TypeError("resolver must be a function");this.state=a,this.queue=[],this.outcome=void 0,A!==n&&h(this,A)}function c(A,e,t){this.promise=A,"function"==typeof e&&(this.onFulfilled=e,this.callFulfilled=this.otherCallFulfilled),"function"==typeof t&&(this.onRejected=t,this.callRejected=this.otherCallRejected)}function u(A,e,t){r((function(){var r;try{r=e(t)}catch(n){return s.reject(A,n)}r===A?s.reject(A,new TypeError("Cannot resolve promise with itself")):s.resolve(A,r)}))}function l(A){var e=A&&A.then;if(A&&("object"==typeof A||"function"==typeof A)&&"function"==typeof e)return function(){e.apply(A,arguments)}}function h(A,e){var t=!1;function r(e){t||(t=!0,s.reject(A,e))}function n(e){t||(t=!0,s.resolve(A,e))}var i=g((function(){e(n,r)}));"error"===i.status&&r(i.value)}function g(A,e){var t={};try{t.value=A(e),t.status="success"}catch(r){t.status="error",t.value=r}return t}(e.exports=B).prototype.finally=function(A){if("function"!=typeof A)return this;var e=this.constructor;return this.then((function(t){return e.resolve(A()).then((function(){return t}))}),(function(t){return e.resolve(A()).then((function(){throw t}))}))},B.prototype.catch=function(A){return this.then(null,A)},B.prototype.then=function(A,e){if("function"!=typeof A&&this.state===o||"function"!=typeof e&&this.state===i)return this;var t=new this.constructor(n);return this.state!==a?u(t,this.state===o?A:e,this.outcome):this.queue.push(new c(t,A,e)),t},c.prototype.callFulfilled=function(A){s.resolve(this.promise,A)},c.prototype.otherCallFulfilled=function(A){u(this.promise,this.onFulfilled,A)},c.prototype.callRejected=function(A){s.reject(this.promise,A)},c.prototype.otherCallRejected=function(A){u(this.promise,this.onRejected,A)},s.resolve=function(A,e){var t=g(l,e);if("error"===t.status)return s.reject(A,t.value);var r=t.value;if(r)h(A,r);else{A.state=o,A.outcome=e;for(var n=-1,i=A.queue.length;++n<i;)A.queue[n].callFulfilled(e)}return A},s.reject=function(A,e){A.state=i,A.outcome=e;for(var t=-1,r=A.queue.length;++t<r;)A.queue[t].callRejected(e);return A},B.resolve=function(A){return A instanceof this?A:s.resolve(new this(n),A)},B.reject=function(A){var e=new this(n);return s.reject(e,A)},B.all=function(A){var e=this;if("[object Array]"!==Object.prototype.toString.call(A))return this.reject(new TypeError("must be an array"));var t=A.length,r=!1;if(!t)return this.resolve([]);for(var i=new Array(t),o=0,a=-1,B=new this(n);++a<t;)c(A[a],a);return B;function c(A,n){e.resolve(A).then((function(A){i[n]=A,++o!==t||r||(r=!0,s.resolve(B,i))}),(function(A){r||(r=!0,s.reject(B,A))}))}},B.race=function(A){var e=this;if("[object Array]"!==Object.prototype.toString.call(A))return this.reject(new TypeError("must be an array"));var t=A.length,r=!1;if(!t)return this.resolve([]);for(var i,o=-1,a=new this(n);++o<t;)i=A[o],e.resolve(i).then((function(A){r||(r=!0,s.resolve(a,A))}),(function(A){r||(r=!0,s.reject(a,A))}));return a}},{immediate:36}],38:[function(A,e,t){var r={};(0,A("./lib/utils/common").assign)(r,A("./lib/deflate"),A("./lib/inflate"),A("./lib/zlib/constants")),e.exports=r},{"./lib/deflate":39,"./lib/inflate":40,"./lib/utils/common":41,"./lib/zlib/constants":44}],39:[function(A,e,t){var r=A("./zlib/deflate"),n=A("./utils/common"),s=A("./utils/strings"),i=A("./zlib/messages"),o=A("./zlib/zstream"),a=Object.prototype.toString,B=0,c=-1,u=0,l=8;function h(A){if(!(this instanceof h))return new h(A);this.options=n.assign({level:c,method:l,chunkSize:16384,windowBits:15,memLevel:8,strategy:u,to:""},A||{});var e=this.options;e.raw&&0<e.windowBits?e.windowBits=-e.windowBits:e.gzip&&0<e.windowBits&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new o,this.strm.avail_out=0;var t=r.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(t!==B)throw new Error(i[t]);if(e.header&&r.deflateSetHeader(this.strm,e.header),e.dictionary){var g;if(g="string"==typeof e.dictionary?s.string2buf(e.dictionary):"[object ArrayBuffer]"===a.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,(t=r.deflateSetDictionary(this.strm,g))!==B)throw new Error(i[t]);this._dict_set=!0}}function g(A,e){var t=new h(e);if(t.push(A,!0),t.err)throw t.msg||i[t.err];return t.result}h.prototype.push=function(A,e){var t,i,o=this.strm,c=this.options.chunkSize;if(this.ended)return!1;i=e===~~e?e:!0===e?4:0,"string"==typeof A?o.input=s.string2buf(A):"[object ArrayBuffer]"===a.call(A)?o.input=new Uint8Array(A):o.input=A,o.next_in=0,o.avail_in=o.input.length;do{if(0===o.avail_out&&(o.output=new n.Buf8(c),o.next_out=0,o.avail_out=c),1!==(t=r.deflate(o,i))&&t!==B)return this.onEnd(t),!(this.ended=!0);0!==o.avail_out&&(0!==o.avail_in||4!==i&&2!==i)||("string"===this.options.to?this.onData(s.buf2binstring(n.shrinkBuf(o.output,o.next_out))):this.onData(n.shrinkBuf(o.output,o.next_out)))}while((0<o.avail_in||0===o.avail_out)&&1!==t);return 4===i?(t=r.deflateEnd(this.strm),this.onEnd(t),this.ended=!0,t===B):2!==i||(this.onEnd(B),!(o.avail_out=0))},h.prototype.onData=function(A){this.chunks.push(A)},h.prototype.onEnd=function(A){A===B&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=n.flattenChunks(this.chunks)),this.chunks=[],this.err=A,this.msg=this.strm.msg},t.Deflate=h,t.deflate=g,t.deflateRaw=function(A,e){return(e=e||{}).raw=!0,g(A,e)},t.gzip=function(A,e){return(e=e||{}).gzip=!0,g(A,e)}},{"./utils/common":41,"./utils/strings":42,"./zlib/deflate":46,"./zlib/messages":51,"./zlib/zstream":53}],40:[function(A,e,t){var r=A("./zlib/inflate"),n=A("./utils/common"),s=A("./utils/strings"),i=A("./zlib/constants"),o=A("./zlib/messages"),a=A("./zlib/zstream"),B=A("./zlib/gzheader"),c=Object.prototype.toString;function u(A){if(!(this instanceof u))return new u(A);this.options=n.assign({chunkSize:16384,windowBits:0,to:""},A||{});var e=this.options;e.raw&&0<=e.windowBits&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),!(0<=e.windowBits&&e.windowBits<16)||A&&A.windowBits||(e.windowBits+=32),15<e.windowBits&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new a,this.strm.avail_out=0;var t=r.inflateInit2(this.strm,e.windowBits);if(t!==i.Z_OK)throw new Error(o[t]);this.header=new B,r.inflateGetHeader(this.strm,this.header)}function l(A,e){var t=new u(e);if(t.push(A,!0),t.err)throw t.msg||o[t.err];return t.result}u.prototype.push=function(A,e){var t,o,a,B,u,l,h=this.strm,g=this.options.chunkSize,w=this.options.dictionary,d=!1;if(this.ended)return!1;o=e===~~e?e:!0===e?i.Z_FINISH:i.Z_NO_FLUSH,"string"==typeof A?h.input=s.binstring2buf(A):"[object ArrayBuffer]"===c.call(A)?h.input=new Uint8Array(A):h.input=A,h.next_in=0,h.avail_in=h.input.length;do{if(0===h.avail_out&&(h.output=new n.Buf8(g),h.next_out=0,h.avail_out=g),(t=r.inflate(h,i.Z_NO_FLUSH))===i.Z_NEED_DICT&&w&&(l="string"==typeof w?s.string2buf(w):"[object ArrayBuffer]"===c.call(w)?new Uint8Array(w):w,t=r.inflateSetDictionary(this.strm,l)),t===i.Z_BUF_ERROR&&!0===d&&(t=i.Z_OK,d=!1),t!==i.Z_STREAM_END&&t!==i.Z_OK)return this.onEnd(t),!(this.ended=!0);h.next_out&&(0!==h.avail_out&&t!==i.Z_STREAM_END&&(0!==h.avail_in||o!==i.Z_FINISH&&o!==i.Z_SYNC_FLUSH)||("string"===this.options.to?(a=s.utf8border(h.output,h.next_out),B=h.next_out-a,u=s.buf2string(h.output,a),h.next_out=B,h.avail_out=g-B,B&&n.arraySet(h.output,h.output,a,B,0),this.onData(u)):this.onData(n.shrinkBuf(h.output,h.next_out)))),0===h.avail_in&&0===h.avail_out&&(d=!0)}while((0<h.avail_in||0===h.avail_out)&&t!==i.Z_STREAM_END);return t===i.Z_STREAM_END&&(o=i.Z_FINISH),o===i.Z_FINISH?(t=r.inflateEnd(this.strm),this.onEnd(t),this.ended=!0,t===i.Z_OK):o!==i.Z_SYNC_FLUSH||(this.onEnd(i.Z_OK),!(h.avail_out=0))},u.prototype.onData=function(A){this.chunks.push(A)},u.prototype.onEnd=function(A){A===i.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=n.flattenChunks(this.chunks)),this.chunks=[],this.err=A,this.msg=this.strm.msg},t.Inflate=u,t.inflate=l,t.inflateRaw=function(A,e){return(e=e||{}).raw=!0,l(A,e)},t.ungzip=l},{"./utils/common":41,"./utils/strings":42,"./zlib/constants":44,"./zlib/gzheader":47,"./zlib/inflate":49,"./zlib/messages":51,"./zlib/zstream":53}],41:[function(A,e,t){var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;t.assign=function(A){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var t=e.shift();if(t){if("object"!=typeof t)throw new TypeError(t+"must be non-object");for(var r in t)t.hasOwnProperty(r)&&(A[r]=t[r])}}return A},t.shrinkBuf=function(A,e){return A.length===e?A:A.subarray?A.subarray(0,e):(A.length=e,A)};var n={arraySet:function(A,e,t,r,n){if(e.subarray&&A.subarray)A.set(e.subarray(t,t+r),n);else for(var s=0;s<r;s++)A[n+s]=e[t+s]},flattenChunks:function(A){var e,t,r,n,s,i;for(e=r=0,t=A.length;e<t;e++)r+=A[e].length;for(i=new Uint8Array(r),e=n=0,t=A.length;e<t;e++)s=A[e],i.set(s,n),n+=s.length;return i}},s={arraySet:function(A,e,t,r,n){for(var s=0;s<r;s++)A[n+s]=e[t+s]},flattenChunks:function(A){return[].concat.apply([],A)}};t.setTyped=function(A){A?(t.Buf8=Uint8Array,t.Buf16=Uint16Array,t.Buf32=Int32Array,t.assign(t,n)):(t.Buf8=Array,t.Buf16=Array,t.Buf32=Array,t.assign(t,s))},t.setTyped(r)},{}],42:[function(A,e,t){var r=A("./common"),n=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(B){n=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(B){s=!1}for(var i=new r.Buf8(256),o=0;o<256;o++)i[o]=252<=o?6:248<=o?5:240<=o?4:224<=o?3:192<=o?2:1;function a(A,e){if(e<65537&&(A.subarray&&s||!A.subarray&&n))return String.fromCharCode.apply(null,r.shrinkBuf(A,e));for(var t="",i=0;i<e;i++)t+=String.fromCharCode(A[i]);return t}i[254]=i[254]=1,t.string2buf=function(A){var e,t,n,s,i,o=A.length,a=0;for(s=0;s<o;s++)55296==(64512&(t=A.charCodeAt(s)))&&s+1<o&&56320==(64512&(n=A.charCodeAt(s+1)))&&(t=65536+(t-55296<<10)+(n-56320),s++),a+=t<128?1:t<2048?2:t<65536?3:4;for(e=new r.Buf8(a),s=i=0;i<a;s++)55296==(64512&(t=A.charCodeAt(s)))&&s+1<o&&56320==(64512&(n=A.charCodeAt(s+1)))&&(t=65536+(t-55296<<10)+(n-56320),s++),t<128?e[i++]=t:(t<2048?e[i++]=192|t>>>6:(t<65536?e[i++]=224|t>>>12:(e[i++]=240|t>>>18,e[i++]=128|t>>>12&63),e[i++]=128|t>>>6&63),e[i++]=128|63&t);return e},t.buf2binstring=function(A){return a(A,A.length)},t.binstring2buf=function(A){for(var e=new r.Buf8(A.length),t=0,n=e.length;t<n;t++)e[t]=A.charCodeAt(t);return e},t.buf2string=function(A,e){var t,r,n,s,o=e||A.length,B=new Array(2*o);for(t=r=0;t<o;)if((n=A[t++])<128)B[r++]=n;else if(4<(s=i[n]))B[r++]=65533,t+=s-1;else{for(n&=2===s?31:3===s?15:7;1<s&&t<o;)n=n<<6|63&A[t++],s--;1<s?B[r++]=65533:n<65536?B[r++]=n:(n-=65536,B[r++]=55296|n>>10&1023,B[r++]=56320|1023&n)}return a(B,r)},t.utf8border=function(A,e){var t;for((e=e||A.length)>A.length&&(e=A.length),t=e-1;0<=t&&128==(192&A[t]);)t--;return t<0||0===t?e:t+i[A[t]]>e?t:e}},{"./common":41}],43:[function(A,e,t){e.exports=function(A,e,t,r){for(var n=65535&A|0,s=A>>>16&65535|0,i=0;0!==t;){for(t-=i=2e3<t?2e3:t;s=s+(n=n+e[r++]|0)|0,--i;);n%=65521,s%=65521}return n|s<<16|0}},{}],44:[function(A,e,t){e.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(A,e,t){var r=function(){for(var A,e=[],t=0;t<256;t++){A=t;for(var r=0;r<8;r++)A=1&A?3988292384^A>>>1:A>>>1;e[t]=A}return e}();e.exports=function(A,e,t,n){var s=r,i=n+t;A^=-1;for(var o=n;o<i;o++)A=A>>>8^s[255&(A^e[o])];return-1^A}},{}],46:[function(A,e,t){var r,n=A("../utils/common"),s=A("./trees"),i=A("./adler32"),o=A("./crc32"),a=A("./messages"),B=0,c=4,u=0,l=-2,h=-1,g=4,w=2,d=8,f=9,Q=286,C=30,U=19,p=2*Q+1,F=15,m=3,y=258,H=y+m+1,E=42,I=113,b=1,v=2,K=3,L=4;function _(A,e){return A.msg=a[e],e}function x(A){return(A<<1)-(4<A?9:0)}function D(A){for(var e=A.length;0<=--e;)A[e]=0}function k(A){var e=A.state,t=e.pending;t>A.avail_out&&(t=A.avail_out),0!==t&&(n.arraySet(A.output,e.pending_buf,e.pending_out,t,A.next_out),A.next_out+=t,e.pending_out+=t,A.total_out+=t,A.avail_out-=t,e.pending-=t,0===e.pending&&(e.pending_out=0))}function S(A,e){s._tr_flush_block(A,0<=A.block_start?A.block_start:-1,A.strstart-A.block_start,e),A.block_start=A.strstart,k(A.strm)}function O(A,e){A.pending_buf[A.pending++]=e}function T(A,e){A.pending_buf[A.pending++]=e>>>8&255,A.pending_buf[A.pending++]=255&e}function M(A,e){var t,r,n=A.max_chain_length,s=A.strstart,i=A.prev_length,o=A.nice_match,a=A.strstart>A.w_size-H?A.strstart-(A.w_size-H):0,B=A.window,c=A.w_mask,u=A.prev,l=A.strstart+y,h=B[s+i-1],g=B[s+i];A.prev_length>=A.good_match&&(n>>=2),o>A.lookahead&&(o=A.lookahead);do{if(B[(t=e)+i]===g&&B[t+i-1]===h&&B[t]===B[s]&&B[++t]===B[s+1]){s+=2,t++;do{}while(B[++s]===B[++t]&&B[++s]===B[++t]&&B[++s]===B[++t]&&B[++s]===B[++t]&&B[++s]===B[++t]&&B[++s]===B[++t]&&B[++s]===B[++t]&&B[++s]===B[++t]&&s<l);if(r=y-(l-s),s=l-y,i<r){if(A.match_start=e,o<=(i=r))break;h=B[s+i-1],g=B[s+i]}}}while((e=u[e&c])>a&&0!=--n);return i<=A.lookahead?i:A.lookahead}function R(A){var e,t,r,s,a,B,c,u,l,h,g=A.w_size;do{if(s=A.window_size-A.lookahead-A.strstart,A.strstart>=g+(g-H)){for(n.arraySet(A.window,A.window,g,g,0),A.match_start-=g,A.strstart-=g,A.block_start-=g,e=t=A.hash_size;r=A.head[--e],A.head[e]=g<=r?r-g:0,--t;);for(e=t=g;r=A.prev[--e],A.prev[e]=g<=r?r-g:0,--t;);s+=g}if(0===A.strm.avail_in)break;if(B=A.strm,c=A.window,u=A.strstart+A.lookahead,h=void 0,(l=s)<(h=B.avail_in)&&(h=l),t=0===h?0:(B.avail_in-=h,n.arraySet(c,B.input,B.next_in,h,u),1===B.state.wrap?B.adler=i(B.adler,c,h,u):2===B.state.wrap&&(B.adler=o(B.adler,c,h,u)),B.next_in+=h,B.total_in+=h,h),A.lookahead+=t,A.lookahead+A.insert>=m)for(a=A.strstart-A.insert,A.ins_h=A.window[a],A.ins_h=(A.ins_h<<A.hash_shift^A.window[a+1])&A.hash_mask;A.insert&&(A.ins_h=(A.ins_h<<A.hash_shift^A.window[a+m-1])&A.hash_mask,A.prev[a&A.w_mask]=A.head[A.ins_h],A.head[A.ins_h]=a,a++,A.insert--,!(A.lookahead+A.insert<m)););}while(A.lookahead<H&&0!==A.strm.avail_in)}function G(A,e){for(var t,r;;){if(A.lookahead<H){if(R(A),A.lookahead<H&&e===B)return b;if(0===A.lookahead)break}if(t=0,A.lookahead>=m&&(A.ins_h=(A.ins_h<<A.hash_shift^A.window[A.strstart+m-1])&A.hash_mask,t=A.prev[A.strstart&A.w_mask]=A.head[A.ins_h],A.head[A.ins_h]=A.strstart),0!==t&&A.strstart-t<=A.w_size-H&&(A.match_length=M(A,t)),A.match_length>=m)if(r=s._tr_tally(A,A.strstart-A.match_start,A.match_length-m),A.lookahead-=A.match_length,A.match_length<=A.max_lazy_match&&A.lookahead>=m){for(A.match_length--;A.strstart++,A.ins_h=(A.ins_h<<A.hash_shift^A.window[A.strstart+m-1])&A.hash_mask,t=A.prev[A.strstart&A.w_mask]=A.head[A.ins_h],A.head[A.ins_h]=A.strstart,0!=--A.match_length;);A.strstart++}else A.strstart+=A.match_length,A.match_length=0,A.ins_h=A.window[A.strstart],A.ins_h=(A.ins_h<<A.hash_shift^A.window[A.strstart+1])&A.hash_mask;else r=s._tr_tally(A,0,A.window[A.strstart]),A.lookahead--,A.strstart++;if(r&&(S(A,!1),0===A.strm.avail_out))return b}return A.insert=A.strstart<m-1?A.strstart:m-1,e===c?(S(A,!0),0===A.strm.avail_out?K:L):A.last_lit&&(S(A,!1),0===A.strm.avail_out)?b:v}function V(A,e){for(var t,r,n;;){if(A.lookahead<H){if(R(A),A.lookahead<H&&e===B)return b;if(0===A.lookahead)break}if(t=0,A.lookahead>=m&&(A.ins_h=(A.ins_h<<A.hash_shift^A.window[A.strstart+m-1])&A.hash_mask,t=A.prev[A.strstart&A.w_mask]=A.head[A.ins_h],A.head[A.ins_h]=A.strstart),A.prev_length=A.match_length,A.prev_match=A.match_start,A.match_length=m-1,0!==t&&A.prev_length<A.max_lazy_match&&A.strstart-t<=A.w_size-H&&(A.match_length=M(A,t),A.match_length<=5&&(1===A.strategy||A.match_length===m&&4096<A.strstart-A.match_start)&&(A.match_length=m-1)),A.prev_length>=m&&A.match_length<=A.prev_length){for(n=A.strstart+A.lookahead-m,r=s._tr_tally(A,A.strstart-1-A.prev_match,A.prev_length-m),A.lookahead-=A.prev_length-1,A.prev_length-=2;++A.strstart<=n&&(A.ins_h=(A.ins_h<<A.hash_shift^A.window[A.strstart+m-1])&A.hash_mask,t=A.prev[A.strstart&A.w_mask]=A.head[A.ins_h],A.head[A.ins_h]=A.strstart),0!=--A.prev_length;);if(A.match_available=0,A.match_length=m-1,A.strstart++,r&&(S(A,!1),0===A.strm.avail_out))return b}else if(A.match_available){if((r=s._tr_tally(A,0,A.window[A.strstart-1]))&&S(A,!1),A.strstart++,A.lookahead--,0===A.strm.avail_out)return b}else A.match_available=1,A.strstart++,A.lookahead--}return A.match_available&&(r=s._tr_tally(A,0,A.window[A.strstart-1]),A.match_available=0),A.insert=A.strstart<m-1?A.strstart:m-1,e===c?(S(A,!0),0===A.strm.avail_out?K:L):A.last_lit&&(S(A,!1),0===A.strm.avail_out)?b:v}function N(A,e,t,r,n){this.good_length=A,this.max_lazy=e,this.nice_length=t,this.max_chain=r,this.func=n}function P(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=d,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new n.Buf16(2*p),this.dyn_dtree=new n.Buf16(2*(2*C+1)),this.bl_tree=new n.Buf16(2*(2*U+1)),D(this.dyn_ltree),D(this.dyn_dtree),D(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new n.Buf16(F+1),this.heap=new n.Buf16(2*Q+1),D(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new n.Buf16(2*Q+1),D(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function X(A){var e;return A&&A.state?(A.total_in=A.total_out=0,A.data_type=w,(e=A.state).pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?E:I,A.adler=2===e.wrap?0:1,e.last_flush=B,s._tr_init(e),u):_(A,l)}function J(A){var e,t=X(A);return t===u&&((e=A.state).window_size=2*e.w_size,D(e.head),e.max_lazy_match=r[e.level].max_lazy,e.good_match=r[e.level].good_length,e.nice_match=r[e.level].nice_length,e.max_chain_length=r[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=m-1,e.match_available=0,e.ins_h=0),t}function W(A,e,t,r,s,i){if(!A)return l;var o=1;if(e===h&&(e=6),r<0?(o=0,r=-r):15<r&&(o=2,r-=16),s<1||f<s||t!==d||r<8||15<r||e<0||9<e||i<0||g<i)return _(A,l);8===r&&(r=9);var a=new P;return(A.state=a).strm=A,a.wrap=o,a.gzhead=null,a.w_bits=r,a.w_size=1<<a.w_bits,a.w_mask=a.w_size-1,a.hash_bits=s+7,a.hash_size=1<<a.hash_bits,a.hash_mask=a.hash_size-1,a.hash_shift=~~((a.hash_bits+m-1)/m),a.window=new n.Buf8(2*a.w_size),a.head=new n.Buf16(a.hash_size),a.prev=new n.Buf16(a.w_size),a.lit_bufsize=1<<s+6,a.pending_buf_size=4*a.lit_bufsize,a.pending_buf=new n.Buf8(a.pending_buf_size),a.d_buf=1*a.lit_bufsize,a.l_buf=3*a.lit_bufsize,a.level=e,a.strategy=i,a.method=t,J(A)}r=[new N(0,0,0,0,(function(A,e){var t=65535;for(t>A.pending_buf_size-5&&(t=A.pending_buf_size-5);;){if(A.lookahead<=1){if(R(A),0===A.lookahead&&e===B)return b;if(0===A.lookahead)break}A.strstart+=A.lookahead,A.lookahead=0;var r=A.block_start+t;if((0===A.strstart||A.strstart>=r)&&(A.lookahead=A.strstart-r,A.strstart=r,S(A,!1),0===A.strm.avail_out))return b;if(A.strstart-A.block_start>=A.w_size-H&&(S(A,!1),0===A.strm.avail_out))return b}return A.insert=0,e===c?(S(A,!0),0===A.strm.avail_out?K:L):(A.strstart>A.block_start&&(S(A,!1),A.strm.avail_out),b)})),new N(4,4,8,4,G),new N(4,5,16,8,G),new N(4,6,32,32,G),new N(4,4,16,16,V),new N(8,16,32,32,V),new N(8,16,128,128,V),new N(8,32,128,256,V),new N(32,128,258,1024,V),new N(32,258,258,4096,V)],t.deflateInit=function(A,e){return W(A,e,d,15,8,0)},t.deflateInit2=W,t.deflateReset=J,t.deflateResetKeep=X,t.deflateSetHeader=function(A,e){return A&&A.state?2!==A.state.wrap?l:(A.state.gzhead=e,u):l},t.deflate=function(A,e){var t,n,i,a;if(!A||!A.state||5<e||e<0)return A?_(A,l):l;if(n=A.state,!A.output||!A.input&&0!==A.avail_in||666===n.status&&e!==c)return _(A,0===A.avail_out?-5:l);if(n.strm=A,t=n.last_flush,n.last_flush=e,n.status===E)if(2===n.wrap)A.adler=0,O(n,31),O(n,139),O(n,8),n.gzhead?(O(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),O(n,255&n.gzhead.time),O(n,n.gzhead.time>>8&255),O(n,n.gzhead.time>>16&255),O(n,n.gzhead.time>>24&255),O(n,9===n.level?2:2<=n.strategy||n.level<2?4:0),O(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(O(n,255&n.gzhead.extra.length),O(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(A.adler=o(A.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69):(O(n,0),O(n,0),O(n,0),O(n,0),O(n,0),O(n,9===n.level?2:2<=n.strategy||n.level<2?4:0),O(n,3),n.status=I);else{var h=d+(n.w_bits-8<<4)<<8;h|=(2<=n.strategy||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(h|=32),h+=31-h%31,n.status=I,T(n,h),0!==n.strstart&&(T(n,A.adler>>>16),T(n,65535&A.adler)),A.adler=1}if(69===n.status)if(n.gzhead.extra){for(i=n.pending;n.gzindex<(65535&n.gzhead.extra.length)&&(n.pending!==n.pending_buf_size||(n.gzhead.hcrc&&n.pending>i&&(A.adler=o(A.adler,n.pending_buf,n.pending-i,i)),k(A),i=n.pending,n.pending!==n.pending_buf_size));)O(n,255&n.gzhead.extra[n.gzindex]),n.gzindex++;n.gzhead.hcrc&&n.pending>i&&(A.adler=o(A.adler,n.pending_buf,n.pending-i,i)),n.gzindex===n.gzhead.extra.length&&(n.gzindex=0,n.status=73)}else n.status=73;if(73===n.status)if(n.gzhead.name){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(A.adler=o(A.adler,n.pending_buf,n.pending-i,i)),k(A),i=n.pending,n.pending===n.pending_buf_size)){a=1;break}a=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,O(n,a)}while(0!==a);n.gzhead.hcrc&&n.pending>i&&(A.adler=o(A.adler,n.pending_buf,n.pending-i,i)),0===a&&(n.gzindex=0,n.status=91)}else n.status=91;if(91===n.status)if(n.gzhead.comment){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(A.adler=o(A.adler,n.pending_buf,n.pending-i,i)),k(A),i=n.pending,n.pending===n.pending_buf_size)){a=1;break}a=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,O(n,a)}while(0!==a);n.gzhead.hcrc&&n.pending>i&&(A.adler=o(A.adler,n.pending_buf,n.pending-i,i)),0===a&&(n.status=103)}else n.status=103;if(103===n.status&&(n.gzhead.hcrc?(n.pending+2>n.pending_buf_size&&k(A),n.pending+2<=n.pending_buf_size&&(O(n,255&A.adler),O(n,A.adler>>8&255),A.adler=0,n.status=I)):n.status=I),0!==n.pending){if(k(A),0===A.avail_out)return n.last_flush=-1,u}else if(0===A.avail_in&&x(e)<=x(t)&&e!==c)return _(A,-5);if(666===n.status&&0!==A.avail_in)return _(A,-5);if(0!==A.avail_in||0!==n.lookahead||e!==B&&666!==n.status){var g=2===n.strategy?function(A,e){for(var t;;){if(0===A.lookahead&&(R(A),0===A.lookahead)){if(e===B)return b;break}if(A.match_length=0,t=s._tr_tally(A,0,A.window[A.strstart]),A.lookahead--,A.strstart++,t&&(S(A,!1),0===A.strm.avail_out))return b}return A.insert=0,e===c?(S(A,!0),0===A.strm.avail_out?K:L):A.last_lit&&(S(A,!1),0===A.strm.avail_out)?b:v}(n,e):3===n.strategy?function(A,e){for(var t,r,n,i,o=A.window;;){if(A.lookahead<=y){if(R(A),A.lookahead<=y&&e===B)return b;if(0===A.lookahead)break}if(A.match_length=0,A.lookahead>=m&&0<A.strstart&&(r=o[n=A.strstart-1])===o[++n]&&r===o[++n]&&r===o[++n]){i=A.strstart+y;do{}while(r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&n<i);A.match_length=y-(i-n),A.match_length>A.lookahead&&(A.match_length=A.lookahead)}if(A.match_length>=m?(t=s._tr_tally(A,1,A.match_length-m),A.lookahead-=A.match_length,A.strstart+=A.match_length,A.match_length=0):(t=s._tr_tally(A,0,A.window[A.strstart]),A.lookahead--,A.strstart++),t&&(S(A,!1),0===A.strm.avail_out))return b}return A.insert=0,e===c?(S(A,!0),0===A.strm.avail_out?K:L):A.last_lit&&(S(A,!1),0===A.strm.avail_out)?b:v}(n,e):r[n.level].func(n,e);if(g!==K&&g!==L||(n.status=666),g===b||g===K)return 0===A.avail_out&&(n.last_flush=-1),u;if(g===v&&(1===e?s._tr_align(n):5!==e&&(s._tr_stored_block(n,0,0,!1),3===e&&(D(n.head),0===n.lookahead&&(n.strstart=0,n.block_start=0,n.insert=0))),k(A),0===A.avail_out))return n.last_flush=-1,u}return e!==c?u:n.wrap<=0?1:(2===n.wrap?(O(n,255&A.adler),O(n,A.adler>>8&255),O(n,A.adler>>16&255),O(n,A.adler>>24&255),O(n,255&A.total_in),O(n,A.total_in>>8&255),O(n,A.total_in>>16&255),O(n,A.total_in>>24&255)):(T(n,A.adler>>>16),T(n,65535&A.adler)),k(A),0<n.wrap&&(n.wrap=-n.wrap),0!==n.pending?u:1)},t.deflateEnd=function(A){var e;return A&&A.state?(e=A.state.status)!==E&&69!==e&&73!==e&&91!==e&&103!==e&&e!==I&&666!==e?_(A,l):(A.state=null,e===I?_(A,-3):u):l},t.deflateSetDictionary=function(A,e){var t,r,s,o,a,B,c,h,g=e.length;if(!A||!A.state)return l;if(2===(o=(t=A.state).wrap)||1===o&&t.status!==E||t.lookahead)return l;for(1===o&&(A.adler=i(A.adler,e,g,0)),t.wrap=0,g>=t.w_size&&(0===o&&(D(t.head),t.strstart=0,t.block_start=0,t.insert=0),h=new n.Buf8(t.w_size),n.arraySet(h,e,g-t.w_size,t.w_size,0),e=h,g=t.w_size),a=A.avail_in,B=A.next_in,c=A.input,A.avail_in=g,A.next_in=0,A.input=e,R(t);t.lookahead>=m;){for(r=t.strstart,s=t.lookahead-(m-1);t.ins_h=(t.ins_h<<t.hash_shift^t.window[r+m-1])&t.hash_mask,t.prev[r&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=r,r++,--s;);t.strstart=r,t.lookahead=m-1,R(t)}return t.strstart+=t.lookahead,t.block_start=t.strstart,t.insert=t.lookahead,t.lookahead=0,t.match_length=t.prev_length=m-1,t.match_available=0,A.next_in=B,A.input=c,A.avail_in=a,t.wrap=o,u},t.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./messages":51,"./trees":52}],47:[function(A,e,t){e.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],48:[function(A,e,t){e.exports=function(A,e){var t,r,n,s,i,o,a,B,c,u,l,h,g,w,d,f,Q,C,U,p,F,m,y,H,E;t=A.state,r=A.next_in,H=A.input,n=r+(A.avail_in-5),s=A.next_out,E=A.output,i=s-(e-A.avail_out),o=s+(A.avail_out-257),a=t.dmax,B=t.wsize,c=t.whave,u=t.wnext,l=t.window,h=t.hold,g=t.bits,w=t.lencode,d=t.distcode,f=(1<<t.lenbits)-1,Q=(1<<t.distbits)-1;A:do{g<15&&(h+=H[r++]<<g,g+=8,h+=H[r++]<<g,g+=8),C=w[h&f];e:for(;;){if(h>>>=U=C>>>24,g-=U,0==(U=C>>>16&255))E[s++]=65535&C;else{if(!(16&U)){if(0==(64&U)){C=w[(65535&C)+(h&(1<<U)-1)];continue e}if(32&U){t.mode=12;break A}A.msg="invalid literal/length code",t.mode=30;break A}p=65535&C,(U&=15)&&(g<U&&(h+=H[r++]<<g,g+=8),p+=h&(1<<U)-1,h>>>=U,g-=U),g<15&&(h+=H[r++]<<g,g+=8,h+=H[r++]<<g,g+=8),C=d[h&Q];t:for(;;){if(h>>>=U=C>>>24,g-=U,!(16&(U=C>>>16&255))){if(0==(64&U)){C=d[(65535&C)+(h&(1<<U)-1)];continue t}A.msg="invalid distance code",t.mode=30;break A}if(F=65535&C,g<(U&=15)&&(h+=H[r++]<<g,(g+=8)<U&&(h+=H[r++]<<g,g+=8)),a<(F+=h&(1<<U)-1)){A.msg="invalid distance too far back",t.mode=30;break A}if(h>>>=U,g-=U,(U=s-i)<F){if(c<(U=F-U)&&t.sane){A.msg="invalid distance too far back",t.mode=30;break A}if(y=l,(m=0)===u){if(m+=B-U,U<p){for(p-=U;E[s++]=l[m++],--U;);m=s-F,y=E}}else if(u<U){if(m+=B+u-U,(U-=u)<p){for(p-=U;E[s++]=l[m++],--U;);if(m=0,u<p){for(p-=U=u;E[s++]=l[m++],--U;);m=s-F,y=E}}}else if(m+=u-U,U<p){for(p-=U;E[s++]=l[m++],--U;);m=s-F,y=E}for(;2<p;)E[s++]=y[m++],E[s++]=y[m++],E[s++]=y[m++],p-=3;p&&(E[s++]=y[m++],1<p&&(E[s++]=y[m++]))}else{for(m=s-F;E[s++]=E[m++],E[s++]=E[m++],E[s++]=E[m++],2<(p-=3););p&&(E[s++]=E[m++],1<p&&(E[s++]=E[m++]))}break}}break}}while(r<n&&s<o);r-=p=g>>3,h&=(1<<(g-=p<<3))-1,A.next_in=r,A.next_out=s,A.avail_in=r<n?n-r+5:5-(r-n),A.avail_out=s<o?o-s+257:257-(s-o),t.hold=h,t.bits=g}},{}],49:[function(A,e,t){var r=A("../utils/common"),n=A("./adler32"),s=A("./crc32"),i=A("./inffast"),o=A("./inftrees"),a=1,B=2,c=0,u=-2,l=1,h=852,g=592;function w(A){return(A>>>24&255)+(A>>>8&65280)+((65280&A)<<8)+((255&A)<<24)}function d(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new r.Buf16(320),this.work=new r.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function f(A){var e;return A&&A.state?(e=A.state,A.total_in=A.total_out=e.total=0,A.msg="",e.wrap&&(A.adler=1&e.wrap),e.mode=l,e.last=0,e.havedict=0,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new r.Buf32(h),e.distcode=e.distdyn=new r.Buf32(g),e.sane=1,e.back=-1,c):u}function Q(A){var e;return A&&A.state?((e=A.state).wsize=0,e.whave=0,e.wnext=0,f(A)):u}function C(A,e){var t,r;return A&&A.state?(r=A.state,e<0?(t=0,e=-e):(t=1+(e>>4),e<48&&(e&=15)),e&&(e<8||15<e)?u:(null!==r.window&&r.wbits!==e&&(r.window=null),r.wrap=t,r.wbits=e,Q(A))):u}function U(A,e){var t,r;return A?(r=new d,(A.state=r).window=null,(t=C(A,e))!==c&&(A.state=null),t):u}var p,F,m=!0;function y(A){if(m){var e;for(p=new r.Buf32(512),F=new r.Buf32(32),e=0;e<144;)A.lens[e++]=8;for(;e<256;)A.lens[e++]=9;for(;e<280;)A.lens[e++]=7;for(;e<288;)A.lens[e++]=8;for(o(a,A.lens,0,288,p,0,A.work,{bits:9}),e=0;e<32;)A.lens[e++]=5;o(B,A.lens,0,32,F,0,A.work,{bits:5}),m=!1}A.lencode=p,A.lenbits=9,A.distcode=F,A.distbits=5}function H(A,e,t,n){var s,i=A.state;return null===i.window&&(i.wsize=1<<i.wbits,i.wnext=0,i.whave=0,i.window=new r.Buf8(i.wsize)),n>=i.wsize?(r.arraySet(i.window,e,t-i.wsize,i.wsize,0),i.wnext=0,i.whave=i.wsize):(n<(s=i.wsize-i.wnext)&&(s=n),r.arraySet(i.window,e,t-n,s,i.wnext),(n-=s)?(r.arraySet(i.window,e,t-n,n,0),i.wnext=n,i.whave=i.wsize):(i.wnext+=s,i.wnext===i.wsize&&(i.wnext=0),i.whave<i.wsize&&(i.whave+=s))),0}t.inflateReset=Q,t.inflateReset2=C,t.inflateResetKeep=f,t.inflateInit=function(A){return U(A,15)},t.inflateInit2=U,t.inflate=function(A,e){var t,h,g,d,f,Q,C,U,p,F,m,E,I,b,v,K,L,_,x,D,k,S,O,T,M=0,R=new r.Buf8(4),G=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!A||!A.state||!A.output||!A.input&&0!==A.avail_in)return u;12===(t=A.state).mode&&(t.mode=13),f=A.next_out,g=A.output,C=A.avail_out,d=A.next_in,h=A.input,Q=A.avail_in,U=t.hold,p=t.bits,F=Q,m=C,S=c;A:for(;;)switch(t.mode){case l:if(0===t.wrap){t.mode=13;break}for(;p<16;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}if(2&t.wrap&&35615===U){R[t.check=0]=255&U,R[1]=U>>>8&255,t.check=s(t.check,R,2,0),p=U=0,t.mode=2;break}if(t.flags=0,t.head&&(t.head.done=!1),!(1&t.wrap)||(((255&U)<<8)+(U>>8))%31){A.msg="incorrect header check",t.mode=30;break}if(8!=(15&U)){A.msg="unknown compression method",t.mode=30;break}if(p-=4,k=8+(15&(U>>>=4)),0===t.wbits)t.wbits=k;else if(k>t.wbits){A.msg="invalid window size",t.mode=30;break}t.dmax=1<<k,A.adler=t.check=1,t.mode=512&U?10:12,p=U=0;break;case 2:for(;p<16;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}if(t.flags=U,8!=(255&t.flags)){A.msg="unknown compression method",t.mode=30;break}if(57344&t.flags){A.msg="unknown header flags set",t.mode=30;break}t.head&&(t.head.text=U>>8&1),512&t.flags&&(R[0]=255&U,R[1]=U>>>8&255,t.check=s(t.check,R,2,0)),p=U=0,t.mode=3;case 3:for(;p<32;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}t.head&&(t.head.time=U),512&t.flags&&(R[0]=255&U,R[1]=U>>>8&255,R[2]=U>>>16&255,R[3]=U>>>24&255,t.check=s(t.check,R,4,0)),p=U=0,t.mode=4;case 4:for(;p<16;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}t.head&&(t.head.xflags=255&U,t.head.os=U>>8),512&t.flags&&(R[0]=255&U,R[1]=U>>>8&255,t.check=s(t.check,R,2,0)),p=U=0,t.mode=5;case 5:if(1024&t.flags){for(;p<16;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}t.length=U,t.head&&(t.head.extra_len=U),512&t.flags&&(R[0]=255&U,R[1]=U>>>8&255,t.check=s(t.check,R,2,0)),p=U=0}else t.head&&(t.head.extra=null);t.mode=6;case 6:if(1024&t.flags&&(Q<(E=t.length)&&(E=Q),E&&(t.head&&(k=t.head.extra_len-t.length,t.head.extra||(t.head.extra=new Array(t.head.extra_len)),r.arraySet(t.head.extra,h,d,E,k)),512&t.flags&&(t.check=s(t.check,h,E,d)),Q-=E,d+=E,t.length-=E),t.length))break A;t.length=0,t.mode=7;case 7:if(2048&t.flags){if(0===Q)break A;for(E=0;k=h[d+E++],t.head&&k&&t.length<65536&&(t.head.name+=String.fromCharCode(k)),k&&E<Q;);if(512&t.flags&&(t.check=s(t.check,h,E,d)),Q-=E,d+=E,k)break A}else t.head&&(t.head.name=null);t.length=0,t.mode=8;case 8:if(4096&t.flags){if(0===Q)break A;for(E=0;k=h[d+E++],t.head&&k&&t.length<65536&&(t.head.comment+=String.fromCharCode(k)),k&&E<Q;);if(512&t.flags&&(t.check=s(t.check,h,E,d)),Q-=E,d+=E,k)break A}else t.head&&(t.head.comment=null);t.mode=9;case 9:if(512&t.flags){for(;p<16;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}if(U!==(65535&t.check)){A.msg="header crc mismatch",t.mode=30;break}p=U=0}t.head&&(t.head.hcrc=t.flags>>9&1,t.head.done=!0),A.adler=t.check=0,t.mode=12;break;case 10:for(;p<32;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}A.adler=t.check=w(U),p=U=0,t.mode=11;case 11:if(0===t.havedict)return A.next_out=f,A.avail_out=C,A.next_in=d,A.avail_in=Q,t.hold=U,t.bits=p,2;A.adler=t.check=1,t.mode=12;case 12:if(5===e||6===e)break A;case 13:if(t.last){U>>>=7&p,p-=7&p,t.mode=27;break}for(;p<3;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}switch(t.last=1&U,p-=1,3&(U>>>=1)){case 0:t.mode=14;break;case 1:if(y(t),t.mode=20,6!==e)break;U>>>=2,p-=2;break A;case 2:t.mode=17;break;case 3:A.msg="invalid block type",t.mode=30}U>>>=2,p-=2;break;case 14:for(U>>>=7&p,p-=7&p;p<32;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}if((65535&U)!=(U>>>16^65535)){A.msg="invalid stored block lengths",t.mode=30;break}if(t.length=65535&U,p=U=0,t.mode=15,6===e)break A;case 15:t.mode=16;case 16:if(E=t.length){if(Q<E&&(E=Q),C<E&&(E=C),0===E)break A;r.arraySet(g,h,d,E,f),Q-=E,d+=E,C-=E,f+=E,t.length-=E;break}t.mode=12;break;case 17:for(;p<14;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}if(t.nlen=257+(31&U),U>>>=5,p-=5,t.ndist=1+(31&U),U>>>=5,p-=5,t.ncode=4+(15&U),U>>>=4,p-=4,286<t.nlen||30<t.ndist){A.msg="too many length or distance symbols",t.mode=30;break}t.have=0,t.mode=18;case 18:for(;t.have<t.ncode;){for(;p<3;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}t.lens[G[t.have++]]=7&U,U>>>=3,p-=3}for(;t.have<19;)t.lens[G[t.have++]]=0;if(t.lencode=t.lendyn,t.lenbits=7,O={bits:t.lenbits},S=o(0,t.lens,0,19,t.lencode,0,t.work,O),t.lenbits=O.bits,S){A.msg="invalid code lengths set",t.mode=30;break}t.have=0,t.mode=19;case 19:for(;t.have<t.nlen+t.ndist;){for(;K=(M=t.lencode[U&(1<<t.lenbits)-1])>>>16&255,L=65535&M,!((v=M>>>24)<=p);){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}if(L<16)U>>>=v,p-=v,t.lens[t.have++]=L;else{if(16===L){for(T=v+2;p<T;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}if(U>>>=v,p-=v,0===t.have){A.msg="invalid bit length repeat",t.mode=30;break}k=t.lens[t.have-1],E=3+(3&U),U>>>=2,p-=2}else if(17===L){for(T=v+3;p<T;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}p-=v,k=0,E=3+(7&(U>>>=v)),U>>>=3,p-=3}else{for(T=v+7;p<T;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}p-=v,k=0,E=11+(127&(U>>>=v)),U>>>=7,p-=7}if(t.have+E>t.nlen+t.ndist){A.msg="invalid bit length repeat",t.mode=30;break}for(;E--;)t.lens[t.have++]=k}}if(30===t.mode)break;if(0===t.lens[256]){A.msg="invalid code -- missing end-of-block",t.mode=30;break}if(t.lenbits=9,O={bits:t.lenbits},S=o(a,t.lens,0,t.nlen,t.lencode,0,t.work,O),t.lenbits=O.bits,S){A.msg="invalid literal/lengths set",t.mode=30;break}if(t.distbits=6,t.distcode=t.distdyn,O={bits:t.distbits},S=o(B,t.lens,t.nlen,t.ndist,t.distcode,0,t.work,O),t.distbits=O.bits,S){A.msg="invalid distances set",t.mode=30;break}if(t.mode=20,6===e)break A;case 20:t.mode=21;case 21:if(6<=Q&&258<=C){A.next_out=f,A.avail_out=C,A.next_in=d,A.avail_in=Q,t.hold=U,t.bits=p,i(A,m),f=A.next_out,g=A.output,C=A.avail_out,d=A.next_in,h=A.input,Q=A.avail_in,U=t.hold,p=t.bits,12===t.mode&&(t.back=-1);break}for(t.back=0;K=(M=t.lencode[U&(1<<t.lenbits)-1])>>>16&255,L=65535&M,!((v=M>>>24)<=p);){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}if(K&&0==(240&K)){for(_=v,x=K,D=L;K=(M=t.lencode[D+((U&(1<<_+x)-1)>>_)])>>>16&255,L=65535&M,!(_+(v=M>>>24)<=p);){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}U>>>=_,p-=_,t.back+=_}if(U>>>=v,p-=v,t.back+=v,t.length=L,0===K){t.mode=26;break}if(32&K){t.back=-1,t.mode=12;break}if(64&K){A.msg="invalid literal/length code",t.mode=30;break}t.extra=15&K,t.mode=22;case 22:if(t.extra){for(T=t.extra;p<T;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}t.length+=U&(1<<t.extra)-1,U>>>=t.extra,p-=t.extra,t.back+=t.extra}t.was=t.length,t.mode=23;case 23:for(;K=(M=t.distcode[U&(1<<t.distbits)-1])>>>16&255,L=65535&M,!((v=M>>>24)<=p);){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}if(0==(240&K)){for(_=v,x=K,D=L;K=(M=t.distcode[D+((U&(1<<_+x)-1)>>_)])>>>16&255,L=65535&M,!(_+(v=M>>>24)<=p);){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}U>>>=_,p-=_,t.back+=_}if(U>>>=v,p-=v,t.back+=v,64&K){A.msg="invalid distance code",t.mode=30;break}t.offset=L,t.extra=15&K,t.mode=24;case 24:if(t.extra){for(T=t.extra;p<T;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}t.offset+=U&(1<<t.extra)-1,U>>>=t.extra,p-=t.extra,t.back+=t.extra}if(t.offset>t.dmax){A.msg="invalid distance too far back",t.mode=30;break}t.mode=25;case 25:if(0===C)break A;if(E=m-C,t.offset>E){if((E=t.offset-E)>t.whave&&t.sane){A.msg="invalid distance too far back",t.mode=30;break}I=E>t.wnext?(E-=t.wnext,t.wsize-E):t.wnext-E,E>t.length&&(E=t.length),b=t.window}else b=g,I=f-t.offset,E=t.length;for(C<E&&(E=C),C-=E,t.length-=E;g[f++]=b[I++],--E;);0===t.length&&(t.mode=21);break;case 26:if(0===C)break A;g[f++]=t.length,C--,t.mode=21;break;case 27:if(t.wrap){for(;p<32;){if(0===Q)break A;Q--,U|=h[d++]<<p,p+=8}if(m-=C,A.total_out+=m,t.total+=m,m&&(A.adler=t.check=t.flags?s(t.check,g,m,f-m):n(t.check,g,m,f-m)),m=C,(t.flags?U:w(U))!==t.check){A.msg="incorrect data check",t.mode=30;break}p=U=0}t.mode=28;case 28:if(t.wrap&&t.flags){for(;p<32;){if(0===Q)break A;Q--,U+=h[d++]<<p,p+=8}if(U!==(4294967295&t.total)){A.msg="incorrect length check",t.mode=30;break}p=U=0}t.mode=29;case 29:S=1;break A;case 30:S=-3;break A;case 31:return-4;default:return u}return A.next_out=f,A.avail_out=C,A.next_in=d,A.avail_in=Q,t.hold=U,t.bits=p,(t.wsize||m!==A.avail_out&&t.mode<30&&(t.mode<27||4!==e))&&H(A,A.output,A.next_out,m-A.avail_out)?(t.mode=31,-4):(F-=A.avail_in,m-=A.avail_out,A.total_in+=F,A.total_out+=m,t.total+=m,t.wrap&&m&&(A.adler=t.check=t.flags?s(t.check,g,m,A.next_out-m):n(t.check,g,m,A.next_out-m)),A.data_type=t.bits+(t.last?64:0)+(12===t.mode?128:0)+(20===t.mode||15===t.mode?256:0),(0==F&&0===m||4===e)&&S===c&&(S=-5),S)},t.inflateEnd=function(A){if(!A||!A.state)return u;var e=A.state;return e.window&&(e.window=null),A.state=null,c},t.inflateGetHeader=function(A,e){var t;return A&&A.state?0==(2&(t=A.state).wrap)?u:((t.head=e).done=!1,c):u},t.inflateSetDictionary=function(A,e){var t,r=e.length;return A&&A.state?0!==(t=A.state).wrap&&11!==t.mode?u:11===t.mode&&n(1,e,r,0)!==t.check?-3:H(A,e,r,r)?(t.mode=31,-4):(t.havedict=1,c):u},t.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./inffast":48,"./inftrees":50}],50:[function(A,e,t){var r=A("../utils/common"),n=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],s=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],i=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],o=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];e.exports=function(A,e,t,a,B,c,u,l){var h,g,w,d,f,Q,C,U,p,F=l.bits,m=0,y=0,H=0,E=0,I=0,b=0,v=0,K=0,L=0,_=0,x=null,D=0,k=new r.Buf16(16),S=new r.Buf16(16),O=null,T=0;for(m=0;m<=15;m++)k[m]=0;for(y=0;y<a;y++)k[e[t+y]]++;for(I=F,E=15;1<=E&&0===k[E];E--);if(E<I&&(I=E),0===E)return B[c++]=20971520,B[c++]=20971520,l.bits=1,0;for(H=1;H<E&&0===k[H];H++);for(I<H&&(I=H),m=K=1;m<=15;m++)if(K<<=1,(K-=k[m])<0)return-1;if(0<K&&(0===A||1!==E))return-1;for(S[1]=0,m=1;m<15;m++)S[m+1]=S[m]+k[m];for(y=0;y<a;y++)0!==e[t+y]&&(u[S[e[t+y]]++]=y);if(Q=0===A?(x=O=u,19):1===A?(x=n,D-=257,O=s,T-=257,256):(x=i,O=o,-1),m=H,f=c,v=y=_=0,w=-1,d=(L=1<<(b=I))-1,1===A&&852<L||2===A&&592<L)return 1;for(;;){for(C=m-v,p=u[y]<Q?(U=0,u[y]):u[y]>Q?(U=O[T+u[y]],x[D+u[y]]):(U=96,0),h=1<<m-v,H=g=1<<b;B[f+(_>>v)+(g-=h)]=C<<24|U<<16|p|0,0!==g;);for(h=1<<m-1;_&h;)h>>=1;if(0!==h?(_&=h-1,_+=h):_=0,y++,0==--k[m]){if(m===E)break;m=e[t+u[y]]}if(I<m&&(_&d)!==w){for(0===v&&(v=I),f+=H,K=1<<(b=m-v);b+v<E&&!((K-=k[b+v])<=0);)b++,K<<=1;if(L+=1<<b,1===A&&852<L||2===A&&592<L)return 1;B[w=_&d]=I<<24|b<<16|f-c|0}}return 0!==_&&(B[f+_]=m-v<<24|64<<16|0),l.bits=I,0}},{"../utils/common":41}],51:[function(A,e,t){e.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],52:[function(A,e,t){var r=A("../utils/common"),n=0,s=1;function i(A){for(var e=A.length;0<=--e;)A[e]=0}var o=0,a=29,B=256,c=B+1+a,u=30,l=19,h=2*c+1,g=15,w=16,d=7,f=256,Q=16,C=17,U=18,p=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],F=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],m=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],y=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],H=new Array(2*(c+2));i(H);var E=new Array(2*u);i(E);var I=new Array(512);i(I);var b=new Array(256);i(b);var v=new Array(a);i(v);var K,L,_,x=new Array(u);function D(A,e,t,r,n){this.static_tree=A,this.extra_bits=e,this.extra_base=t,this.elems=r,this.max_length=n,this.has_stree=A&&A.length}function k(A,e){this.dyn_tree=A,this.max_code=0,this.stat_desc=e}function S(A){return A<256?I[A]:I[256+(A>>>7)]}function O(A,e){A.pending_buf[A.pending++]=255&e,A.pending_buf[A.pending++]=e>>>8&255}function T(A,e,t){A.bi_valid>w-t?(A.bi_buf|=e<<A.bi_valid&65535,O(A,A.bi_buf),A.bi_buf=e>>w-A.bi_valid,A.bi_valid+=t-w):(A.bi_buf|=e<<A.bi_valid&65535,A.bi_valid+=t)}function M(A,e,t){T(A,t[2*e],t[2*e+1])}function R(A,e){for(var t=0;t|=1&A,A>>>=1,t<<=1,0<--e;);return t>>>1}function G(A,e,t){var r,n,s=new Array(g+1),i=0;for(r=1;r<=g;r++)s[r]=i=i+t[r-1]<<1;for(n=0;n<=e;n++){var o=A[2*n+1];0!==o&&(A[2*n]=R(s[o]++,o))}}function V(A){var e;for(e=0;e<c;e++)A.dyn_ltree[2*e]=0;for(e=0;e<u;e++)A.dyn_dtree[2*e]=0;for(e=0;e<l;e++)A.bl_tree[2*e]=0;A.dyn_ltree[2*f]=1,A.opt_len=A.static_len=0,A.last_lit=A.matches=0}function N(A){8<A.bi_valid?O(A,A.bi_buf):0<A.bi_valid&&(A.pending_buf[A.pending++]=A.bi_buf),A.bi_buf=0,A.bi_valid=0}function P(A,e,t,r){var n=2*e,s=2*t;return A[n]<A[s]||A[n]===A[s]&&r[e]<=r[t]}function X(A,e,t){for(var r=A.heap[t],n=t<<1;n<=A.heap_len&&(n<A.heap_len&&P(e,A.heap[n+1],A.heap[n],A.depth)&&n++,!P(e,r,A.heap[n],A.depth));)A.heap[t]=A.heap[n],t=n,n<<=1;A.heap[t]=r}function J(A,e,t){var r,n,s,i,o=0;if(0!==A.last_lit)for(;r=A.pending_buf[A.d_buf+2*o]<<8|A.pending_buf[A.d_buf+2*o+1],n=A.pending_buf[A.l_buf+o],o++,0===r?M(A,n,e):(M(A,(s=b[n])+B+1,e),0!==(i=p[s])&&T(A,n-=v[s],i),M(A,s=S(--r),t),0!==(i=F[s])&&T(A,r-=x[s],i)),o<A.last_lit;);M(A,f,e)}function W(A,e){var t,r,n,s=e.dyn_tree,i=e.stat_desc.static_tree,o=e.stat_desc.has_stree,a=e.stat_desc.elems,B=-1;for(A.heap_len=0,A.heap_max=h,t=0;t<a;t++)0!==s[2*t]?(A.heap[++A.heap_len]=B=t,A.depth[t]=0):s[2*t+1]=0;for(;A.heap_len<2;)s[2*(n=A.heap[++A.heap_len]=B<2?++B:0)]=1,A.depth[n]=0,A.opt_len--,o&&(A.static_len-=i[2*n+1]);for(e.max_code=B,t=A.heap_len>>1;1<=t;t--)X(A,s,t);for(n=a;t=A.heap[1],A.heap[1]=A.heap[A.heap_len--],X(A,s,1),r=A.heap[1],A.heap[--A.heap_max]=t,A.heap[--A.heap_max]=r,s[2*n]=s[2*t]+s[2*r],A.depth[n]=(A.depth[t]>=A.depth[r]?A.depth[t]:A.depth[r])+1,s[2*t+1]=s[2*r+1]=n,A.heap[1]=n++,X(A,s,1),2<=A.heap_len;);A.heap[--A.heap_max]=A.heap[1],function(A,e){var t,r,n,s,i,o,a=e.dyn_tree,B=e.max_code,c=e.stat_desc.static_tree,u=e.stat_desc.has_stree,l=e.stat_desc.extra_bits,w=e.stat_desc.extra_base,d=e.stat_desc.max_length,f=0;for(s=0;s<=g;s++)A.bl_count[s]=0;for(a[2*A.heap[A.heap_max]+1]=0,t=A.heap_max+1;t<h;t++)d<(s=a[2*a[2*(r=A.heap[t])+1]+1]+1)&&(s=d,f++),a[2*r+1]=s,B<r||(A.bl_count[s]++,i=0,w<=r&&(i=l[r-w]),o=a[2*r],A.opt_len+=o*(s+i),u&&(A.static_len+=o*(c[2*r+1]+i)));if(0!==f){do{for(s=d-1;0===A.bl_count[s];)s--;A.bl_count[s]--,A.bl_count[s+1]+=2,A.bl_count[d]--,f-=2}while(0<f);for(s=d;0!==s;s--)for(r=A.bl_count[s];0!==r;)B<(n=A.heap[--t])||(a[2*n+1]!==s&&(A.opt_len+=(s-a[2*n+1])*a[2*n],a[2*n+1]=s),r--)}}(A,e),G(s,B,A.bl_count)}function Y(A,e,t){var r,n,s=-1,i=e[1],o=0,a=7,B=4;for(0===i&&(a=138,B=3),e[2*(t+1)+1]=65535,r=0;r<=t;r++)n=i,i=e[2*(r+1)+1],++o<a&&n===i||(o<B?A.bl_tree[2*n]+=o:0!==n?(n!==s&&A.bl_tree[2*n]++,A.bl_tree[2*Q]++):o<=10?A.bl_tree[2*C]++:A.bl_tree[2*U]++,s=n,B=(o=0)===i?(a=138,3):n===i?(a=6,3):(a=7,4))}function z(A,e,t){var r,n,s=-1,i=e[1],o=0,a=7,B=4;for(0===i&&(a=138,B=3),r=0;r<=t;r++)if(n=i,i=e[2*(r+1)+1],!(++o<a&&n===i)){if(o<B)for(;M(A,n,A.bl_tree),0!=--o;);else 0!==n?(n!==s&&(M(A,n,A.bl_tree),o--),M(A,Q,A.bl_tree),T(A,o-3,2)):o<=10?(M(A,C,A.bl_tree),T(A,o-3,3)):(M(A,U,A.bl_tree),T(A,o-11,7));s=n,B=(o=0)===i?(a=138,3):n===i?(a=6,3):(a=7,4)}}i(x);var Z=!1;function j(A,e,t,n){var s,i,a;T(A,(o<<1)+(n?1:0),3),i=e,a=t,N(s=A),O(s,a),O(s,~a),r.arraySet(s.pending_buf,s.window,i,a,s.pending),s.pending+=a}t._tr_init=function(A){Z||(function(){var A,e,t,r,n,s=new Array(g+1);for(r=t=0;r<a-1;r++)for(v[r]=t,A=0;A<1<<p[r];A++)b[t++]=r;for(b[t-1]=r,r=n=0;r<16;r++)for(x[r]=n,A=0;A<1<<F[r];A++)I[n++]=r;for(n>>=7;r<u;r++)for(x[r]=n<<7,A=0;A<1<<F[r]-7;A++)I[256+n++]=r;for(e=0;e<=g;e++)s[e]=0;for(A=0;A<=143;)H[2*A+1]=8,A++,s[8]++;for(;A<=255;)H[2*A+1]=9,A++,s[9]++;for(;A<=279;)H[2*A+1]=7,A++,s[7]++;for(;A<=287;)H[2*A+1]=8,A++,s[8]++;for(G(H,c+1,s),A=0;A<u;A++)E[2*A+1]=5,E[2*A]=R(A,5);K=new D(H,p,B+1,c,g),L=new D(E,F,0,u,g),_=new D(new Array(0),m,0,l,d)}(),Z=!0),A.l_desc=new k(A.dyn_ltree,K),A.d_desc=new k(A.dyn_dtree,L),A.bl_desc=new k(A.bl_tree,_),A.bi_buf=0,A.bi_valid=0,V(A)},t._tr_stored_block=j,t._tr_flush_block=function(A,e,t,r){var i,o,a=0;0<A.level?(2===A.strm.data_type&&(A.strm.data_type=function(A){var e,t=4093624447;for(e=0;e<=31;e++,t>>>=1)if(1&t&&0!==A.dyn_ltree[2*e])return n;if(0!==A.dyn_ltree[18]||0!==A.dyn_ltree[20]||0!==A.dyn_ltree[26])return s;for(e=32;e<B;e++)if(0!==A.dyn_ltree[2*e])return s;return n}(A)),W(A,A.l_desc),W(A,A.d_desc),a=function(A){var e;for(Y(A,A.dyn_ltree,A.l_desc.max_code),Y(A,A.dyn_dtree,A.d_desc.max_code),W(A,A.bl_desc),e=l-1;3<=e&&0===A.bl_tree[2*y[e]+1];e--);return A.opt_len+=3*(e+1)+5+5+4,e}(A),i=A.opt_len+3+7>>>3,(o=A.static_len+3+7>>>3)<=i&&(i=o)):i=o=t+5,t+4<=i&&-1!==e?j(A,e,t,r):4===A.strategy||o===i?(T(A,2+(r?1:0),3),J(A,H,E)):(T(A,4+(r?1:0),3),function(A,e,t,r){var n;for(T(A,e-257,5),T(A,t-1,5),T(A,r-4,4),n=0;n<r;n++)T(A,A.bl_tree[2*y[n]+1],3);z(A,A.dyn_ltree,e-1),z(A,A.dyn_dtree,t-1)}(A,A.l_desc.max_code+1,A.d_desc.max_code+1,a+1),J(A,A.dyn_ltree,A.dyn_dtree)),V(A),r&&N(A)},t._tr_tally=function(A,e,t){return A.pending_buf[A.d_buf+2*A.last_lit]=e>>>8&255,A.pending_buf[A.d_buf+2*A.last_lit+1]=255&e,A.pending_buf[A.l_buf+A.last_lit]=255&t,A.last_lit++,0===e?A.dyn_ltree[2*t]++:(A.matches++,e--,A.dyn_ltree[2*(b[t]+B+1)]++,A.dyn_dtree[2*S(e)]++),A.last_lit===A.lit_bufsize-1},t._tr_align=function(A){var e;T(A,2,3),M(A,f,H),16===(e=A).bi_valid?(O(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):8<=e.bi_valid&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}},{"../utils/common":41}],53:[function(A,e,t){e.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(e,t,r){(function(A){!function(A,e){if(!A.setImmediate){var t,r,n,s,i=1,o={},a=!1,B=A.document,c=Object.getPrototypeOf&&Object.getPrototypeOf(A);c=c&&c.setTimeout?c:A,t="[object process]"==={}.toString.call(A.process)?function(A){process.nextTick((function(){l(A)}))}:function(){if(A.postMessage&&!A.importScripts){var e=!0,t=A.onmessage;return A.onmessage=function(){e=!1},A.postMessage("","*"),A.onmessage=t,e}}()?(s="setImmediate$"+Math.random()+"$",A.addEventListener?A.addEventListener("message",h,!1):A.attachEvent("onmessage",h),function(e){A.postMessage(s+e,"*")}):A.MessageChannel?((n=new MessageChannel).port1.onmessage=function(A){l(A.data)},function(A){n.port2.postMessage(A)}):B&&"onreadystatechange"in B.createElement("script")?(r=B.documentElement,function(A){var e=B.createElement("script");e.onreadystatechange=function(){l(A),e.onreadystatechange=null,r.removeChild(e),e=null},r.appendChild(e)}):function(A){setTimeout(l,0,A)},c.setImmediate=function(A){"function"!=typeof A&&(A=new Function(""+A));for(var e=new Array(arguments.length-1),r=0;r<e.length;r++)e[r]=arguments[r+1];var n={callback:A,args:e};return o[i]=n,t(i),i++},c.clearImmediate=u}function u(A){delete o[A]}function l(A){if(a)setTimeout(l,0,A);else{var t=o[A];if(t){a=!0;try{!function(A){var t=A.callback,r=A.args;switch(r.length){case 0:t();break;case 1:t(r[0]);break;case 2:t(r[0],r[1]);break;case 3:t(r[0],r[1],r[2]);break;default:t.apply(e,r)}}(t)}finally{u(A),a=!1}}}}function h(e){e.source===A&&"string"==typeof e.data&&0===e.data.indexOf(s)&&l(+e.data.slice(s.length))}}("undefined"==typeof self?void 0===A?this:A:self)}).call(this,void 0!==A?A:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[10])(10)),Xi.exports);const Wi=e(Ji);export{Vi as F,Wi as J,t as a,A as c,e as g,Si as h};

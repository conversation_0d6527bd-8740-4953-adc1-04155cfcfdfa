import{r as e}from"./request-v7NtcsCU.js";function t(t,a){return e.post("/qa/export",t,a)}function a(t){return e.delete("/qa",{data:t})}function s(t){return e.put("/qa",t)}function r(t){let a=`/qa/list?taskId=${t.taskId}`;return a=t.allocateUserId?a+`&allocateUserId=${t.allocateUserId}`:a,a=t.fileIdList&&t.fileIdList.length>0?a+`&fileIdList=${t.fileIdList.join(",")}`:a,a=void 0!==t.isReview?a+`&isReview=${t.isReview}`:a,a=t.keyword?a+`&keyword=${t.keyword}`:a,a=t.page?a+`&page=${t.page}`:a,a=t.pageSize?a+`&pageSize=${t.pageSize}`:a,a=t.score?a+`&score=${t.score}`:a,a=t.tags&&t.tags.length>0?a+`&tags=${t.tags}`:a,e.post(a)}function n(t,a){const s=`/qa/file-content?taskId=${t}&qaId=${a}`;return e.get(s)}function i(t,a){const s=`/qa/deallocate?taskId=${t}&userId=${a}`;return e.get(s)}function o(t,a){const s=`/qa/allocate?taskId=${t}&userId=${a}`;return e.get(s)}function c(t){const a=`/qa/tags/statistics?taskId=${t}`;return e.get(a)}function d(t){return e.post("/review/score",t)}async function u(t,a){try{const s=`/review/allocated/qas?taskId=${t}&userId=${a}`;return(await e.get(s)).data}catch(s){return}}function l(t,a){return e.post("/review/reviewer",{taskId:t,userId:a})}function I(t,a){return e.delete("/review/reviewer",{data:{taskId:t,userId:a}})}function f(t){const a=`/review/reviewer/list?taskId=${t}`;return e.get(a)}export{l as a,o as b,r as c,I as d,c as e,n as f,f as g,a as h,t as i,i as j,u as q,d as s,s as u};

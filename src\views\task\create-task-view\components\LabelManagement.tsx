import React, { ChangeEvent } from 'react';
import { Switch, Input, Button, message } from 'antd';
import { LabelManagementProps } from '../types';
import { DEFAULT_VALUES, CSS_CLASSES, MESSAGES, VALIDATION_RULES } from '../constants';
import { getLabelList } from '@/api/task';

const { TextArea } = Input;

const LabelManagement: React.FC<LabelManagementProps> = ({
  autoLabel,
  labelText,
  isGeneratingLabels,
  fileList,
  onAutoLabelChange,
  onLabelTextChange,
  onAIGenerate,
}) => {
  const handleAutoChange = (checked: boolean) => {
    onAutoLabelChange(checked);
    if (!checked) {
      onLabelTextChange(DEFAULT_VALUES.LABEL_TEXT);
    }
  };

  const handleLabelChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    let inputValue = e.target.value;
    // 处理逗号格式
    inputValue = inputValue.replace(/\s*，\s*/g, '，');
    inputValue = inputValue.replace(/，{2,}/g, '，');
    
    // 处理开头的逗号
    if (inputValue.trimStart().startsWith('，')) {
      const firstCommaIndex = inputValue.indexOf('，');
      if (firstCommaIndex !== -1) {
        inputValue =
          inputValue.substring(0, firstCommaIndex) + inputValue.substring(firstCommaIndex + 1);
      }
    }
    
    onLabelTextChange(inputValue.trim());
  };

  const handleAIGenerate = async () => {
    if (fileList.length === 0) {
      message.warning(MESSAGES.WARNING.SELECT_DATASET);
      return;
    }
    
    const currentLabelCount = labelText ? labelText.split('，').length : 0;
    if (currentLabelCount >= DEFAULT_VALUES.MAX_LABEL_COUNT) {
      message.warning(MESSAGES.WARNING.MAX_LABELS);
      return;
    }

    try {
      await onAIGenerate();
    } catch (error) {
      message.error(MESSAGES.ERROR.LABEL_GENERATION_FAILED);
      console.error('生成标签失败:', error);
    }
  };

  // 验证标签格式
  const validateLabels = (text: string): boolean => {
    return VALIDATION_RULES.LABEL.PATTERN.test(text);
  };

  return (
    <>
      <div className={CSS_CLASSES.AUTO_LABEL}>
        <span style={{ marginRight: '15px' }}>自动标签</span>
        <Switch size="small" checked={autoLabel} onChange={handleAutoChange} />
      </div>
      
      {autoLabel && (
        <div style={{ marginTop: '16px' }}>
          <div style={{ position: 'relative' }}>
            <TextArea
              value={labelText}
              onChange={handleLabelChange}
              autoSize={{ minRows: 2, maxRows: 6 }}
              onPressEnter={(e: React.KeyboardEvent<HTMLTextAreaElement>) =>
                e.preventDefault()
              } // 阻止回车换行
              placeholder={MESSAGES.PLACEHOLDER.LABEL}
              className={CSS_CLASSES.LABEL_TEXTAREA}
            />
            <div className={CSS_CLASSES.LABEL_TEXTAREA_FOOTER}>
              <div
                style={{
                  marginTop: '8px',
                  fontSize: '14px',
                  color: '#666',
                  lineHeight: '1.4',
                }}
              >
                请输入分类标签，以逗号分隔。示例:飞机，电磁，坦克..……，请勿使用特殊符号
              </div>
              <Button
                type="primary"
                loading={isGeneratingLabels}
                onClick={handleAIGenerate}
                className={CSS_CLASSES.CREATE_AI_BTN}
              >
                AI智能生成
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default LabelManagement;

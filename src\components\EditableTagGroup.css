/* 标签动画效果 */

/* 标签从可选区域移动到当前标签区域的动画 */
@keyframes tagMoveUp {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-40px) scale(1);
    opacity: 1;
  }
}

/* 标签从当前标签区域移动到可选区域的动画 */
@keyframes tagMoveDown {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(20px) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(40px) scale(1);
    opacity: 1;
  }
}

/* 标签淡入动画 */
@keyframes tagFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 标签淡出动画 */
@keyframes tagFadeOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

/* 应用动画的类 */
.tag-move-up {
  animation: tagMoveUp 0.6s ease-in-out;
}

.tag-move-down {
  animation: tagMoveDown 0.6s ease-in-out;
}

.tag-fade-in {
  animation: tagFadeIn 0.3s ease-in-out;
}

.tag-fade-out {
  animation: tagFadeOut 0.3s ease-in-out;
}

/* 标签容器的过渡效果 */
.qa-tags {
  transition: all 0.3s ease;
}

.available-tags-panel {
  transition: all 0.3s ease;
}

/* 标签的基础过渡效果 */
.edit-tag, .select-tag {
  transition: all 0.3s ease;
  position: relative;
}

/* 标签hover效果增强 */
.edit-tag:hover, .select-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 标签选中时的脉冲效果 */
@keyframes tagPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.tag-pulse {
  animation: tagPulse 0.3s ease-in-out;
}

/* 标签移动时的临时样式 */
.tag-moving {
  z-index: 1000;
  position: relative;
}

/* 可选标签面板的展开/收起动画 */
.available-tags-panel-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.available-tags-panel-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.available-tags-panel-exit {
  opacity: 1;
  transform: translateY(0);
}

.available-tags-panel-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}
.tag-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow: auto;
  height: 22rem;
}
.qa-tag {
  margin: 0 3px 3px 0;
  min-height: 32px;
  border-radius: 2px;
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  padding: 0 10px;
  border: 1px solid #F1F1F1
}
.edit-tag,
.select-tag {
  color: #0FB698 !important;
  cursor: pointer;
  transition: all 0.3s ease;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 7px;
  margin-top: 4px;
}
.select-tag:hover {
  background-color: #99E2DA !important;
}
.qa-item-container:hover {
  border-radius: 0px 8px 8px 0px;
  background: #f5f5f5;
}
.qa-tag .ant-tag.ant-tag-has-color .anticon-close {
  color: #2C2C2C;
}
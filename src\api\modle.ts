import { AxiosResponse } from "axios";
import {
  CustomResponseType,
  ModelDetailType,
  modelOnlineType,
  ModelSetType,
} from "../types";
import request from "../utils/request";
import { AddBaseModelFormType, UploadBaseModelFormType } from "../views/task/finetuntask/UploadBaseModel";
// import { StyleInfo } from "antd/es/theme/util/genComponentStyleHook";

export interface GetModlesParams {
  page?: number;
  size?: number;
  sortAttribute?: string;
  sortDirection?: string;
  modelName: string;
  status: string;
  modelCategory?: string;
  category: number;
}

interface ModelSetsResp extends CustomResponseType {
  data: ModelSetType[];
  totalCount: number;
}

interface ModelDetail extends CustomResponseType {
  data: ModelDetailType;
}

export interface updataModelParams {
  modelId: string;
  modelIntro?: string;
  modelName?: string;
  modelScene?: string;
}
interface ModelOnine extends CustomResponseType {
  id: string;
  status: string;
  time: number;
}

export interface CreateTrainingParams {
  baseModelId: string;
  batchSize: string;
  iterationRound: string;
  learningRate: string;
  modelIntro: string;
  modelName: string;
  taskIdList: string[];
  testSetRatio: number;
  trainStrategy: string;
}
interface ApiResponse {
  code: number;
  message: string;
  data: ModelDetailType[];
}
interface BaseResponse<T> {
  code: number;
  message: string;
  data: T;
}
/**
 * 获取模型集
 * @param getModelSets
 * @returns
 */
export function getModelSets(
  params: GetModlesParams
): Promise<AxiosResponse<ModelSetsResp>> {
  let url = `/fine-tuning/model/list?page=${params.page}&size=${params.size}&category=${params.category}`;
  url = params.sortAttribute
    ? url + `&sortAttribute=${params.sortAttribute}`
    : url;
  url = params.sortDirection
    ? url + `&sortDirection=${params.sortDirection}`
    : url;
  url = params.modelName ? url + `&modelName=${params.modelName}` : url;
  url = params.status ? url + `&status=${params.status}` : url;
  url = params.modelCategory
    ? url + `&modelCategory=${params.modelCategory}`
    : url;
  // if (params.filter) {
  //   url += `&${JSON.stringify(params.filter)}`;
  // }
  return request.get(url);
}
/**
 * 获取基座模型详情
 * @param modelId 需要获取详情的模型ID
 * @returns
 */
export function getModelDetail(
  modelId: string
): Promise<AxiosResponse<ModelDetail>> {
  return request.get(`/fine-tuning/model?modelId=${modelId}`);
}

export async function queryBaseModel(): Promise<ModelDetailType[]> {
  try {
    const res: AxiosResponse<ApiResponse> = await request.get(
      `/fine-tuning/model/list`
    );

    if (res.data.code === 200) {
      // 过滤出 category === 0 的基础模型
      const baseModel = res.data.data.filter(
        (item: ModelDetailType) => item.category === 0
      );
      return baseModel;
    } else {
      // 如果 code 不为 200，返回空数组
      console.error("API Error:", res.data.message);
      return [];
    }
  } catch (error) {
    // 捕获请求错误
    console.error("Request Error:", error);
    return [];
  }
}
/**
 * 更新模型
 * @param params 需要获更新模型的模型信息
 * @returns
 */
export function updataModel(params: updataModelParams) {
  const url = `/fine-tuning/model?modelId`;
  return request.put(url, params);
}

/**
 * 创建微调训练
 * @param params 需要获更新模型的模型信息
 * @returns
 */
export function createTraining(params: CreateTrainingParams) {
  const url = `/fine-tuning/model/train`;
  return request.post(url, params);
}

/**
 * 获取我的模型详情
 * @param modelId 需要获取详情的模型ID
 * @returns
 */
export function getMyModelDetail(
  modelId: string
): Promise<AxiosResponse<ModelDetail>> {
  return request.get(`/model?modelId=${modelId}`);
}

export function modelOnline(
  id: string,
  status: number,
  time: number
): Promise<AxiosResponse<ModelDetail>> {
  return request.post(`/model/online?id=${id}&status=${status}&time=${time}`);
}

export function modelOffline(
  id: string,
  status: number
): Promise<AxiosResponse<ModelDetail>> {
  return request.post(`/model/offline?id=${id}&status=${status}`);
}
// 模型上下线
export function getModelOnline(id: string) {
  return request.post(`/fine-tuning/model/model/${id}`);
}
export function getModelAccess(id: string) {
  return request.post(`/model/train/access?id=${id}`);
}

export function getEstimate(
  id: string,
  strategy: string,
  interation_number: number,
  rate: number,
  size: number
) {
  return request.get(
    `/model/train/estimate?id=${id}&strategy=${strategy}&interation_number=${interation_number}&rate=${rate}&size=${size}`
  );
}

export function createModel(params: any) {
  return request.post(`/fine-tuning/model`, params);
}
// 删除模型
export function deleteModel(modelId: Number) {
  return request.delete(`/fine-tuning/model/delete/${modelId}`);
}
export function cannelTrain(id: number) {
  return request.post(`/fine-tuning/model/train/stop/${id}`);
}

export function startTrain(id: string) {
  return request.post(`/fine-tuning/model/train/${id}`);
}

// 上线模型
export function onlineModel(id: string) {
  return request.post(`/fine-tuning/model/model/${id}`);
}

// 参数配置
export function getModelConfig(id: string) {
  return request.get(`/fine-tuning/model/config/${id}`);
}

// 对话
export function getChat(id: string) {
  return request.get(`/fine-tuning/model/chat/${id}`);
}
// 获取服务器列表
export function getServerList() {
  return request.get(`/fine-tuning/model/server/all`);
}
// 获取算力配置
export function getPower(id: string) {
  return request.get(`/fine-tuning/model/server/${id}`);
}
// 根据模型id获取模型配置信息
export function getModelConfigById(id: string) {
  return request.get(`/fine-tuning/model/configInfo`, {
    params: { modelId: id },
  });
}
// 训练失败后重新训练，根据失败模型id获取模型配置信息
export function getModelConfigByFailId(modelId: string) {
  return request.get(`/fine-tuning/model/info/${modelId}`);
}
// 配置调整
export function configAdjust(modelId: string, params: any) {
  return request.post(`/fine-tuning/model/config?modelId=${modelId}`, params);
}
// 预估训练时长
export function predicateTime(params: any) {
  return request.post(`/fine-tuning/model/train/predicate`, params);
}

export async function uploadModel(
  UploadBaseModelForm: UploadBaseModelFormType
): Promise<BaseResponse<boolean>> {
  try {
    const url = `/fine-tuning/model/upload`;
    const res = await request.post(url, UploadBaseModelForm);
    if (res.data.code === 200) {
      return { code: 200, message: "上传成功", data: true };
    } else  {
      return { code: 400, message: res.data.message, data: false };
    } 
  } catch (e) {
    return { code: 400, message: "未知错误", data: false };
  }
}

export async function addModel(
  UploadBaseModelForm: AddBaseModelFormType
): Promise<BaseResponse<boolean>> {
  try {
    const url = `/fine-tuning/model/base-model`;
    const res = await request.post(url, UploadBaseModelForm);
    if (res.data.code === 200) {
      return { code: 200, message: "添加成功", data: true };
    } else  {
      return { code: 400, message: res.data.message, data: false };
    } 
  } catch (e) {
    return { code: 400, message: "未知错误", data: false };
  }
}

export async function deleteBaseModel(modelId: string
): Promise<BaseResponse<boolean>> {
  try {
    const url = `/fine-tuning/model/delete/${modelId}`;
    const res = await request.delete(url);
    if (res.data.code === 200) {
      return { code: 200, message: "删除成功", data: true };
    } else  {
      return { code: 400, message: res.data.message, data: false };
    } 
  } catch (e) {
    return { code: 400, message: "未知错误", data: false };
  }
}

// 根据模型id获取模型服务器信息
export function getModelServerInfo(id: string) {
  return request.get(`/server/by-model?modelId=${id}`);
}

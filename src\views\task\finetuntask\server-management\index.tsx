import React, { useState, useEffect } from "react";
import { Button, Space, message } from "antd";
import "../../../../css/uploadBaseModel.css";
import { LeftOutlined, UploadOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { ServerConfiguration } from "./data";
import { queryServer } from "../../../../api/server";
import ServerBg from "../../../../assets/img/server-bg.svg";
const ServerManagement: React.FC = () => {
  const [serverList, setServerList] = useState<ServerConfiguration[]>([]);
  const navigate = useNavigate();
  useEffect(() => {
    const fetchServerData = async () => {
      try {
        const serverData = await queryServer();
        setServerList(serverData);
      } catch (error) {
        message.error("获取服务器信息失败");
      }
    };
    fetchServerData();
  }, []);

  return (
    <div className="uploadBaseModel">
      <Space
        size={20}
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          width: "100%",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "10px",
          }}
        >
          <Button
            style={{ fontSize: "12px", width: "36px", height: "36px" }}
            shape="circle"
            icon={<LeftOutlined />}
            onClick={() => navigate(-1)}
          />
          <div
            className="mediumText"
            style={{ fontSize: "20px", lineHeight: "36px", fontWeight: "500" }}
          >
            服务器管理
          </div>
        </div>
        <Button
          type="primary"
          size="large"
          shape="round"
          style={{
            backgroundColor: "black",
            fontSize: "14px",
            fontWeight: "700",
          }}
          className="boldText"
          onClick={() => {
            navigate("/main/finetune/serverManagement/newServer");
          }}
        >
          添加服务器
        </Button>
      </Space>
      <div className="uploadModelArea">
        {/* 渲染服务器卡片 */}
        <div
          style={{
            display: "flex",
            flexWrap: "wrap",
            gap: "20px",
            marginTop: "20px",
          }}
        >
          {serverList.length > 0 ? (
            serverList.map((server) => (
              <div
                key={server.id}
                style={{
                  width: "calc(25% - 20px)", 
                  height: "185px",
                  backgroundImage: `url(${ServerBg})`,
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  position: "relative",                 
                  borderRadius: "8px",
                  overflow: "hidden",
                  boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
                }}
              >
                <div
                  style={{
                    position: "absolute",
                    top: "80px",
                    left: "10px",
                    color: "#fff",
                    fontSize: "12px",
                    fontWeight: "400",
                    textShadow: "1px 1px 2px rgba(0, 0, 0, 0.5)",
                  }}
                >
                  {server.description?.toString().length > 20 ? `${server.description?.toString().substring(0, 20)}...` : server.description}
                </div>
                <div
                  style={{
                    position: "absolute",
                    top: "10px",
                    left: "10px",
                    color: "#fff",
                    fontSize: "16px",
                    fontWeight: "bold",
                    textShadow: "1px 1px 2px rgba(0, 0, 0, 0.5)",
                  }}
                >
                  {server.serverName}
                </div>
                <div
                  style={{
                    position: "absolute",
                    bottom: "10px",
                    right: "10px",
                  }}
                >
                  <div
                    style={{
                      padding: "5px 10px",
                      border: "none",
                      borderRadius: "4px",
                      cursor: "pointer",
                      fontSize: "12px",
                      color: "#fff",
                    }}
                    onClick={() => navigate(`/main/finetune/serverManagement/updateServer/${server.id}`)} // 跳转到配置页面
                  >
                    配置
                  </div>
                </div>
              </div>
            ))
          ) : (
            <p>暂无服务器信息</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ServerManagement;
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal, message } from "antd";
import { Key } from "antd/es/table/interface";
import Tree, { DataNode, EventDataNode, TreeProps } from "antd/es/tree";
import { useEffect, useState } from "react";
import Icon, { ExclamationCircleFilled } from "@ant-design/icons";
import { DataSetType, qATaskRetryRequest } from "../types";
import { deleteDataset } from "../api/dataset";
import { title } from "process";
import { deleteillationTask, reillationTask } from "../api/task";
interface UploadErrorModalProp {
  rowData?: any;
  visible: boolean;
  OnClose: (rows?: any[]) => void;
  OnChange?: () => void;
}

const UploadErrorModal: React.FC<UploadErrorModalProp> = ({
  rowData,
  visible,
  OnClose,
  OnChange,
}) => {
  const { confirm } = Modal;
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [taskId, setTaskId] = useState<string>()
  const [fileIdArray, setFileIdArray] = useState<string[]>([]);
  const [messageApi, contextHolder] = message.useMessage()
  const [showWarning, setShowWarning] = useState(false)
  const onOk = () => {
    OnClose();
  };
  const onCancel = () => {
    OnClose();
  };

  const relist = () => {
    const params: qATaskRetryRequest = {
      taskId: taskId!,
      fileIdList: fileIdArray.map(num => num.toString())
    };
    reillationTask(params)
  }

  const deletelist = () => {
    const params: qATaskRetryRequest = {
      taskId: taskId!,
      fileIdList: fileIdArray.map(num => num.toString())
    };
    deleteillationTask(params)
  }

  useEffect(() => {
    console.log('uploadError', rowData);
    if (rowData) {
      setTaskId(rowData.taskId);
      rowData.problems?.forEach((element: { fileTreeNodeForProblem: any; }) => {
        const fileTreeNodeForProblem = element.fileTreeNodeForProblem
        setTreeData([fileTreeNodeForProblem]);
      });
    }
  }, [rowData])
  return (
    <Modal
      centered
      title="解析失败文档"
      keyboard={false}
      maskClosable={false}
      width={588}
      open={visible}
      onOk={onOk}
      onCancel={onCancel}
      footer={[
        <div
          style={{
            display: "flex",
            justifyContent: "end",
            alignItems: "center",
          }}
        >
          <Button
            type="link"
            className="boldText"
            style={{ color: "#111111" }}
            onClick={() => {
              const modal = confirm({
                centered: true,
                title: "删除提示",
                icon: <ExclamationCircleFilled />,
                width: 540,
                content: (
                  <>
                    <div className="default-info" style={{ color: "#111111" }}>
                      确定要删除所选数据集吗？
                    </div>
                    {rowData && rowData?.relatedTask?.length > 0 ? (
                      <div
                        className="upload-error-label"
                        style={{ marginTop: "8px" }}
                      >
                        删除源数据集则无法在相关推理任务审核中进行原文对照，且删除后无法恢复。
                      </div>
                    ) : null}
                  </>
                ),
                footer: [
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "flex-end",
                      padding: "2rem 0 0 0",
                      gap: "8px",
                    }}
                  >
                    <Button
                      type="text"
                      onClick={() => {
                        modal.destroy()
                        onOk()
                      }}
                      shape="round"
                    >
                      取消
                    </Button>
                    <Button
                      disabled={rowData && rowData?.relatedTask?.length > 0}
                      type="primary"
                      onClick={() => {
                        if (fileIdArray.length !== 0) {
                          modal.destroy()
                          deletelist()
                          setFileIdArray([])
                        } else {
                          message.info(`没有选择文件或文件为空`);
                          modal.destroy()
                          onOk()
                        }
                      }}
                      shape="round"
                      className="primary-btn"
                      style={{ width: "120px" }}
                    >
                      确认删除
                    </Button>
                  </div>,
                ],

                onOk() {
                  if (rowData) {
                    deletelist()
                    if (OnChange) OnChange();
                    modal.destroy();
                  }
                },
                onCancel() {
                  modal.destroy();
                },
              });

              onOk();
            }}
          >
            删除
          </Button>
          <Button
            type="primary"
            shape="round"
            className="primary-btn boldText"
            onClick={() => {
              if (fileIdArray.length !== 0) {
                relist()
                setFileIdArray([])
              } else {
                message.info(`没有选择文件或文件为空`);
              }
              onOk()
            }}
          >
            重新推理
          </Button>
        </div>,
      ]}
    >
      <div>
        <div className="upload-error-label">
          以下文档解析失败，请选择处理方式
        </div>
        <Tree
          checkable
          // defaultExpandedKeys={["0-0-0", "0-0-1"]}
          // defaultSelectedKeys={["0-0-0", "0-0-1"]}
          // defaultCheckedKeys={["0-0-0", "0-0-1"]}
          virtual={false}
          treeData={treeData}
          onCheck={(checked: Key[] | { checked: Key[]; halfChecked: Key[]; }, info: any) => {
            let checkedKeys: Key[] = [];
            if (Array.isArray(checked)) {
              checkedKeys = checked;
            } else {
              checkedKeys = checked.checked;
            }
            const getChildrenFileIds = (node: any) => {
              const childrenFileIds: string[] = [];
              if (node.type === 'FILE') {
                childrenFileIds.push(node.fileId);
              } else if (node.children && node.children.length > 0) {
                node.children.forEach((child: any) => {
                  if (child.type === 'FILE') {
                    childrenFileIds.push(child.fileId);
                  } else {
                    childrenFileIds.push(...getChildrenFileIds(child));
                  }
                });
              }
              return childrenFileIds;
            }
            let newFileIdArray = [...fileIdArray];
            // 去掉取消选择的 fileId
            if (!checkedKeys.includes(info.node.key)) {
              const uncheckedNodeFileIds = getChildrenFileIds(info.node);
              newFileIdArray = newFileIdArray.filter((fileId) => !uncheckedNodeFileIds.includes(fileId));
            }
            if (checkedKeys) {
              checkedKeys.forEach((key: Key) => {
                const node = info.node.key === key ? info.node : info.checkedNodes.find((node: { key: Key }) => node.key === key);
                if (node) {
                  newFileIdArray.push(...getChildrenFileIds(node));
                }
              });
            }
            // 更新 fileIdArray 数组
            setFileIdArray(newFileIdArray);
          }}
          fieldNames={{
            title: "name",
            key: "name",
            children: "children"
          }}
        />


      </div>
    </Modal>
  );
};

export default UploadErrorModal;

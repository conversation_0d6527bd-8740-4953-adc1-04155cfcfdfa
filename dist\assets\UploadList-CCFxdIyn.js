import{r as e,j as s,B as t,l,f as a,ap as i,x as n,aA as r,I as o,at as d,a3 as c,as as x,c as u,d as h,an as p}from"./react-core-BrQk45h1.js";import{U as f}from"./UploadErrorModal-Dpa2hJTx.js";const j={width:64,height:22,marginInlineEnd:8,verticalAlign:"top"},m={height:22,borderStyle:"dashed"},g={borderRadius:"var(--br-9xs)",backgroundColor:"var(--color-skyblue)",color:"var(--color-steelblue-100)",padding:"var(--padding-10xs) 0.5rem",fontSize:"14px"},y=({tagList:x,onChange:u,showAddBtn:h,AddBtnLabel:p,OnEdit:f,isClose:y})=>{const[v,b]=e.useState(!1),[w,S]=e.useState(""),[k,C]=e.useState(0),E=e.useRef(null),N=e.useRef(null),[A,z]=e.useState(!1),[I,B]=e.useState(x||[]);e.useEffect((()=>{B(x)}),[]),e.useEffect((()=>{T()}),[I]),e.useEffect((()=>{T()}),[k]),e.useEffect((()=>{}),[A]);const T=()=>{if(N.current){const e=N.current;z(0===k?N.current.scrollWidth>N.current.clientWidth:k>e.clientWidth-e.scrollWidth)}},L=()=>{if(f&&f(!1),w&&!I.includes(w)){const e=[...I,w];B(e),u&&u(e),T()}b(!1),S("")},q=e=>{N.current&&(N.current,C((s=>s+e>0?0:s+e)))};return s.jsxs("div",{style:{display:"flex",alignItems:"center"},onMouseLeave:()=>{C(0)},children:[k>=0?null:s.jsx(t,{style:{position:"relative",left:"0"},type:"text",size:"small",onClick:()=>q(80),children:s.jsx(l,{style:{fontSize:"12px"}})}),s.jsx("div",{className:"tag-list ant-table-cell ant-table-cell-ellipsis",ref:N,children:s.jsx(a,{children:s.jsxs("div",{style:{display:"inline-flex",transform:`translateX(${k}px)`,transition:"transform 400ms ease-in-out 0s"},children:[I.map(((e,t)=>s.jsx(i,{className:"tag-item reqularText",bordered:!1,style:g,onClose:e=>{e.preventDefault();const s=null==I?void 0:I.filter(((e,s)=>s!==t));B(s),u&&u(s)},closable:!!y,closeIcon:s.jsx("a",{style:{position:"absolute",top:"-8px"},className:"tag-item-close-btn",children:s.jsx(n,{count:s.jsx(r,{style:{color:"white",backgroundColor:"#a5b1c5",borderRadius:"50%",cursor:"pointer",fontSize:"12px"}})})}),children:e},t))),h?v?s.jsx(o,{autoFocus:!0,maxLength:5,ref:E,type:"text",size:"small",style:j,value:w,onChange:e=>{S(e.target.value)},onBlur:L,onPressEnter:L}):s.jsx("div",{style:{width:"28px",height:"22px"},children:s.jsx(i,{style:m,icon:s.jsx(d,{}),onClick:()=>{f&&f(!0),b(!0)},className:"add-btn",children:p})}):s.jsx("div",{style:{width:"28px",height:"22px"}})]})})}),A?s.jsx(t,{style:{position:"relative",right:"0"},type:"text",size:"small",onClick:()=>q(-80),children:s.jsx(c,{style:{fontSize:"12px"}})}):null]})},v=()=>{const[t,l]=e.useState(0);return e.useEffect((()=>{(()=>{const e=setInterval((()=>{l((e=>{const s=e+10*Math.random();return s>=100?100:s}))}),500)})()}),[t]),s.jsx(x,{style:{position:"absolute",left:"0",bottom:"-16px"},percent:t,showInfo:!1,size:["100%",2]})},b={done:"上传成功",success:"上传成功",error:"上传失败",uploading:"上传中",removed:"移除"},w=({fileList:l,onAddTag:a,className:i,noEditTag:n,onDelFile:r,onReUpload:o})=>{const[d,c]=e.useState([]),[x,j]=e.useState(!1);e.useEffect((()=>{c(l)}),[l]);const m=({file:e,index:l})=>{const{parseState:a,status:i}=e;if("uploading"!==i&&"解析中"!==a){if("error"===i)return s.jsx(t,{type:"link",className:"reqularText",onClick:()=>o(l),children:"重新上传"});if("解析成功"===a)return s.jsx(t,{type:"link",className:"reqularText",children:"详情"});if("解析失败"===a)return s.jsx(t,{type:"link",className:"reqularText",children:"重新解析"})}return null};return s.jsxs("div",{className:i,children:[d.map(((e,l)=>s.jsxs(u,{className:"upload-list-item",gutter:16,style:{position:"relative"},children:[s.jsx(h,{span:5,children:s.jsxs("div",{style:{width:"100%",display:"flex",gap:12},children:[s.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M3.33329%2014.6666H12.6666C13.0348%2014.6666%2013.3333%2014.3681%2013.3333%2013.9999V4.66658H9.99996V1.33325H3.33329C2.9651%201.33325%202.66663%201.63173%202.66663%201.99992V13.9999C2.66663%2014.3681%202.9651%2014.6666%203.33329%2014.6666Z'%20fill='%23F1F4F9'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M10%201.33325L13.3333%204.66659'%20stroke='%23AEB9CA'%20stroke-width='1.5'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e"}),s.jsx("div",{className:"reqularText",style:{fontSize:"14px",fontWeight:"500",textDecoration:"underline",textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap"},children:e.name})]})}),s.jsx(h,{span:9,children:s.jsx(y,{tagList:e.tags||[],dataSetId:e.dataSetId,onChange:s=>{a(e.dataSetId,s)},showAddBtn:!1,AddBtnLabel:"添加标签"})}),s.jsx(h,{span:2,children:s.jsx("div",{style:{minWidth:"60px"},className:"reqularText",children:s.jsx("label",{className:"error"===e.status?"color-dimgray error-info":"color-dimgray",children:e.status?b[e.status]:"平台库"})})}),s.jsxs(h,{span:6,children:[s.jsx("div",{style:{width:"130px"},children:e.parseState}),s.jsx("div",{style:{width:"90px"},children:s.jsx(m,{file:e,index:l})})]}),s.jsx(h,{span:2,style:{textAlign:"right"},children:s.jsx(t,{type:"text",size:"small",onClick:()=>{r(l)},children:s.jsx(p,{})})}),"uploading"===e.status?s.jsx(v,{}):""]},e.dataSetId))),s.jsx(f,{visible:x,OnClose:e=>{j(!1)}})]})};export{y as T,w as U};

import{j as e,ar as t,r as l,k as s,as as i,S as a,B as n,n as d,f as r,I as o,al as c,D as u,e as m,am as h,c as x,d as j,ab as p,af as b,t as g,K as v,y}from"./react-core-BrQk45h1.js";import{O as f,T as k}from"./TimeModal-CKvOYePi.js";/* empty css                          */import{i as N}from"./info-icon-DzE4dkR7.js";import{w as S}from"./warningIcon-B4XoRFEk.js";import{j as C,k as A,o as w,s as F,g as I,i as B,h as T,l as W,m as z}from"./modle-Dr2iwiy1.js";import{n as D}from"./utils-DuyTwmHT.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";const O=({children:l})=>e.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center"},children:[e.jsx("div",{className:"descriptions",children:e.jsx(t,{column:1,style:{padding:"1rem 0rem 2rem 0.6rem"},size:"small",items:l})}),e.jsx("div",{style:{position:"absolute",bottom:"46px",right:"25%"}})]}),V=()=>{const[t,V]=l.useState(!1),[E,K]=l.useState(!1),[U,R]=l.useState(!1),[H,M]=l.useState(!1),[Q,J]=l.useState(!1),[Z,G]=l.useState(!1),[L,P]=l.useState(!1),[Y,q]=l.useState(""),[X,$]=l.useState(""),[_,ee]=l.useState(""),[te,le]=l.useState(""),[se,ie]=l.useState([]),[ae,ne]=l.useState([]),[de,re]=l.useState(!1),oe=s(),[ce,ue]=l.useState([]),[me,he]=l.useState([]),[xe,je]=l.useState(new Object),[pe,be]=l.useState(new Object),[ge,ve]=l.useState(new Object),[ye,fe]=l.useState(new Object),[ke,Ne]=l.useState({page:1,size:20,total:0}),[Se,Ce]=l.useState("modelName"),[Ae,we]=l.useState(""),[Fe,Ie]=l.useState(""),[Be,Te]=l.useState(!1),[We,ze]=l.useState(""),[De,Oe]=l.useState(""),[Ve,Ee]=l.useState("BASE_MODEL"),[Ke,Ue]=l.useState("FINE_TUNING"),[Re,He]=l.useState(""),[Me,Qe]=l.useState(0),Je=l.useRef(),[Ze,Ge]=l.useState(1),[Le,Pe]=l.useState(1),[Ye,qe]=l.useState(""),[Xe,$e]=l.useState(!1),[_e,et]=l.useState(!1),[tt,lt]=l.useState({}),[st,it]=l.useState(0),[at,nt]=l.useState({}),[dt,rt]=l.useState({}),ot=xe.modelName,ct=e.jsxs("span",{style:{lineHeight:"25px"},children:["模型微调流程:",e.jsx("br",{}),"1.选定合适基座模型",e.jsx("br",{}),"2.调整训练配置",e.jsx("br",{}),"3.执行训练任务",e.jsx("br",{}),"4.模型部署应用"]});function ut(){if(!Be){Te(!0);const e={...ke,category:0,sortAttribute:Se,sortDirection:Ae,modelName:"modelName"===Se?We:"",status:""};I(e).then((e=>{var t,l,s;Te(!1),200===(null==(t=e.data)?void 0:t.code)&&(ue(null==(l=e.data)?void 0:l.data),Ne({...ke,total:null==(s=e.data)?void 0:s.totalCount}),Qe((ke.page-1)*ke.size))}))}}function mt(){if(!Be){Te(!0);const e={...ke,category:1,sortAttribute:Se,sortDirection:Fe,modelName:"modelName"===Se?De:"",status:Re};I(e).then((e=>{var t,l,s,i;if(Te(!1),200===(null==(t=e.data)?void 0:t.code)){const t=null==(l=e.data)?void 0:l.data;he(t),Ne({...ke,total:null==(s=e.data)?void 0:s.totalCount}),Qe((ke.page-1)*ke.size);(null==(i=e.data)?void 0:i.data.some((e=>2===e.status)))?localStorage.setItem("isOnline","1"):localStorage.setItem("isOnline","0")}}))}}l.useEffect((()=>{ut(),mt()}),[]),l.useEffect((()=>(clearInterval(Je.current),ut(),Je.current=setInterval(ut,5e3),()=>{Je.current&&clearInterval(Je.current)})),[ke.page,ke.size,Ae,Se,We]),l.useEffect((()=>(clearInterval(Je.current),mt(),Je.current=setInterval(mt,5e3),()=>{Je.current&&clearInterval(Je.current)})),[ke.page,ke.size,Fe,Se,Re,De]);const ht=ce,xt=me,jt=e=>7===e?"在线":6===e?"上线中":5===e?"离线":4===e?"停止训练":2===e?"训练中":3===e?"训练失败":8===e?"上线失败":"",pt=e=>{const t=ht.find((t=>t.id===e)),l=xt.find((t=>t.id===e));return t?t?t.modelName:"":l?l?l.modelName:"":void 0},bt=e=>1e-5===e?"1e-5":5e-5===e?"5e-5":1e-4===e?"1e-4":5e-4===e?"5e-4":.001===e?"1e-3":void 0,gt=[{key:"1",label:"模型ID",children:xe.id},{key:"2",label:"模型类型",children:0===xe.category?"基座模型":"微调模型"},{key:"3",label:"参数量",children:xe.parameter},{key:"4",label:"模型介绍",children:xe.introduction},{key:"5",label:"适用场景推荐",children:xe.scene},{key:"6",label:"服务器名称",children:pe.serverName}],vt=[{key:"1",label:"模型ID",children:xe.id},{key:"2",label:"模型介绍",children:xe.introduction},{key:"3",label:"数据集",children:(null==ye?void 0:ye.datasets)||"-"},{key:"4",label:"数据集比例",children:(null==ye?void 0:ye.datasetRadio)?100*(null==ye?void 0:ye.datasetRadio)+"%":"-"},{key:"5",label:"基座模型",children:pt(null==ye?void 0:ye.modelBaseId)||"-"},{key:"6",label:"训练策略",children:(null==ye?void 0:ye.trainStrategy)||"-"},{key:"7",label:"迭代次数",children:(null==ye?void 0:ye.interationNumber)||"-"},{key:"8",label:"批次大小",children:(null==ye?void 0:ye.batchSize)||"-"},{key:"9",label:"学习率",children:bt(null==ye?void 0:ye.learnRate)||"-"},{key:"10",label:e.jsx("span",{style:{color:"#000000",fontWeight:"bold"},children:"模型状态"}),children:jt(null==ye?void 0:ye.status)||"-"},{key:"11",label:e.jsx("span",{style:{color:"#000000",fontWeight:"bold"},children:"训练进度"}),children:(null==ye?void 0:ye.trainProgress)?e.jsx(e.Fragment,{children:e.jsx(i,{percent:Number(null==ye?void 0:ye.trainProgress),type:"line"})}):"-"}],yt=[{key:"1",label:"模型ID",children:xe.id},{key:"2",label:"模型介绍",children:xe.introduction||"-"},{key:"3",label:"数据集",children:(null==ye?void 0:ye.datasets)||"-"},{key:"4",label:"数据集比例",children:(null==ye?void 0:ye.datasetRadio)?100*(null==ye?void 0:ye.datasetRadio)+"%":"-"},{key:"5",label:"基座模型",children:pt(null==ye?void 0:ye.modelBaseId)||"-"},{key:"6",label:"训练策略",children:(null==ye?void 0:ye.trainStrategy)||"-"},{key:"7",label:"迭代次数",children:(null==ye?void 0:ye.interationNumber)||"-"},{key:"8",label:"批次大小",children:(null==ye?void 0:ye.batchSize)||"-"},{key:"9",label:"学习率",children:bt(null==ye?void 0:ye.learnRate)||"-"},{key:"10",label:e.jsx("span",{style:{color:"#000000",fontWeight:"bold"},children:"模型状态"}),children:jt(null==ye?void 0:ye.status)||"-"},{key:"11",label:e.jsx("span",{style:{color:"#000000",fontWeight:"bold"},children:"中断原因"}),children:(null==ye?void 0:ye.stopReason)||"-"},{key:"12",label:e.jsx("span",{style:{color:"#000000",fontWeight:"bold"},children:"训练进度"}),children:(null==ye?void 0:ye.trainProgress)?e.jsx(e.Fragment,{children:e.jsx(i,{percent:Number(null==ye?void 0:ye.trainProgress),type:"line"})}):"-"}],ft=(e=>{const t=y();return e=>{z(e).then((l=>{var s,i;if(200===(null==(s=l.data)?void 0:s.code)){const s=null==(i=l.data)?void 0:i.data;oe("/main/finetune/create"),t({type:"SET_TRAINING_DATA",payload:{modelId:e,modelConfigData:s}})}}))}})();return e.jsxs(a,{children:[e.jsxs("div",{className:"createModelContent",children:[e.jsxs("div",{className:"mediumText",style:{fontSize:"28px",lineHeight:"36px",fontWeight:"500",marginLeft:"2rem",justifyContent:"space-between",display:"flex"},children:[e.jsx("div",{children:" 模型仓库 "}),e.jsxs("div",{children:[e.jsx(n,{type:"primary",size:"large",shape:"round",style:{backgroundColor:"black",fontSize:"14px",fontWeight:"700",margin:"0 1rem 0 0"},className:"boldText",onClick:()=>{oe("/main/finetune/serverManagement")},children:"服务器管理"}),e.jsx(n,{type:"primary",size:"large",shape:"round",style:{backgroundColor:"black",fontSize:"14px",fontWeight:"700",margin:"0 1rem 0 0"},className:"boldText",onClick:()=>{oe("/main/finetune/create")},children:"微调训练"}),e.jsx(d,{title:ct,children:e.jsx("img",{src:N,style:{width:"16px",height:"16px"}})})]})]}),e.jsxs("div",{className:"pedestal-info",children:[e.jsxs("div",{style:{width:"100%",textAlign:"start",justifyContent:"space-between",display:"inline-flex",marginBottom:"1.5rem",alignItems:"center"},children:[e.jsxs(r,{size:"small",children:[e.jsx("div",{style:{margin:"0 1rem 0 0",width:"64px",height:"19px",fontWeight:"500",fontSize:"16px"},children:"基座模型"}),e.jsx(r.Compact,{children:e.jsx(o,{size:"large",className:"filter-input",suffix:e.jsx(c,{}),value:We,onChange:e=>{ze(e.target.value)},placeholder:"请输入基座模型名称"})}),e.jsx(u,{menu:{items:[{key:"verifyUser",label:"按模型名称",children:[{key:"2-1",label:"正序"},{key:"2-2",label:"倒序"}]}],onClick:e=>{"2-1"===e.key?we("asc"):"2-2"===e.key&&we("desc")}},children:e.jsxs(n,{className:"default-btn",style:{width:124,backgroundColor:"#fff",fontSize:"14px",fontWeight:"400",margin:"0 0 0 1rem",color:"#111111",border:"1px solid #D7DDE5"},children:["筛选",e.jsx(m,{})]})}),e.jsx(n,{className:"default-btn",style:{backgroundColor:"#fff",fontSize:"14px",fontWeight:"400",margin:"0 0 0 1rem",color:"#111111",border:"1px solid #D7DDE5"},onClick:()=>{oe("/main/finetune/addBaseModel")},children:"新增基座模型"})]}),e.jsx("div",{children:`共${ht.length}个`})]}),e.jsx("div",{className:"baseModelArea",children:e.jsxs("div",{className:"scrollbar",children:[0===ht.length&&e.jsx(h,{}),e.jsx(x,{gutter:[20,36],wrap:!0,children:ht.map((l=>e.jsx(j,{span:6,children:e.jsxs(p,{className:"行至"===l.modelName.slice(0,2)?"purpleBlue":"otherColor",bordered:!1,headStyle:{borderBottom:"none"},style:{wordWrap:"break-word",height:"177px",width:"320px",color:"#FFFFFF"},children:[e.jsx(d,{title:l.modelName,children:e.jsx("div",{className:"baseModelNameTitle",children:l.modelName})}),e.jsx(d,{title:l.introduction,children:e.jsx("div",{className:"baseModelIntroduction",children:l.introduction})}),e.jsx(n,{style:{position:"absolute",left:"80%",bottom:"7px",color:"#FFFFFF"},type:"text",onClick:()=>{var e;(e=>{const t=ht.find((t=>t.id===e)),l=t?t.modelName:"",s=t?t.id:"";q(l),$(s)})(l.id),e=l.id,B(e).then((e=>{var t,l,s,i;if(200===(null==(t=e.data)?void 0:t.code)){const t=null==(l=e.data)?void 0:l.data.modelInfo,a=null==(s=e.data)?void 0:s.data.serverResponse,n=null==(i=e.data)?void 0:i.data.trainDetail;je(t),be(a),fe(n)}})),t||V(!0)},children:"详情"}),e.jsx("div",{style:{position:"relative"},children:"行至"===l.modelName.slice(0,2)&&e.jsx("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAlCAYAAAAuqZsAAAAAAXNSR0IArs4c6QAAAvJJREFUWEfNmGlrFEEQhp/X+8Zb8RYUPygqigciCCL+ZMEvguCBaNSAQgzxwlwQMAleaKIltfQsvZO5ejOr6U+7zNszT1d1VVe1WKFDZVxm5s8OADuBX8AHST+r1mFmW4H1wLykhbo1m9l2YAvwTdJsrC8EM7NVwCVgTyT+DTyWNJf/YNBfAPaHZ64dlfS2CK5A77IZ4KmkP/5nCVgJVPb+WUkP44/V6IckTSXofTGjS8BqPpK9/062qgb6CUkvsokN9HOSHvSANZjk+gVJd/1HQ/2UpKEE/WdJj7pgDT/i+jFJIwn6l5LGE/SvJb2PwQ4B52uiaBJwt/i+zAdG0dRJSc8ToDrvl2Qx2FngSAVYMlTqImKoGOwUcLIE7J9DxWAbgRvAmhzcf4HKR+UO4DTg2diz9kfgTcqeWq77KjO/b9YsT4Uwv5I7AQo3eptQhZk/l6X9SHKwqtGXu7PoK3tx6SEerHU8uLds/kCgmlhsL3C5zWits1T2rTqL+fOrwK42orUpVK3FgjtXA57n9gGLgLvvXWq0pkA1AityY7/HTE0Q9TyudGUbUMGyJ0I17Oeg12deDHQKwr6iMj8p1VIV7p6W9KwVsBahMp77kr4sy2IDgHKeZ5Km+wYbEJTz3JP0vS+wAUJ9kjQc0pEHoPLBUNVXFrVwRQvs61gKgXEGOAh4rvS+0ivYjhVT+sq2oYrK8x+AB8Vial8Zwy3HUlU9Q6eB6QEb4J5KWcSIpLEu2AqBco88kTQTg3lZ7fVX1ei2WGZW1cBk74j1frfhlzRVo9u5x2C3gA0Vs3r6PjO7HvqDsikx1DrgdtNF90SlmVWB9UCF/HMtXFGVRmtW6pjZJuBm00Xnwc4BhwsmL4EKYMcAz0P5UbQI94yDeZtYq8+DeU/pZbRf1GVjHBguKvLCxZ6DHY303vK9KtF7mX4xJNMlezBPW5THdodbwa+S5mv2hd/6uBU2h1tBT5Clw8y2hQBYC/jNzkSZOLlQrANt6/lfAhiWNVRjcXMAAAAASUVORK5CYII=",style:{position:"absolute",left:"88%",bottom:"35px",width:"36px",height:"35px"}})})]})},l.id)))})]})})]}),e.jsxs("div",{className:"myModelArea",children:[e.jsxs("div",{style:{width:"100%",textAlign:"start",justifyContent:"space-between",display:"inline-flex",marginBottom:"1.5rem",alignItems:"center"},children:[e.jsxs(r,{size:"small",children:[e.jsx("div",{style:{margin:"0 1rem 0 0",width:"64px",height:"19px",fontWeight:"500",fontSize:"16px"},children:"我的模型"}),e.jsx(r.Compact,{children:e.jsx(o,{size:"large",className:"filter-input",suffix:e.jsx(c,{}),onChange:e=>{Oe(e.target.value)},placeholder:"请输入我的模型名称"})}),e.jsx(u,{menu:{items:[{key:"moduleName",label:"按模型名称",children:[{key:"1-1",label:"正序"},{key:"1-2",label:"倒序"}]},{key:"trainingStatus",label:"按训练状态",children:[{key:"2-1",label:"在线"},{key:"2-2",label:"离线"},{key:"2-3",label:"训练中"},{key:"2-4",label:"训练失败"}]}],onClick:e=>{"1-1"===e.key?(He(""),Ie("asc")):"1-2"===e.key&&(He(""),Ie("desc")),"2-1"===e.key?He("7"):"2-2"===e.key?He("5"):"2-3"===e.key?He("2"):"2-4"===e.key&&He("3")}},children:e.jsxs(n,{className:"default-btn",style:{width:124,height:"40px",fontSize:"14px",justifyContent:"center",margin:"0 0 0 1rem"},children:["筛选",e.jsx(m,{})]})})]}),e.jsx("div",{children:`共${xt.length}个`})]}),e.jsxs("div",{style:{minHeight:"159px"},className:0===xt.length?"empty":"",children:[0===xt.length&&e.jsx(h,{}),e.jsx(x,{gutter:[20,36],wrap:!0,children:xt.map((l=>e.jsx(j,{span:6,children:!at[l.id]||3!==l.status&&0!==l.status&&4!==l.status?!dt[l.id]||2!==l.status&&0!==l.status?e.jsxs(p,{className:7===l.status?"islive":6===l.status?"goingLive":5===l.status?"done":4===l.status||2===l.status?"training":"interrupted",bordered:!1,headStyle:{borderBottom:"none"},style:{wordWrap:"break-word",height:"185px",width:"320px",color:"#FFFFFF"},children:[e.jsx(d,{title:l.modelName,children:e.jsx("div",{className:"modelNameTitle",children:l.modelName})}),e.jsx(d,{title:l.introduction,children:e.jsx("div",{className:"introduction",children:l.introduction})}),e.jsxs("div",{className:"statusInfo",children:[e.jsxs("div",{className:5===l.status?"modelStatus":"otherStatus",children:[e.jsx("span",{className:7===l.status?"liveround":6===l.status?"goliveround":5===l.status?"interruptedround":2===l.status?"traninground":"doneround"}),jt(l.status)]}),e.jsx("div",{className:"actionInfo",children:7===l.status?e.jsxs("div",{className:"prolong",children:[e.jsx(n,{type:"text",className:"btdStyle",onClick:()=>{t||(A(l.id).then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)&&sessionStorage.setItem("config",JSON.stringify(e.data.data))})),oe(`/main/finetune/adjustment/${l.id}`))},children:"调试 |"}),e.jsx(n,{type:"text",className:"btdStyle",onClick:()=>{const e=pt(l.id);oe(`/main/finetune/configure/${l.id}/${e}/${l.status}`)},children:"配置|"}),e.jsx(n,{type:"text",className:"btdStyle",onClick:()=>{pt(xe.modelBaseId);const e=pt(l.id);oe(`/main/finetune/detail/${l.id}/${e}`)},children:"详情"})]}):5===l.status||8===l.status?e.jsxs("div",{className:"donetext",children:[e.jsx(n,{type:"text",className:"btdStyle",onClick:()=>{const e=pt(l.id);oe(`/main/finetune/configure/${l.id}/${e}/${l.status}`)},children:"配置|"}),e.jsx(n,{type:"text",className:"btdStyle",onClick:()=>{const e=pt(l.id);oe(`/main/finetune/detail/${l.id}/${e}`)},children:"详情"})]}):3===l.status||4===l.status?e.jsxs("div",{className:"Interrupted",children:[e.jsx(n,{type:"text",className:"btdStyle",onClick:()=>{nt((e=>({...e,[l.id]:!0}))),$(l.id)},children:"重新训练"}),e.jsx(n,{type:"text",className:"btdStyle",onClick:()=>(async e=>{try{const t=Number(e);await T(t)?g.success("删除成功"):g.error("删除失败")}catch(t){g.error("删除失败")}})(l.id),children:"|删除"})]}):2===l.status?e.jsx("div",{className:"training1",children:e.jsx(n,{type:"text",className:"btdStyle",onClick:()=>{rt((e=>({...e,[l.id]:!0})))},children:"停止训练"})}):null})]})]}):e.jsx(e.Fragment,{children:e.jsxs(p,{className:"interrupted",bordered:!1,headStyle:{borderBottom:"none"},style:{wordWrap:"break-word",height:"170px",width:"230px",color:"#FFFFFF"},title:e.jsxs("span",{children:[e.jsx(b,{style:{color:"red",fontSize:"16px"}})," ","提示"]}),children:[e.jsx("div",{className:"text",children:"确定要停止训练吗？"}),e.jsxs("div",{className:"btn",children:[e.jsx(n,{onClick:()=>{rt((e=>({...e,[l.id]:!1})))},children:"取消"}),e.jsx(n,{onClick:()=>{C(Number(l.id)),rt((e=>({...e,[l.id]:!1}))),g.success("模型训练正在停止...")},children:"确定"})]})]})}):e.jsxs(p,{className:"interrupted",bordered:!1,headStyle:{borderBottom:"none"},style:{wordWrap:"break-word",height:"185px",width:"320px",color:"#FFFFFF"},title:e.jsxs("span",{children:[e.jsx(b,{style:{color:"red",fontSize:"16px"}})," ","提示"]}),children:[e.jsx("div",{className:"text",children:"确定要重新训练吗？"}),e.jsxs("div",{className:"btn",children:[e.jsx(n,{onClick:()=>{nt((e=>({...e,[l.id]:!1})))},children:"取消"}),e.jsx(n,{onClick:()=>{ft(l.id),nt((e=>({...e,[l.id]:!1})))},children:"确定"})]})]})},l.id)))})]})]})]}),e.jsx(f,{visible:t,titltext:Y,moduletext:ot,buttontext:"微调训练",logo:!0,text:!0,isDelete:!0,tooltip:ct,onButtonClick:()=>{oe("/main/finetune/create",{state:{modelName:Y,modelId:X}})},OnClose:()=>{V(!1)},deleteClick:()=>{re(!0)},children:e.jsx(O,{children:gt})}),e.jsx(v,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx("img",{src:S,alt:"警告",className:"warning"}),e.jsx("span",{style:{marginLeft:8},children:"提示"})]}),centered:!0,open:de,closable:!1,width:520,className:"delete-model",footer:[e.jsx(n,{className:"modal-btn",onClick:()=>re(!1),children:"取消"}),e.jsx(n,{className:"modal-btn",onClick:()=>{W(X).then((e=>{200===(null==e?void 0:e.code)?(g.success("删除成功"),ut(),V(!1)):g.error("删除失败"),re(!1)}))},children:"确定"})],wrapClassName:"custom-modal",children:e.jsx("p",{children:"确定要删除模型吗？"})}),e.jsx(k,{visible:H,titltext:"选择上线时间",buttontext:"确认上线",timetext:"持续",onButtonClick:()=>(w(X),Ge(1),void M(!1)),value:Ze,OnClose:()=>{M(!1)},onValueChange:Ge,children:e.jsx(e.Fragment,{})}),e.jsx(k,{visible:Q,titltext:"延长上线时间",buttontext:"确认延长",timetext:"延长",onButtonClick:()=>{J(!1)},OnClose:()=>{J(!1)},children:e.jsx(e.Fragment,{})}),e.jsxs(v,{width:429,title:e.jsxs("div",{className:"modalTitle",children:[e.jsx("img",{src:S,style:{marginRight:"12px"},alt:""}),"模型体积信息"]}),centered:!0,open:Xe,footer:null,maskClosable:!1,onCancel:()=>$e(!1),children:[e.jsxs("div",{className:"lightWeightModal",children:[e.jsx("span",{className:"lightWeightContent origion",children:"原模型体积：10GB"}),e.jsxs("span",{className:"lightWeightContent",children:["轻量化模型体积：",7===D.status||tt[st]?"8GB":"待确认"]})]}),e.jsx("div",{className:"offmodelButton",children:e.jsx(n,{className:"offmodeltrue",disabled:7===D.status||tt[st],onClick:()=>{return e=st,void lt((t=>({...t,[e]:!0})));var e},children:"轻量化"})})]}),e.jsxs(v,{width:429,title:"模型下线",centered:!0,open:Z,footer:null,maskClosable:!1,onCancel:()=>G(!1),children:[e.jsx("div",{className:"offmodel",children:"确认模型下线"}),e.jsxs("div",{className:"offmodelButton",children:[e.jsx(n,{className:"offmodelCancel",onClick:()=>G(!1),children:"取消"}),e.jsx(n,{className:"offmodeltrue",onClick:()=>{G(!1),w(X).then((e=>{var t,l,s;200===(null==(t=e.data)?void 0:t.code)&&g.success((null==(s=null==(l=e.data)?void 0:l.data)?void 0:s.message)||"下线成功")}))},children:"确认下线"})]})]}),e.jsxs(v,{width:429,title:Ye,centered:!0,open:L,footer:null,maskClosable:!1,onCancel:()=>P(!1),children:[e.jsx("div",{className:"offmodel",children:"确认"+Ye}),e.jsxs("div",{className:"offmodelButton",children:[e.jsx(n,{className:"offmodelCancel",onClick:()=>P(!1),children:"取消"}),e.jsx(n,{className:"offmodeltrue",onClick:()=>{"模型训练"===Ye?F(X).then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)&&g.success("开始训练")})):"模型暂停"===Ye&&C(Number(X)).then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)&&g.success("暂停成功")})),P(!1)},children:"确认"})]})]}),e.jsx(f,{visible:E,titltext:Y,buttontext:"确定",logo:!1,text:!1,isbutton:!1,onButtonClick:()=>{K(!1)},OnClose:()=>{K(!1)},children:e.jsx(O,{children:vt})}),e.jsx(f,{visible:U,titltext:Y,buttontext:"确定",logo:!1,text:!1,isbutton:!1,onButtonClick:()=>{R(!1)},OnClose:()=>{K(!1)},children:e.jsx(O,{children:vt})}),e.jsxs(f,{visible:U,titltext:Y,buttontext:"重新训练",interruptText:"请结合中断原因及训练进度选择适合您的训练方式",isInterruptText:!0,logo:!1,text:!1,isbutton:!1,onButtonClick:()=>{F(X).then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)?(g.success("开始训练"),R(!1)):R(!1)}))},OnClose:()=>{R(!1)},children:[e.jsx(O,{children:yt}),e.jsx("div",{style:{position:"absolute",bottom:"103px",left:"18%"},children:e.jsx(d,{title:"请结合中断原因及训练进度选择适合您的训练方式",children:e.jsx("img",{src:N,style:{width:"16px",height:"16px"}})})})]})]})};export{V as default};

// import { StyleInfo } from "antd/es/theme/util/genComponentStyleHook";
import { UserType } from "./api/user";
// import exp from "constants";

export interface FingerPrintType {
  ip: string;
  device: string;
  browser: string;
  os: string;
}

export interface CustomResponseType {
  code: number;
  data: any;
  message?: string;
  userName: string;
}

export interface RegisterParams {
  userName: string;
  account: string;
  password: string;
  verificationCode: string;
  registerType: string;
  fingerPrint?: FingerPrintType;
}

export interface LoginParams {
  account: string;
  password: string;
  fingerPrint?: FingerPrintType;
}

export interface UserInfoParams {
  userName: string,
  fingerPrint?: FingerPrintType;
}

export interface VerifyCodeLoginParams {
  account: string;
  verificationCode: string;
  loginType: string;
  fingerPrint?: FingerPrintType;
}

export interface VerifyCodeParams {
  account: string;
  loginType: string;
  fingerPrint?: FingerPrintType;
}

export interface RegisterCodeParams {
  account: string;
  registerType: string;
  fingerPrint?: FingerPrintType;
}

export enum VerifyCodeTypeEnum {
  Login,
  Register,
}

export interface DatasetFilter {
  dataSetName: string;
  tag: string;
  startTime: string;
  endTime: string;
}

export interface DataSetType {
  id: string;
  name: string;
  taskName: string;
  datasetName: string;
  createTime: number;
  datasetStatus: string;
  progress: number;
  tags: string[];
  owner: string;
  relatedQATaskList: RelatedTaskType[];
  childFiles: string[];
  complete: number;
  total: number;
  failedReason: string;
  tag: string;
  startTime: string;
  endTime: string;
}

export interface ModelSetType {
  totalCount?: number;
  id: string;
  introduction: string;
  modelName: string;
  status: number;
  trainTaskId: string;
}

export interface RelatedTaskType {
  taskId: string;
  taskName: string;
}

export interface SplitDataType {
  text: string; //需要切分的原文内容
  sentence_max_len?: number; //单个段落的最大字数，不填默认200
}

export interface GenerateQaType {
  file_id: number; //需要生成QA生成结果的原始文档，必填
  para_id?: number; //需要生成QA生成结果的具体段落，不填默认全部
  model?: string; //需要使用哪个模型生成问题，不填默认glm2
}

export interface SearchQaType {
  file_id: number; //需要查询QA生成结果的原始文档，必填
  para_id?: number; //需要查询QA生成结果的具体段落，不填默认全部
}

export interface FileType {
  content: string; // 上传文件中提取到的文本内容
  file_id: number; // 上传文件在数据库中的id
  file_name: string; // 上传文件的文件名
}

export interface SplitType {
  content: string; // 当前段落的文本内容
  para_id: number; // 当前段落的id
}

// export interface GenerateQaResultType {
//   file_id: number; //本次查到QA的来源文档
//   para_id?: number; //本次查到QA的来源段落
//   para_text: string; //来源段落的具体内容
//   qas_list: QaResultType[]; //QA结果数组
//   num_qa_per_para: number; //QA结果个数
// }

export interface ParsingResultType {
  file_id: number; // 上传文件在数据库中的id
  file_name: string; // 上传文件的文件名
  progress_percent: number; //解析进度
}

/* redux 类型定义 */
// action的类型
export interface ActionType {
  type: string;
  payload?: any;
}

export interface TaskType {
  taskId: string;
  dataset_id: string;
  // index: number;
  taskName: string;
  createTime: number;
  status: string;
  reviewCount?: number; //目前不显示
  // description: string;
  paragraphValue?: number; // 0- auto
  questionDensity?: number; //0- auto
  qaCount?: number;
  failedReason?: string; //目前不显示
  complete?: number;
  total: number;
  curAllocated?: number;
  userId: string;
  totalProgress?: number;
  curProgress?: number;
  userRole: string;
  reviewConfigId?:any

  reviewers: UserType[];
  creator: UserType;
  buttonDisabled:boolean
  description: string;
}

export interface AlluserInfoType {
  createTime: string,
  gender: number,
  id: string,
  isDelete: number,
  updateTime: string,
  userAccount: string,
  userAvatar: string,
  userName: string,
  userPassword: string,
  userRole: string
}

export const TaskStatus = {
  //success: "SUCCESS",
  //success: "SUCCEEDED",
  success: "SUCCESS",
  inProgress: "PROCESSING",
  failed: "FAILED",
  error: "ERROR"
};

export const DataSetStatus = {
  sucess: "SUCCESS",
  inProgress: "PARSING",
  failed: "ERROR",
};

export interface ParseDatasetType {
  "fileId": string;
  "fileName": string;
  "fileType": string;
  "parseFilePath": string;
  "srcFilePath": string;
}

export enum DatasetFileEnum {
  Folder,
  Txt,
  Doc,
  Pdf,
  Csv,
}

// export interface DisplayQaResultType extends QaResultType {
//   id: number;
//   source_text: string;
//   task_id: number;
//   data_set_id: number;
//   checked: boolean;
//   expend?: boolean;
//   reviewed?: boolean;
// }

export interface TaskFilter {
  taskName: string;
  startTime?: Date;
  endTime?: Date;
}

export interface CreateTaskFieldType {
  taskName: string;
  datasetList: string[];
  taskConfigMap: {
    splitLevel: number; //对应prd中的6个句子分组
    questionDensity: number; //对应prd中的11~20个问题/组
  };
  description: string;
  domain: string;
  tags?: string[];
}

export interface TaskDetailType {
  taskStatus: string;
  taskCreatorId: string;
  reviewConfigId: string;

  id: string;
  densityLevel: number;
  splitLevel: number;
  description: string;
  domain: string;
  // pairs: DocumentContentPairType[];
  // childFiles: TaskDetailFiles[];
  fileIdList: string[];
  taskName: string;
  taskProgress: number;
  taskStatusEnum: string; //FAILED,PROCESSING,SUCCEEDED
  taskId: number;
  complete: number;
  total: number;
  qaCount: number;
  reviewCount: number;
  curAllocated: number;
  updateTime: string;
  createTime: string;
  taskStartTime: number;
  taskEndTime: number;
  userRole: string;
}

interface ProgressUser {
  userAvatar: string,
  userName: string,
}
export interface ProgressAllInfo {
  total: number,
  totalReviewed: number,
  curAllocated: number,
  curReviewed: number,
  totalProgress: number,
  curProgress: number,
  user: ProgressUser,
  code: number,
  length: number
}

export interface TaskDetailFiles {
  fileName: string;
  childFiles: string[];
}

export interface DocumentContentPairType {
  fileContent: FileContent;
  qaDocumentList: QADocument[];
  splitLevel: number;
  taskCreatorId: string;
  childFileId: string;
}

export interface FileContent {
  content: string;
  id: string;
  fileName: string;
}

export interface QADocument {
  id: string;
  question: string;
  answer: string;
  fileContentId: string;
  highlightIdxList: HighlightIdx[];
  verifyUserId: string;

  review: boolean;
  modify: boolean;

  score: string;
  // 102
  allocated: boolean;
  allocatedUserId: string;
  fileId: string;

}

export interface AllocatedQA {
  code: number,
  data: QADocument[];
}

export interface HighlightIdx {
  end: number;
  start: number;
  score:number
}

export interface PaginationType {
  page: number;
  size: number;
  total: number;
}

export interface DataSetTreeType {
  id: string;
  name: string;
  status: string;
  qaCount?: number;
  fileName: string;
  fileId: string;
  fileType: number;
  srcFilePath: string;
  datasetName: string;
  parseFilePath: string;
  type: string; //ZIP DIR FILE
  children?: DataSetTreeType[];
  childFileId?: string;
  childFilePath?: string;
}

export interface TaskDeleteReviewType extends RelatedTaskType {
  datasetName: string;
  dataSetId: string;
}

export interface ModelDetailType {
  baseModel: string;
  batchSize: number;
  createTime: string;
  datasetTestProp: string;
  duration: string;
  evaluateScore: string;
  evaluateStatus: string;
  id: string;
  iterationRound: number;
  lastOnlineTime: string;
  learningRate: string;
  modelCategory: string;
  introduction: string;
  modelName: string;
  parameter: string;
  scene: string;
  modelType: string;
  onlineCount: number;
  onlineTime: string;
  publishConfig: string;
  trainStatus: string;
  trainStrategy: string;
  updateTime: string;
  userId: string;
  category: number;
  modelInfo:{};
  serverResponse:{};
  trainDetail:{};
  modelRadar:{
    data:{}
  };
}

export interface trainDetail{
  batchSize: number;
  datasets:[];
  modelBaseId: number;
  trainStrategy: string;
  interationNumber: number;
  learnRate: number;
  trainProgress: number;
  stopReason: string;
}

export interface modelInfo {
  id: string;
  iterationRound: number;
  lastOnlineTime: string;
  learningRate: string;
  modelCategory: string;
  introduction: string;
  modelName: string;
  parameter: string;
  scene: string;
  modelType: string;
  onlineCount: number;
  onlineTime: string;
  publishConfig: string;
  trainStatus: string;
  trainStrategy: string;
  updateTime: string;
  userId: string;
  category: number;
}


export interface UpdateModelType {
  modelId: string;
  modelIntro: string;
  modelName: string;
  modelScene: string;
}export interface qATaskRetryRequest {
  taskId: string,
  fileIdList: string[],
}

export interface deleteillationTaskType {
  taskId: string,
  fileIdList: string[],
}
export interface trainingConfigType {
  trainStrategy: string;
  iterationRound: number;
  batchSize: number;
  learningRate: string;
}

export interface modelOnlineType {
  id: string,
  status: string,
  time: number
}
export interface ModelFailData {
  properties: {
      modelId: string;
      category: number;
      modelName: string;
      trainConfig: {
          id: string[];
          datasets: null | any;
          batchSize: number;
          learnRate: number;
          properties: null | any;
          baseModelId: number;
          datasetRadio: number;
          trainStrategy: string;
          interationNumber: number;
      };
      introduction: string;
      basicConfigRequest: {
          server: string;
          serverConfigId: number;
          computeResource: string[];
          trainingFramework: string;
      };
  };
}

import{r as e,j as s,B as a,y as r,k as l,A as t,S as i,c as n,d as o,p as d,I as c}from"./react-core-BrQk45h1.js";import{W as m,U as u,r as p}from"./WelcomePage-D5s039rc.js";import{l as j,r as g}from"./auth-BKEkOdcM.js";import{V as h}from"./types-ccCJOgIs.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./index-y6n7cw2i.js";import"./utils-DuyTwmHT.js";import"./redux-CXOBDlvD.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";const x=({params:r,disabled:l,type:t,onClick:i})=>{const[n,o]=e.useState(!1),[d,c]=e.useState(0);e.useEffect((()=>{let e;return n&&d>0?e=setInterval((()=>{c((e=>e-1))}),1e3):0===d&&o(!1),()=>{clearInterval(e)}}),[n,d]);return s.jsx(a,{shape:"round",block:!0,className:"sendVerifyCodeBtn boldText",onClick:()=>{i&&i(),r.account&&(c(60),o(!0),t===h.Login?j(r):g(r))},disabled:l||n||d>0,children:n?`重新发送(${d}s)`:"发送验证码"})},N=()=>{const j=r(),[g,N]=e.useState(""),[f,v]=e.useState(""),[y,b]=e.useState(""),[I,C]=e.useState(""),[w,P]=e.useState(!1),S=l(),[q]=t(),B=q.getAll("redirect")[0],k=B&&"/login"!==B&&"/register"!==B&&"/"!==B?B:"/overview",E=()=>{S(k)};return s.jsx(i,{style:{position:"inherit"},children:s.jsx("div",{className:"bg",children:s.jsxs(n,{justify:"start",children:[s.jsx(o,{span:12,className:"loginLf",children:s.jsx(m,{})}),s.jsx(o,{span:12,className:"loginRt",children:s.jsxs("div",{className:"loginBox",children:[s.jsx("h2",{className:"loginTitle",children:s.jsx("span",{className:"primary boldText",children:"行至账号-注冊"})}),s.jsxs(d,{className:"registerForm",requiredMark:!1,name:"loginDefault",style:{maxWidth:600},initialValues:{remember:!0,username:"",phoneNum:"",password:""},onFinish:e=>{j(p({userName:f,account:g,password:y,verificationCode:I,registerType:"PHONE_NUMBER"},E))},onFinishFailed:e=>{},autoComplete:"off",children:[s.jsx(d.Item,{label:"用户名",name:"userName",rules:[{required:!0,message:"请输入用户名"}],children:s.jsx(c,{placeholder:"请输入用户名",className:"loginInput",value:f,onChange:e=>v(e.target.value)})}),s.jsx(d.Item,{label:"手机号",name:"account",rules:[{required:!0,message:"请输入手机号"},{pattern:/^1[3456789]\d{9}$/,message:"手机号格式不正确"}],children:s.jsx(c,{className:"loginInput",placeholder:"请输入手机号",value:g,onChange:e=>N(e.target.value)})}),s.jsxs(n,{justify:"start",gutter:8,children:[s.jsx(o,{span:17,children:s.jsx(d.Item,{label:"验证码",name:"verificationCode",rules:[{required:!0,message:"请输入短信验证码"}],children:s.jsx(c,{className:"loginVerifyCodeInput",placeholder:"请输入短信验证码",value:I,onChange:e=>C(e.target.value)})})}),s.jsx(o,{span:7,children:s.jsx(x,{disabled:!1,params:{account:g,registerType:"PHONE_NUMBER"},type:h.Register})})]}),s.jsx(d.Item,{label:"密码",name:"password",rules:[{required:!0,message:"请输入密码"}],children:s.jsx(c.Password,{className:"loginInput",placeholder:"请输入密码",value:y,onChange:e=>b(e.target.value)})}),s.jsx(d.Item,{label:"确认密码",name:"confimPassword",dependencies:["password"],rules:[{required:!0,message:"请输入密码"},({getFieldValue:e})=>({validator:(s,a)=>a&&e("password")!==a?Promise.reject(new Error("输入的密码不匹配!")):Promise.resolve()})],children:s.jsx(c.Password,{placeholder:"请输入密码",className:"loginInput"})}),s.jsx(d.Item,{children:s.jsx(a,{type:"primary",shape:"round",block:!0,htmlType:"submit",className:"verifyloginBtn loginBtn",children:"注册并登录"})})]}),s.jsx(u,{isAgree:w,onIsAgreeChange:e=>{P(e)}})]})})]})})})};export{N as default};

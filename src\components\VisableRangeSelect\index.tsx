import { Avatar, Divider, Input, Select, Space, Tag } from "antd";
import { CustomTagProps } from "rc-select/lib/BaseSelect";
import avatar1 from '../../assets/img/avatar-1.png';
import avatar2 from '../../assets/img/avatar-2.png';
import avatar3 from '../../assets/img/avatar-3.png';
import avatar4 from '../../assets/img/avatar-4.png';
import avatar5 from '../../assets/img/avatar-5.png';
import avatar6 from '../../assets/img/avatar-6.png';
import { BaseOptionType } from "antd/es/select";
import { FlattenOptionData } from "rc-select/lib/interface";
import { Dispatch, useEffect, useState } from "react";
import classes from "./index.module.css";
import { useDispatch, useSelector } from "react-redux";
import { UserType } from "../../api/user";
import { getUserList } from "../../store/user/action";
import { addReviewUser, deleteReviewUser } from "../../api/review";
interface VisableRangeSelectProp {
    taskId: string;
    defaultSelectUser?: UserType[];
    onChange: (users: string[]) => void;
    className?: string;
}

const VisableRangeSelect: React.FC<VisableRangeSelectProp> = ({ taskId, defaultSelectUser, onChange, className }) => {
    const [selectUser, setSelectUser] = useState<string[]>([]);
    const dispath: Dispatch<any> = useDispatch();
    const userList = useSelector((state: { user: { userList: UserType[] } }) => state.user.userList);
    // const options = [{ value: 'gold' }, { value: 'lime' }, { value: 'green' }, { value: 'cyan' }];
    const colorList = ['pink',
        'red',
        'yellow',
        'orange',
        'cyan',
        'green',
        'blue',
        'purple',
        'geekblue',
        'magenta',
        'volcano',
        'gold',
        'lime'];
    const [searchValue, setSearchValue] = useState<string>();
    const avatar = [avatar1, avatar2, avatar3, avatar4, avatar5, avatar6];

    const options = () => {
        return userList?.map(user => {
            return { label: user.userName, value: user.id }
        })
    };

    useEffect(() => {
        if (userList.length === 0) {
            dispath(getUserList());
        }
    }, [])

    useEffect(() => {
        if (defaultSelectUser)
            setSelectUser((defaultSelectUser.map(item => item.id) as any[]));
    }, [defaultSelectUser])

    const convertToNumberInRange = (inputString: string, n: number): number => {
        if (!inputString || inputString.length === 0 || n <= 0) {
            // 处理空字符串或无效的上限值
            return -1;
        }

        // 将字符串的每个字符的 Unicode 码点相加，然后对上限值 n 取余
        const charCodeSum = inputString.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
        const result = charCodeSum % n;

        return result;
    };

    const calculateDifference = (arr1: string[], arr2: string[]): string[] => {
        // 使用 Set 来存储数组元素，确保唯一性
        const set2 = new Set(arr2);
    
        // 使用 filter 方法找到在 arr1 中但不在 arr2 中的元素
        const difference = arr1.filter(item => !set2.has(item));
    
        return difference;
      };

    const tagRender = (props: CustomTagProps) => {
        const { label, value, closable, onClose } = props;
        const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
            event.preventDefault();
            event.stopPropagation();
        };
        return (
            <Tag
                className={classes["tag-item"]}
                color={colorList[convertToNumberInRange(value, colorList.length - 1)]}
                onMouseDown={onPreventMouseDown}
                closable={closable}
                closeIcon={false}
                onClose={() => {
                    onClose();
                    deleteReviewUser(taskId, value);
                }}
            >
                <Space>
                    <Avatar size={24} src={avatar[convertToNumberInRange(value, avatar.length - 1)]} />
                    {label}
                </Space>
            </Tag>
        );
    };
    return <Select
        maxTagCount={5}
        showSearch={false}
        mode="multiple"
        tagRender={tagRender}
        value={selectUser}
        onChange={(e: string[]) => {
            console.log('users', e);
            if (e.length > selectUser.length) {
                const diff = calculateDifference(e, selectUser);
                diff.forEach(value => {
                    addReviewUser(taskId, value);
                });
            }
            setSelectUser(e);
            onChange(e);
        }}
        style={{ width: '100%' }}
        className={className ? className : classes['range-select']}
        options={options()}
        optionFilterProp={"label"}
        dropdownRender={(menu) => {
            return <>
                <Input
                    bordered={false}
                    placeholder="搜索团队成员"
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    onKeyDown={(e) => e.stopPropagation()}
                />
                <Divider style={{ margin: '8px 0' }} />
                {menu}</>
        }}
        searchValue={searchValue}
        optionRender={(option: FlattenOptionData<BaseOptionType>, info: { index: number }) => {
            return <Space>
                <Avatar size={24} src={avatar[convertToNumberInRange((option.value as string), avatar.length - 1)]} />
                {option.label}
            </Space>;
        }}
    />
}

export default VisableRangeSelect;
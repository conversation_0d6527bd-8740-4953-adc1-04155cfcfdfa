import{k as e,j as t,f as s,B as i,l,r as n,M as r,S as a,T as d,m as o,n as c,o as h,F as x,p as m,q as p,s as j,t as g}from"./react-core-BrQk45h1.js";import{r as u,m as v,t as f,a as y,s as C,b as S,c as T,d as k,e as w,f as N}from"./conts-BxhewW4k.js";import{g as I}from"./modle-Dr2iwiy1.js";import{a as F}from"./utils-DuyTwmHT.js";import{g as z}from"./group-151-0eGrjKh_.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";const E=e=>{const t=C.indexOf(e);return t>-1?25*t:0},b=e=>{const t=y.indexOf(e);return t>-1?25*t:0},M=e=>{const t=f.indexOf(e);return t>-1?25*t:0},A=e=>{const t=v.indexOf(e);return t>-1?25*t:0},L=e=>{const t=u.indexOf(e);return t>-1?25*t:0},V=(e,t)=>{var s;return(null==(s={temperature:{.5:0,.7:25,1:50,1.3:75,1.5:100},topK:{5:0,20:25,50:50,100:75,200:100},topP:{.6:0,.7:25,.8:50,.9:75,.95:100},maxTokens:{50:0,100:25,200:50,300:75,500:100},frequencyPenalty:{1:0,1.2:25,1.5:50,1.8:75,2:100}}[e])?void 0:s[t])??null},_=({saveSet:n})=>{const r=e();return t.jsxs(s,{size:20,style:{display:"inline-flex",alignItems:"center"},className:"headerSpace",children:[t.jsx(i,{style:{fontSize:"12px",width:"36px",height:"36px"},shape:"circle",icon:t.jsx(l,{}),onClick:()=>r("/main/finetune")}),t.jsx("div",{className:"mediumText",style:{fontSize:"28px",lineHeight:"36px",fontWeight:"500"},children:"调试模型"}),t.jsx(i,{type:"primary",size:"large",shape:"round",className:"publishButton",onClick:()=>n(),children:"保存"})]})},B="data:image/svg+xml,%3csvg%20width='24'%20height='29'%20viewBox='0%200%2024%2029'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20id='Group%20310'%3e%3cg%20id='Vector'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M6.71983%2011.7007C5.42666%2012.5484%204.95944%2013.4903%204.95944%2014.2439C4.95944%2014.9976%205.42666%2015.9394%206.71983%2016.7871C7.99577%2017.6235%209.87638%2018.2062%2012.0551%2018.2062C14.2338%2018.2062%2016.1144%2017.6235%2017.3904%2016.7871C18.6835%2015.9394%2019.1507%2014.9976%2019.1507%2014.2439C19.1507%2013.4903%2018.6835%2012.5484%2017.3904%2011.7007C16.1144%2010.8643%2014.2338%2010.2816%2012.0551%2010.2816C9.87638%2010.2816%207.99577%2010.8643%206.71983%2011.7007ZM4.5258%208.35382C6.54219%207.03199%209.20989%206.27971%2012.0551%206.27971C14.9003%206.27971%2017.568%207.03199%2019.5844%208.35382C21.5836%209.66436%2023.1527%2011.7042%2023.1527%2014.2439C23.1527%2016.7837%2021.5836%2018.8235%2019.5844%2020.134C17.568%2021.4558%2014.9003%2022.2081%2012.0551%2022.2081C9.20989%2022.2081%206.54219%2021.4558%204.5258%2020.134C2.52663%2018.8235%200.957511%2016.7837%200.957511%2014.2439C0.957511%2011.7042%202.52663%209.66436%204.5258%208.35382Z'%20fill='url(%23paint0_linear_487_2188)'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M6.72051%205.72624C5.42693%206.57552%204.95896%207.51915%204.95896%208.27347C4.95896%209.02773%205.42694%209.97136%206.72061%2010.8207C7.99677%2011.6585%209.87714%2012.2423%2012.0549%2012.2423V16.2442C9.20867%2016.2442%206.54061%2015.4898%204.52431%2014.1661C2.52553%2012.8539%200.957031%2010.8126%200.957031%208.27347C0.957031%205.73438%202.52541%203.69313%204.52415%202.38088C6.54039%201.05714%209.20841%200.302734%2012.0546%200.302734C13.4758%200.302734%2014.8428%200.489975%2016.1024%200.836454L15.041%204.69507C14.1367%204.44632%2013.1272%204.30466%2012.0546%204.30466C9.87688%204.30466%207.9966%204.88844%206.72051%205.72624Z'%20fill='url(%23paint1_linear_487_2188)'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M17.3904%2022.7629C18.6835%2021.9152%2019.1508%2020.9734%2019.1508%2020.2197C19.1508%2019.4648%2018.6821%2018.5194%2017.388%2017.6685C16.1117%2016.8293%2014.2316%2016.2444%2012.0549%2016.2444V12.2425C14.9021%2012.2425%2017.5704%2012.999%2019.5866%2014.3246C21.5849%2015.6386%2023.1527%2017.6813%2023.1527%2020.2197C23.1527%2022.7595%2021.5836%2024.7993%2019.5844%2026.1098C17.568%2027.4317%2014.9003%2028.1839%2012.0551%2028.1839C10.634%2028.1839%209.26697%2027.9967%208.00737%2027.6502L9.06876%2023.7916C9.97307%2024.0404%2010.9826%2024.182%2012.0551%2024.182C14.2338%2024.182%2016.1144%2023.5994%2017.3904%2022.7629Z'%20fill='url(%23paint2_linear_487_2188)'/%3e%3c/g%3e%3c/g%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_487_2188'%20x1='12.0549'%20y1='0.302734'%20x2='12.0549'%20y2='28.1839'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%230FB698'/%3e%3cstop%20offset='1'%20stop-color='%230FB698'%20stop-opacity='0.09'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_487_2188'%20x1='12.0549'%20y1='0.302734'%20x2='18.7908'%20y2='21.3234'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%230FB698'/%3e%3cstop%20offset='0.526042'%20stop-color='%230FB698'%20stop-opacity='0.09'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_487_2188'%20x1='15.58'%20y1='28.184'%20x2='11.962'%20y2='15.9811'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%230FB698'/%3e%3cstop%20offset='1'%20stop-color='%230FB698'%20stop-opacity='0.09'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e",O=n.forwardRef(((e,s)=>{const{fromUser:i,text:l}=e,[a,d]=n.useState(""),o=sessionStorage.getItem("name");return n.useEffect((()=>{d(l)}),[l]),t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"chat-message-container",children:[t.jsx("img",{src:"/assets/avatar-1-Bu_rICU3.svg"}),t.jsx("span",{style:{marginLeft:"11px",fontSize:"16px",color:"#000000"},children:o})]}),t.jsx("div",{className:i?"chat-message-one":"chat-message-ai-one",children:t.jsx(r,{children:a})})]})})),P=({messageList:e,isLocked:l,setMessageInput:r,onSendMessage:c,messageInput:h,handleNewConversation:x})=>{n.useEffect((()=>{p()}),[e]);const m=n.useRef(null),p=()=>{m.current&&m.current.scrollToBottom()};return t.jsxs("div",{className:"right",children:[t.jsx("div",{className:"headTitle",children:"调试预览"}),t.jsx(i,{className:"newtalk",type:"link",onClick:x,children:"新的对话"}),t.jsx("div",{className:"talkcontent",children:t.jsx(a,{ref:m,children:t.jsxs(s,{size:16,direction:"vertical",children:[t.jsxs("div",{className:"chat-message-container",children:[t.jsx("img",{className:"avatar-icon",src:B}),t.jsx("div",{style:{marginLeft:"20px"},children:"军事智能专家"})]}),t.jsx("div",{className:"chat-message-ai",children:"您好，我是军事智能助手有什么可以帮助您"}),null==e?void 0:e.map((e=>t.jsx(t.Fragment,{children:"user"===e.role?t.jsx(O,{fromUser:!0,text:e.content}):t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"chat-message-container",children:[t.jsx("img",{className:"avatar-icon",src:B}),t.jsx("div",{style:{marginLeft:"20px"},children:"军事智能专家"})]}),t.jsx("div",{className:"chat-message-ai",children:e.content})]})})))]})})}),t.jsx("div",{className:"chatTalk",children:t.jsx(d,{style:{border:"none",boxShadow:"none",maxHeight:"20px"},placeholder:l?"正在回答...":"请输入问题...",autoSize:!0,onPressEnter:e=>(e=>{if(l)e.preventDefault();else if("Enter"===e.key){if(e.shiftKey)return;e.preventDefault(),c(),r("")}})(e),onChange:e=>r(e.target.value),value:h})}),t.jsx(i,{className:"sendButton",type:"text",disabled:l,onClick:()=>{c()},children:t.jsx(o,{})})]})},U=({label:e,tooltipTitle:s,formatter:i,value:l,onChange:n,disabled:r=!1,SiderMark:a})=>t.jsxs("div",{className:"slider-container",children:[t.jsxs("div",{className:"temperatruelabel",children:[t.jsx("span",{children:e}),t.jsx(c,{title:s,children:t.jsx("img",{className:"frame-child179",src:z})})]}),t.jsx(h,{value:l,className:"create-tasks-slider",onChange:n,marks:a,step:25,dots:!0,tooltip:{formatter:i},disabled:r,style:{width:"67.8%",display:"inline-flex",margin:"unset"}})]}),q={0:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:C[0]}),t.jsx(c,{title:S[0],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),25:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:C[1]}),t.jsx(c,{title:S[1],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),50:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:C[2]}),t.jsx(c,{title:S[2],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),75:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:C[3]}),t.jsx(c,{title:S[3],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),100:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:C[4]}),t.jsx(c,{title:S[4],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]})},G={0:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:y[0]}),t.jsx(c,{title:T[0],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),25:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:y[1]}),t.jsx(c,{title:T[1],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),50:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:y[2]}),t.jsx(c,{title:T[2],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),75:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:y[3]}),t.jsx(c,{title:T[3],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),100:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:y[4]}),t.jsx(c,{title:T[4],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]})},D={0:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:f[0]}),t.jsx(c,{title:T[0],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),25:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:f[1]}),t.jsx(c,{title:T[1],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),50:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:f[2]}),t.jsx(c,{title:T[2],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),75:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:f[3]}),t.jsx(c,{title:T[3],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),100:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:f[4]}),t.jsx(c,{title:T[4],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]})},K={0:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:v[0]}),t.jsx(c,{title:w[0],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),25:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:v[1]}),t.jsx(c,{title:w[1],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),50:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:v[2]}),t.jsx(c,{title:w[2],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),75:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:v[3]}),t.jsx(c,{title:w[3],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),100:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:v[4]}),t.jsx(c,{title:w[4],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]})},H={0:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:u[0]}),t.jsx(c,{title:w[0],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),25:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:u[1]}),t.jsx(c,{title:w[1],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),50:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:u[2]}),t.jsx(c,{title:w[2],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),75:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:u[3]}),t.jsx(c,{title:w[3],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),100:t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{color:"#8E98A7"},children:u[4]}),t.jsx(c,{title:w[4],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]})},R=[{key:"splitLevel",label:"温度",tooltipTitle:"影响输出的随机性和创新性。较高的温度值促进创造性。",SiderMark:q,formatter:e=>e?S[Math.floor(e/25)]:S[0]},{key:"topkValue",label:"Top-k采样",tooltipTitle:"控制候选词的范围，影响文本的多样性和新颖性。",SiderMark:G,formatter:e=>e?T[Math.floor(e/25)]:T[0]},{key:"toppValue",label:"Top-p采样",tooltipTitle:"通过动态调整候选词池的大小，影响文本的创新性和多样性。",SiderMark:D,formatter:e=>e?k[Math.floor(e/25)]:k[0]}],Z=[{key:"maxLen",label:"最大长度",tooltipTitle:"虽然不直接影响相关性，但适当的长度可以确保回答不偏离主题。",SiderMark:K,formatter:e=>e?w[Math.floor(e/25)]:w[0]},{key:"repeatValue",label:"重复率惩罚",tooltipTitle:"通过减少重复，帮助模型保持话题的聚焦和相关性。",SiderMark:H,formatter:e=>e?N[Math.floor(e/25)]:N[0]}],W=["自动配置","手动配置"],J=({trainingSettings:e,setTrainingSettings:s,setModelConfig:i,modelConfig:l})=>{const r=(e,t)=>{s((s=>({...s,[e]:t}))),i("手动配置")},a=n.useMemo((()=>W.map((e=>({label:t.jsx(c,{title:e,children:t.jsx("a",{onClick:()=>i(e),className:l===e?"model-config-active":"model-config",children:e})}),value:e})))),[]);return t.jsxs("div",{className:"left",children:[t.jsx("div",{className:"headTitle",children:"参数配置"}),t.jsx("div",{className:"adjiustModel",children:t.jsxs("div",{style:{display:"flex",alignItems:"center",width:"100%"},children:[t.jsx("div",{style:{fontSize:"16px",fontWeight:"500",lineHeight:"16px",color:"#000000",fontFamily:"HarmonyOS Sans SC, HarmonyOS Sans SC",marginBottom:"24px"},children:"参数配置:"}),t.jsx(x,{style:{display:"flex",flexDirection:"column",width:"60%",marginLeft:"70px"},children:t.jsx(m.Item,{children:t.jsx(p,{className:"createtask-segmented",size:"large",options:a,value:l,onChange:e=>{"自动配置"===e&&s({splitLevel:50,topkValue:50,toppValue:50,maxLen:50,repeatValue:50}),i(e)}})})})]})}),t.jsxs("div",{className:"creative",children:[t.jsx("div",{className:"section-title",children:"创意性："}),R.map((s=>t.jsx(U,{label:s.label,tooltipTitle:s.tooltipTitle,formatter:s.formatter,value:e[s.key],SiderMark:s.SiderMark,onChange:e=>r(s.key,e),disabled:"自动配置"===l},s.key)))]}),t.jsxs("div",{className:"correlation",children:[t.jsx("div",{className:"section-title",children:"相关性："}),Z.map((s=>t.jsx(U,{label:s.label,tooltipTitle:s.tooltipTitle,SiderMark:s.SiderMark,formatter:s.formatter,value:e[s.key],onChange:e=>r(s.key,e),disabled:"自动配置"===l},s.key)))]})]})},Y=n.forwardRef(((e,s)=>{const{task_id:i}=j(),[l,r]=n.useState([]),[d,o]=n.useState(""),[c,h]=n.useState("请选择"),[x,m]=n.useState("自动配置"),{splitLevel:p,topkValue:S,toppValue:T,maxLen:k,repeatValue:w}=e.modelData||{},[N,z]=n.useState(!1),B=sessionStorage.getItem("config"),O=B?JSON.parse(B):{},[U,q]=n.useState({splitLevel:p?E(p):(null==O?void 0:O.temperature)?V("temperature",null==O?void 0:O.temperature):50,topkValue:S?b(S):(null==O?void 0:O.topK)?V("topK",null==O?void 0:O.topK):50,toppValue:T?M(T):(null==O?void 0:O.topP)?V("topP",null==O?void 0:O.topP):50,maxLen:k?A(k):(null==O?void 0:O.maxTokens)?V("maxTokens",null==O?void 0:O.maxTokens):50,repeatValue:w?L(w):(null==O?void 0:O.frequencyPenalty)?V("frequencyPenalty",null==O?void 0:O.frequencyPenalty):50}),[G,D]=n.useState([]),[K,H]=n.useState(""),[R,Z]=n.useState(""),W=n.useRef(null),[Y,Q]=n.useState([]),[X,$]=n.useState(!1);return n.useEffect((()=>{W.current&&W.current.scrollToBottom()}),[G]),n.useEffect((()=>{I({page:1,size:100,category:1,sortAttribute:"modelName",sortDirection:"asc",modelName:"",status:"7",modelCategory:"FINE_TUNING"}).then((e=>{var t;if(200===(null==(t=e.data)?void 0:t.code)){const t=e.data.data.map((e=>({value:e.id,label:e.modelName})));r(t);const s=t.find((e=>String(e.value)===i));s&&(h(s.label),o(s.value))}}))}),[]),t.jsx(a,{children:t.jsxs("div",{className:"adjustmentDetail",children:[t.jsx(_,{saveSet:()=>{d?(z(!1),C[U.splitLevel/25],y[U.topkValue/25],f[U.toppValue/25],v[U.maxLen/25],u[U.repeatValue/25],g.success("保存配置成功")):z(!0)}}),t.jsx("div",{className:"adjustment-detail-info",children:t.jsxs("div",{className:"container-left-right",children:[t.jsx(J,{modelConfig:x,setModelConfig:m,trainingSettings:U,setTrainingSettings:q}),t.jsx(P,{messageList:G,handleNewConversation:()=>{G.length>0&&(Q([...Y,...G]),D([]))},isLocked:X,messageInput:K,setMessageInput:H,onSendMessage:async()=>{if(X)return;if(!K.trim())return;$(!0);const e=F.get("token"),t={modelId:d,temperature:C[U.splitLevel/25],topK:y[U.topkValue/25],topP:f[U.toppValue/25],maxTokens:v[U.maxLen/25],frequencyPenalty:u[U.repeatValue/25],question:K};D((e=>[...e,{role:"user",content:K}]));const s=await fetch("/api/fine-tuning/model/chat",{method:"POST",headers:{"Content-Type":"application/json","ACCESS-KEY":e||""},body:JSON.stringify(t)});if(!s.body)return void $(!1);const i=s.body.getReader(),l=new TextDecoder("utf-8");let n="";for(;;){const{done:e,value:t}=await i.read();if(e)break;n+=l.decode(t,{stream:!0});const s=n.split("\n");n=s.pop()||"";for(const i of s)if(i.startsWith("data:")){const e=i.replace("data:","").trim();D((t=>{const s=t[t.length-1];return"ai"===(null==s?void 0:s.role)?(s.content+=e,[...t]):[...t,{role:"ai",content:e}]}))}}$(!1),H("")}})]})})]})})}));export{Y as default};

import{r as e,j as t,K as a,W as s,S as r,B as l,t as n,_ as o,k as i,ae as d,af as c,f as m}from"./react-core-BrQk45h1.js";import{u as p,c as x,d as h}from"./dataset-CNkWbBvO.js";/* empty css                          */import{D as j}from"./DatasetTable-qqjeGPbM.js";import{U as u}from"./UploadErrorModal-Dpa2hJTx.js";import{U as g}from"./UploadList-CCFxdIyn.js";import{u as y}from"./uploadcomputer-DVakL5y0.js";import{b as f}from"./bxtrash-Dz9B4ICl.js";import{d as k}from"./task-ClBf-SyM.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";import"./types-ccCJOgIs.js";import"./empty-logo-5H_PPUCG.js";const b=({visible:i,OnClose:d})=>{const[c,m]=e.useState([]),h={name:"file",multiple:!0,action:"/api/qa_generator/upload_data",accept:".txt,.docx,.doc,.pdf,.zip,.rar,.csv,.xlsx,.json,.png,.md,.jpg,.jpeg",showUploadList:!1,fileList:c,beforeUpload:e=>{const t=[".txt",".docx",".doc",".pdf",".zip",".rar",".csv",".xlsx",".json",".md",".png",".jpg",".jpeg"],a=e.name.toLowerCase();return!!t.some((e=>a.endsWith(e)))||(n.error(`不支持的文件类型: ${e.name}。请上传以下类型: ${t.join(", ")}`),o.LIST_IGNORE)},onDrop(e){},customRequest:e=>{p({importDataset:e.file,tags:[]}).then((t=>{var a;200===(null==(a=t.data)?void 0:a.code)?e.onSuccess&&e.onSuccess(t,e.file):e.onError&&e.onError(t.data)})).catch(e.onError)},onChange(e){let t=[...e.fileList];m(t)}};return t.jsx(a,{title:"上传本地数据集",centered:!0,keyboard:!1,maskClosable:!1,width:870,open:i,onOk:()=>{m([]),d()},onCancel:()=>{m([]),d()},cancelButtonProps:{},styles:{body:{height:"624px"}},footer:[t.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center"},children:t.jsx(l,{type:"primary",className:"primary-btn boldText",style:{width:"124px"},onClick:()=>{m([]),d()},children:"确认"},"okBtn")},"okBtnDiv")],children:t.jsxs("div",{style:{height:"100%"},children:[t.jsxs(s,{...h,style:{height:"239px !important"},className:"uploadDatasetDragger",children:[t.jsxs("div",{className:"createTaskDraggerInner reqularText",children:[t.jsx("img",{className:"createTaskDraggerIcon",alt:"",src:y}),t.jsx("p",{children:"拖入您需要解析的本地数据集文档，或点击进行选择"})]}),t.jsxs("div",{className:"createTaskDraggerInfo reqularText",children:[t.jsx("label",{className:"crateTaskDraggerLabel",children:"请将文档打包为zip格式进行上传"}),t.jsx("br",{}),t.jsx("label",{className:"crateTaskDraggerLabel",style:{left:"39%",transform:"translateX(-31%)"},children:"支持解析的文档类型有txt、doc、docx、rtf、pdf、json、csv、xlsx、markdown"})]})]}),t.jsx("div",{style:{height:"369px"},children:t.jsx(r,{children:t.jsx(g,{fileList:c,onAddTag:(e,t)=>{x({dataSetId:e,tags:t}).then((a=>{var s;if(0===(null==(s=null==a?void 0:a.data)?void 0:s.code)){const a=c;a.forEach((a=>{a.dataSetId===e&&(a.tags=t)})),m(a)}}))},onDelFile:e=>{const t=c.filter(((t,a)=>a!==e));m(t)},onReUpload:e=>{const t=c[e];const a=t;a.status="uploading",p({importDataset:t,tags:[]}).then((t=>{var s;200===(null==(s=t.data)?void 0:s.code)?(a.status="success",a.dataSetId=t.data.data):a.status="error",c[e]=a,m(c)}))}},"uploadDatasetModal")})})]})})},v=()=>{const s=i(),[o,p]=e.useState(!1),[x,g]=e.useState(!1),[y,v]=e.useState([]),{confirm:C}=a,L=e.useRef(null);return t.jsx(r,{children:t.jsxs("div",{className:"createTaskContent",children:[t.jsx("div",{className:"mediumText",style:{fontSize:"28px",lineHeight:"36px",fontWeight:"500",marginLeft:"2rem"},children:"源数据集"}),t.jsx("div",{className:"createTaskArea",children:t.jsx(j,{ref:L,onSelectRows:e=>v(e),ShowActionColumn:!0,Extra:t.jsxs("div",{style:{display:"inline-flex"},children:[t.jsxs(l,{size:"large",onClick:()=>{g(!0)},className:"upload-dataset-btn",style:{marginRight:"8px"},children:[t.jsx("img",{className:"btn-icon",src:"data:image/svg+xml,%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M13.3333%202H2.66668C1.93134%202%201.33334%202.598%201.33334%203.33333V12.6667C1.33334%2013.402%201.93134%2014%202.66668%2014H13.3333C14.0687%2014%2014.6667%2013.402%2014.6667%2012.6667V3.33333C14.6667%202.598%2014.0687%202%2013.3333%202ZM2.66668%2012.6667L2.66668%204H13.3333L13.3347%2012.6667H2.66668Z'%20fill='black'/%3e%3cpath%20d='M6.19533%206.19531L4.43111%207.95954C4.04058%208.35006%204.04058%208.98323%204.43111%209.37375L6.19533%2011.138L7.138%2010.1953L5.60933%208.66665L7.138%207.13798L6.19533%206.19531ZM9.80467%206.19531L8.862%207.13798L10.3907%208.66665L8.862%2010.1953L9.80467%2011.138L11.5689%209.37375C11.9594%208.98323%2011.9594%208.35006%2011.5689%207.95954L9.80467%206.19531Z'%20fill='black'/%3e%3c/svg%3e"}),"导入数据集"]}),t.jsxs(l,{disabled:0===y.length,size:"large",className:"upload-dataset-btn",style:{marginRight:"16px"},onClick:()=>{const e=y.some((e=>e.relatedQATaskList.length>0));let a=[];y.filter((e=>e.relatedQATaskList.length>0)).length>0&&(a=y.filter((e=>e.relatedQATaskList.length>0)).map((e=>e.relatedQATaskList.map((t=>({...t,dataSetName:e.name,dataSetId:e.id}))))).reduce(((e,t)=>e.concat(t))));const r=[{title:"数据集名称",dataIndex:"datasetName",key:"datasetName",render:(e,a)=>{const{datasetName:s}=a;return t.jsx("a",{className:"dataset-name",children:s})}},{title:"任务名称",dataIndex:"name",key:"name",render:(e,a)=>{const{taskName:r,taskId:l}=a;return t.jsx("a",{className:"dataset-name",onClick:e=>{e.preventDefault(),s(`/main/task/detail/${l}`),o.destroy()},children:r})}},{title:"Action",key:"action",render:(e,{taskId:a})=>t.jsxs(m,{children:[t.jsx(l,{type:"link",style:{color:"#0fb698",fontFamily:"HarmonyOS Sans SC Reqular"},onClick:()=>{s(`/main/task/detail/${a}`),o.destroy()},children:"详情"}),t.jsx(l,{type:"link",style:{color:"#0fb698",fontFamily:"HarmonyOS Sans SC Reqular"},onClick:()=>{const e=C({centered:!0,title:"删除提示",icon:t.jsx(c,{}),width:540,content:t.jsx(t.Fragment,{children:t.jsx("div",{className:"default-info",style:{color:"black"},children:"确定要删除所选任务吗？"})}),footer:[t.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",padding:"2rem 0 0 0",gap:"8px"},children:[t.jsx(l,{type:"text",onClick:()=>e.destroy(),shape:"round",children:"取消"}),t.jsx(l,{type:"primary",onClick:()=>{k([a]).then((t=>{200===t.data.code?(e.destroy(),o.destroy()):n.error(t.data.message)}))},shape:"round",className:"primary-btn",style:{width:"120px"},children:"确认删除"})]})],onOk(){k([a]).then((t=>{200===t.data.code?(e.destroy(),o.destroy()):n.error(t.data.message)}))},onCancel(){e.destroy()}})},children:"删除"})]})}],o=C({centered:!0,title:"删除提示",icon:t.jsx(c,{}),width:540,content:t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"default-info",style:{color:"black"},children:"确定要删除所选数据集吗？"}),e?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"upload-error-label",style:{marginTop:"8px"},children:"所选源数据集有关联的推理任务，无法进行删除。如需删除，请先删除关联任务。"}),t.jsx(d,{scroll:{y:400},size:"small",pagination:!1,columns:r,dataSource:a,style:{flex:1},tableLayout:"fixed",rowKey:"taskId",className:"dataset-table"})]}):null]}),footer:[t.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",padding:"2rem 0 0 0",gap:"8px"},children:[t.jsx(l,{type:"text",onClick:()=>o.destroy(),shape:"round",children:"取消"}),t.jsx(l,{disabled:e,type:"primary",onClick:()=>{var e;const t=y.map((e=>e.id));null==(e=L.current)||e.handleDeleteRows(t),h(t).then((e=>{o.destroy(),0!==e.data.code&&200!==e.data.code||L.current&&L.current.onRefresh()}))},shape:"round",className:"primary-btn",style:{width:"120px"},children:"确认删除"})]})],onOk(){var e;const t=y.map((e=>e.id));null==(e=L.current)||e.handleDeleteRows(t),h(t).then((e=>{o.destroy(),0!==e.data.code&&200!==e.data.code||L.current&&L.current.onRefresh()}))},onCancel(){o.destroy()}})},children:[t.jsx("img",{className:"btn-icon",src:f}),"删除数据集"]}),t.jsx(l,{type:"primary",size:"large",shape:"round",style:{backgroundColor:"black",fontSize:"14px",fontWeight:"700"},className:"boldText",onClick:()=>{s("/main/task/create",{state:{selectedRows:y}})},children:"生成训练数据"})]})})}),t.jsx(b,{visible:x,OnClose:()=>{g(!1),L.current&&L.current.onRefresh()}}),t.jsx(u,{visible:o,OnClose:e=>{p(!1)}})]})})};export{v as default};

import { LeftOutlined } from "@ant-design/icons";
import { useNavigate, useParams } from "react-router-dom";

import { Button, Space } from "antd";
export interface HeaderSpaceProps {
  saveSet: () => void;
}

const HeaderSpace: React.FC<HeaderSpaceProps> = ({ saveSet }) => {
  const navigate = useNavigate();

  return (
    <Space size={20} style={{ display: "inline-flex", alignItems: "center" }}>
      <Button
        style={{ fontSize: "12px", width: "36px", height: "36px" }}
        shape="circle"
        icon={<LeftOutlined />}
        onClick={() => navigate("/main/finetune")}
      />
      <div
        className="mediumText"
        style={{ fontSize: "28px", lineHeight: "36px", fontWeight: "500" }}
      >
        调试模型
      </div>
      <Button
        type="primary"
        size="large"
        shape="round"
        className="publishButton"
        onClick={() => saveSet()}
      >
        保存
      </Button>
    </Space>
  );
};

export default HeaderSpace;

import{k as e,p as s,r as l,I as a,j as r,f as t,B as o,l as i,t as n}from"./react-core-BrQk45h1.js";/* empty css                        */import{c}from"./server-YuXc7UOr.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const m=()=>{const m=e(),[p]=s.useForm(),[u,d]=l.useState({cpuInfo:"",description:"",gpuInfo:"",id:0,memoryInfo:"",resourceUrl:"",serverName:""}),{TextArea:h}=a,x=(e,s)=>{d((l=>({...l,[e]:s})))};return r.jsxs("div",{className:"uploadBaseModel",children:[r.jsxs(t,{size:20,style:{display:"inline-flex",alignItems:"center"},children:[r.jsx(o,{style:{fontSize:"12px",width:"36px",height:"36px"},shape:"circle",icon:r.jsx(i,{}),onClick:()=>m(-1)}),r.jsx("div",{className:"mediumText",style:{fontSize:"20px",lineHeight:"36px",fontWeight:"500"},children:"添加服务器"})]}),r.jsx("div",{className:"uploadModelArea",children:r.jsxs(s,{form:p,className:"reqularText",name:"uploadModelForm",autoComplete:"off",labelCol:{span:6},wrapperCol:{span:18,offset:2},children:[r.jsx(s.Item,{name:"serverName",label:"服务器名称:",rules:[{required:!0,message:"请选择上传文件"}],children:r.jsx(a,{placeholder:"请输入服务器名称",value:u.resourceUrl,onChange:e=>x("serverName",e.target.value),allowClear:!0,size:"large",style:{width:"511px"}})}),r.jsx(s.Item,{name:"resourceUrl",label:"服务器IP:",rules:[{required:!0,message:"请输入服务器IP"}],children:r.jsx(a,{placeholder:"http://xz1.puhuacloud.com:17305",value:u.resourceUrl,onChange:e=>x("resourceUrl",e.target.value),allowClear:!0,size:"large",style:{width:"511px"}})}),r.jsx(s.Item,{name:"cpuInfo",label:"CPU核数:",rules:[{required:!1,message:"请输入CPU核数"}],children:r.jsx(a,{placeholder:"请输入CPU核数",value:u.cpuInfo,onChange:e=>x("cpuInfo",e.target.value),allowClear:!0,size:"large",style:{width:"511px"}})}),r.jsx(s.Item,{name:"gpuInfo",label:"GPU信息:",rules:[{required:!1,message:"请输入GPU信息"}],children:r.jsx(a,{value:u.gpuInfo,onChange:e=>x("gpuInfo",e.target.value),placeholder:"请输入GPU信息",size:"large",style:{width:"511px"}})}),r.jsx(s.Item,{name:"memoryInfo",label:"内存信息:",rules:[{required:!1,message:"请输入内存信息"}],children:r.jsx(a,{placeholder:"请输入内存信息",value:u.memoryInfo,onChange:e=>x("memoryInfo",e.target.value),allowClear:!0,size:"large",style:{width:"511px"}})}),r.jsx(s.Item,{name:"description",label:"服务器描述:",rules:[{required:!1,message:"请输入服务器描述"}],children:r.jsx(h,{value:u.description,onChange:e=>x("description",e.target.value),placeholder:"请输入服务器描述",rows:4,allowClear:!0,size:"large",style:{width:"511px"}})}),r.jsxs(s.Item,{style:{textAlign:"center",marginTop:"40px"},children:[r.jsx(o,{shape:"round",className:"cancalBut",size:"large",style:{marginRight:"60px"},onClick:()=>{m(-1)},children:"取消"}),r.jsx(o,{shape:"round",htmlType:"submit",className:"submBut",size:"large",style:{marginLeft:"60px"},onClick:async()=>{try{await p.validateFields();const e=await c(u);200===e.code?(n.success("上传成功"),m(-1)):n.error(e.message)}catch(e){}},children:"确认添加"})]})]})})]})};export{m as default};

import{k as e,r as s,p as a,j as t,S as r,f as l,B as i,l as o,I as n,_ as c,aE as d,t as m}from"./react-core-BrQk45h1.js";import{U as p}from"./UploadList-CCFxdIyn.js";import{u}from"./uploadcomputer-DVakL5y0.js";import{u as h,p as j}from"./task-ClBf-SyM.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./UploadErrorModal-Dpa2hJTx.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const{Dragger:x}=c,g=()=>{const c=e(),[g,f]=s.useState(""),[k,N]=s.useState([]),[b,v]=s.useState(!1),[y]=a.useForm(),T={name:"file",multiple:!0,action:"/task/import",accept:"json,application/json,.csv",showUploadList:!0,fileList:k,onDrop(e){},customRequest:()=>{},onChange(e){let s=[...e.fileList];N(s)}};return t.jsx(r,{children:t.jsxs("div",{className:"uploadDataset",children:[t.jsxs(l,{size:20,style:{display:"inline-flex",alignItems:"center"},children:[t.jsx(i,{style:{fontSize:"12px",width:"36px",height:"36px"},shape:"circle",icon:t.jsx(o,{}),onClick:()=>c(-1)}),t.jsx("div",{className:"mediumText",style:{fontSize:"28px",lineHeight:"36px",fontWeight:"500"},children:"上传本地数据"})]}),t.jsx("div",{className:"uploadDatasetArea",style:{marginBottom:"47px"},children:t.jsxs(a,{form:y,className:"reqularText",name:"uploadDatasetForm",layout:"vertical",initialValues:{},onFinish:e=>{const s={taskName:e.taskName,importQAs:k[0].originFileObj};v(!0),h(s).then((e=>{e.data.data,m.success("上传成功"),v(!1),c(-1)}))},onFinishFailed:e=>{v(!1)},autoComplete:"off",children:[t.jsx(a.Item,{name:"taskName",label:"任务名称",rules:[{required:!0,message:"请输入任务名称"},{message:"任务名称不符合要求！"}],children:t.jsx(n,{placeholder:"请输入",value:g,onChange:e=>f(e.target.value),style:{width:"30rem",height:"2.5rem"}})}),t.jsxs(a.Item,{label:"上传本地数据",rules:[{required:!0,message:"请上传本地数据"}],children:[t.jsxs(x,{...T,className:"createTaskDragger",children:[t.jsxs("div",{className:"createTaskDraggerInner",children:[t.jsx("img",{className:"createTaskDraggerIcon",alt:"",src:u}),t.jsx("p",{className:"reqularText",children:"拖入您需要解析的本地数据集文档，或点击进行选择"})]}),t.jsxs("div",{className:"createTaskDraggerInfo",children:[t.jsxs("label",{className:"crateTaskDraggerLabel reqularText",children:["请首先",t.jsx(i,{type:"link",style:{padding:"0"},onClick:e=>{e.stopPropagation(),j().then((e=>{const s=new Blob([e.data],{type:"application/zip"}),a=window.URL.createObjectURL(s),t=document.createElement("a");t.href=a,t.download="模板.zip",document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(a)})).catch((e=>{m.error("下载模板文件失败："+e.message)}))},children:"下载模板文件"}),"，按照模板文件格式收集或修改本地数据。"]}),t.jsx("br",{}),t.jsx("label",{className:"crateTaskDraggerLabel reqularText",children:"支持解析的文档类型有json、csv"})]})]}),t.jsx("div",{children:t.jsx(p,{fileList:k,onAddTag:(e,s)=>{N(k)},onDelFile:e=>{const s=k.filter(((s,a)=>a!==e));N(s)},className:"create-task-file-list",onReUpload:e=>{const s=k[e];const a=s;a.status="uploading",h({importQAs:s,taskName:g}).then((s=>{var t;200===(null==(t=s.data)?void 0:t.code)?(a.status="success",a.dataSetId=s.data.data):a.status="error",k[e]=a,N(k)}))}})})]}),t.jsx(a.Item,{style:{textAlign:"center",marginTop:"120px"},children:t.jsx(i,{shape:"round",htmlType:"submit",className:"submitBut",disabled:b,children:b?t.jsx(d,{}):"确认上传"})})]})})]})})};export{g as default};

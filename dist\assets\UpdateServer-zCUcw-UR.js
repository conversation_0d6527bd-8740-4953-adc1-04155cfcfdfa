import{k as e,s,p as a,r as l,j as r,f as t,B as i,l as o,I as n,t as c}from"./react-core-BrQk45h1.js";import{a as m,u as d,d as p}from"./server-YuXc7UOr.js";/* empty css                        */import{D as u}from"./DeleteConfirmModal-CNzDvFL4.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const x=({name:e,label:s,rules:l,placeholder:t})=>r.jsx(a.Item,{name:e,label:s+":",rules:l,children:r.jsx(n,{placeholder:t,size:"large",allowClear:!0,className:"form-field-input"})}),j=()=>{const j=e(),{server_id:f}=s(),[h]=a.useForm(),[g,b]=l.useState(!1),[y,C]=l.useState(!1),[w,N]=l.useState(null);l.useEffect((()=>{(async()=>{try{const e=await m(Number(f));e&&h.setFieldsValue(e)}catch{c.error("数据加载失败")}})()}),[]);return r.jsxs("div",{className:"uploadBaseModel",children:[r.jsxs(t,{size:20,style:{display:"inline-flex",alignItems:"center"},children:[r.jsx(i,{style:{fontSize:"12px",width:"36px",height:"36px"},shape:"circle",icon:r.jsx(o,{}),onClick:()=>j(-1)}),r.jsx("div",{className:"mediumText",style:{fontSize:"20px",lineHeight:"36px",fontWeight:"500"},children:"服务器配置"})]}),r.jsx("div",{className:"uploadModelArea",children:r.jsxs(a,{form:h,labelCol:{span:6},wrapperCol:{span:18,offset:2},children:[r.jsx(x,{name:"serverName",label:"服务器名称",rules:[{required:!0,message:"请输入名称"}]}),r.jsx(x,{name:"resourceUrl",label:"服务器IP",rules:[{required:!0,message:"请输入IP"}]}),r.jsx(x,{name:"cpuInfo",label:"CPU核数"}),r.jsx(x,{name:"gpuInfo",label:"GPU信息"}),r.jsx(x,{name:"memoryInfo",label:"内存信息"}),r.jsx(a.Item,{name:"description",label:"服务器描述:",children:r.jsx(n.TextArea,{rows:4,className:"form-field-input"})}),r.jsxs(a.Item,{wrapperCol:{offset:5},children:[r.jsx(i,{shape:"round",className:"cancalBut",size:"large",style:{marginRight:"60px"},onClick:()=>j(-1),children:"取消"}),r.jsx(i,{shape:"round",htmlType:"submit",className:"submBut",size:"large",style:{marginLeft:"60px"},loading:g,onClick:async()=>{try{b(!0);const e=await h.validateFields(),s=await d({...e,id:Number(f)});200===s.code?(c.success("上传成功"),j(-1)):c.error(s.message)}catch(e){c.error("提交失败，请检查表单")}finally{b(!1)}},children:"提交"}),r.jsx(i,{shape:"round",size:"large",style:{marginLeft:"60px",color:"white",backgroundColor:"rgba(231, 82, 82, 1)"},loading:g,onClick:()=>C(!0),children:"服务器删除"})]})]})}),r.jsx(u,{visible:y,onCancel:()=>C(!1),onDelete:async()=>{try{b(!0);const e=Number(f);await p(e)?(j(-1),c.success("删除成功")):c.error("删除失败")}catch(e){c.error("删除")}finally{b(!1)}},title:"提示",content:"确认删除该服务器吗？"})]})};export{j as default};

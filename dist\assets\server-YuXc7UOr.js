import{r as a}from"./request-v7NtcsCU.js";async function e(e){try{const t="/server",s=await a.post(t,e);return 200==s.data.code?{code:200,message:"更新成功",data:!0}:50001==s.data.code?{code:400,message:s.data.message,data:!1}:{code:400,message:"创建失败",data:!1}}catch(t){return{code:400,message:"未知错误",data:!1}}}async function t(e){try{const t="/server",s=await a.put(t,e);return 200==s.data.code?{code:200,message:"更新成功",data:!0}:50001==s.data.code?{code:400,message:s.data.message,data:!1}:{code:400,message:"更新失败",data:!1}}catch(t){return{code:400,message:"未知错误",data:!1}}}async function s(){try{const e="/server/all",t=await a.get(e);return 200==t.data.code?t.data.data.servers:[]}catch(e){return[]}}async function r(e){try{const t=`/server/${e}`,s=await a.get(t);return 200==s.data.code?s.data.data:void 0}catch(t){return}}async function c(e){try{const t=`/server/${e}`;return 200==(await a.delete(t)).data.code}catch(t){return!1}}export{r as a,e as c,c as d,s as q,t as u};

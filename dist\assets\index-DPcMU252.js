import{s as e,r as l,k as t,j as i,n as a,f as s,ar as n,a7 as d,aq as r,as as o,S as c,B as x,l as m,x as u,t as h}from"./react-core-BrQk45h1.js";import{O as v,T as g}from"./TimeModal-CKvOYePi.js";import{i as p}from"./info-icon-DzE4dkR7.js";import{i as j}from"./info-icon2-BPNrwGFd.js";import{i as b}from"./charts-C9p0W68L.js";import{h as f,i as y}from"./modle-Dr2iwiy1.js";import{D as S}from"./DeleteConfirmModal-CNzDvFL4.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const N="_training-header_otgam_15",w="_info-title_jg3f9_1",C="_descriptionText_jg3f9_25",k=({trainingData:d})=>{const{id:r}=e();l.useRef();const o=t(),[c,x]=l.useState(!1),[m,u]=l.useState(d.name),[h,g]=l.useState(d.modelIntro),[b,f]=l.useState(!1),[y,S]=l.useState(!1),N=[{key:"1",label:"模型名称",children:i.jsx(a,{title:d.name,children:i.jsx("div",{className:C,children:d.name})})},{key:"2",label:"模型介绍",children:i.jsx(a,{title:d.desc,children:i.jsx("div",{className:C,children:d.desc})})},{key:"3",label:"服务器信息",children:i.jsx(a,{title:d.serverName,children:i.jsx("div",{className:C,children:d.serverName})})}],k=[{key:"1",label:"数据集",children:i.jsx(a,{title:d.datasetName,children:i.jsx("div",{className:C,children:d.datasetName})})},{key:"2",label:"测试集比例",children:d.testSetRadio}],B=[{key:"1",label:"基座模型",children:i.jsx(a,{title:d.modelBaseName,children:i.jsx("div",{className:C,children:d.modelBaseName})})},{key:"2",label:"训练策略",children:d.trainingStrategy},{key:"3",label:"模型URL",children:i.jsxs("div",{style:{display:"flex",alignItems:"center"},children:["离线"===d.modelUrl&&i.jsx(a,{title:"请将模型上线显示URL",children:i.jsx("img",{src:j,style:{width:"16px",height:"16px",marginLeft:"-24px",position:"absolute"}})}),"离线"===d.modelUrl?d.modelUrl:i.jsx(a,{title:d.modelUrl,children:i.jsx("div",{className:C,children:d.modelUrl})})]})}],R=[{key:"1",label:"模型ID",children:null==d?void 0:d.id},{key:"2",label:"模型介绍",children:null==d?void 0:d.modelIntro},{key:"3",label:"数据集",children:(null==d?void 0:d.datasetName)||"-"},{key:"4",label:"数据集比例",children:(null==d?void 0:d.testSetRadio)||"-"},{key:"5",label:"基座模型",children:(null==d?void 0:d.modelBaseName)||"-"},{key:"6",label:"训练策略",children:(null==d?void 0:d.trainingStrategy)||"-"},{key:"7",label:"迭代次数",children:(null==d?void 0:d.interationNumber)||"-"},{key:"8",label:"批次大小",children:(null==d?void 0:d.batchSize)||"-"},{key:"9",label:"学习率",children:(_=null==d?void 0:d.learnRate,(1e-5===_?"1e-5":5e-5===_?"5e-5":1e-4===_?"1e-4":5e-4===_?"5e-4":.001===_?"1e-3":void 0)||"-")},{key:"10",label:i.jsx("span",{style:{color:"#000000",fontWeight:"bold"},children:"模型状态"}),children:(null==d?void 0:d.status)||"-"},{key:"11",label:i.jsx("span",{style:{color:"#000000",fontWeight:"bold"},children:"训练进度"}),children:null==d?void 0:d.trainProgress}];var _;const F=i.jsxs("span",{style:{lineHeight:"25px"},children:["增量训练：",i.jsx("br",{}),"结合该模型之前微调训练",i.jsx("br",{}),"的基座模型+历史训练",i.jsx("br",{}),"集数据再加上新数据",i.jsx("br",{}),"重新开始训练。"]});return i.jsxs(i.Fragment,{children:[i.jsxs(s,{direction:"vertical",children:[i.jsx(n,{style:{margin:"0 24px"},title:i.jsx("label",{className:w,children:"基本信息"}),items:N,column:1,bordered:!1}),i.jsx(n,{style:{margin:"0 24px"},title:i.jsx("label",{className:w,children:"数据配置"}),items:k,column:1,bordered:!1}),i.jsx(n,{style:{margin:"0 24px"},title:i.jsx("label",{className:w,children:"训练配置"}),items:B,column:1,bordered:!1})]}),i.jsx(v,{visible:c,titltext:d.name,moduletext:d.baseModel,buttontext:"增量训练",logo:!1,OnClose:()=>{x(!1)},onButtonClick:()=>{o("/main/finetune/create")},children:i.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center"},children:[i.jsx("div",{className:"descriptions",children:i.jsx(n,{column:1,style:{padding:"1rem 0rem 2rem 0.6rem"},size:"small",items:R})}),i.jsx("div",{style:{position:"absolute",bottom:"46px",right:"25%"},children:i.jsx(a,{title:F,children:i.jsx("img",{src:p,style:{width:"16px",height:"16px"}})})})]})})]})},B="_report-chart_iud23_31",R="_report-desc_iud23_39",_="_sample-div-text_13fn6_1",F="_sample-div_13fn6_1",L="_sample-div-score_13fn6_33",U="_sample-img_13fn6_51",z=({question:e,text:l,answer:t,score:a})=>i.jsx(i.Fragment,{children:i.jsxs(s,{direction:"vertical",size:19,children:[i.jsxs("div",{className:F,children:[i.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='18'%20height='18'%20viewBox='0%200%2018%2018'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20width='18'%20height='18'%20rx='4'%20fill='%23867ACD'%20fill-opacity='0.15'/%3e%3cpath%20d='M14%2015H11.0818L8.81761%2013.0729C8.74214%2013.0809%208.63732%2013.0849%208.50315%2013.0849C7.66457%2013.0849%206.90147%2012.9053%206.21384%2012.5462C5.53459%2012.1792%204.99371%2011.6525%204.59119%2010.9663C4.19706%2010.28%204%209.47008%204%208.53645C4%207.61081%204.19706%206.80486%204.59119%206.11861C4.99371%205.43235%205.53878%204.90968%206.22641%204.5506C6.91405%204.18353%207.67296%204%208.50315%204C9.33333%204%2010.0881%204.18353%2010.7673%204.5506C11.4549%204.90968%2012%205.43235%2012.4025%206.11861C12.805%206.80486%2013.0063%207.61081%2013.0063%208.53645C13.0063%209.41422%2012.826%2010.1843%2012.4654%2010.8466C12.1132%2011.5089%2011.6268%2012.0316%2011.0063%2012.4146L14%2015ZM6.27673%208.53645C6.27673%209.07109%206.36897%209.54189%206.55346%209.94886C6.74633%2010.3558%207.01048%2010.671%207.34591%2010.8945C7.68134%2011.1099%208.06709%2011.2176%208.50315%2011.2176C8.9392%2011.2176%209.32495%2011.1099%209.66038%2010.8945C10.0042%2010.671%2010.2683%2010.3558%2010.4528%209.94886C10.6457%209.54189%2010.7421%209.07109%2010.7421%208.53645C10.7421%208.00979%2010.6457%207.54697%2010.4528%207.14799C10.2683%206.74102%2010.0042%206.42982%209.66038%206.21436C9.32495%205.99093%208.9392%205.87922%208.50315%205.87922C8.06709%205.87922%207.68134%205.99093%207.34591%206.21436C7.01048%206.42982%206.74633%206.74102%206.55346%207.14799C6.36897%207.54697%206.27673%208.00979%206.27673%208.53645Z'%20fill='%23867ACD'/%3e%3c/svg%3e",alt:"qusestion",className:U}),e]}),i.jsxs("div",{className:_,children:[i.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='18'%20height='18'%20viewBox='0%200%2018%2018'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20width='18'%20height='18'%20rx='4'%20fill='%2325B0E7'%20fill-opacity='0.1'/%3e%3cpath%20d='M14%204H4V6.5H7.92857V14H10.0714V6.5H14V4Z'%20fill='%2325B0E7'/%3e%3c/svg%3e",alt:"text",className:U}),l]}),i.jsxs("div",{className:F,children:[i.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='18'%20height='18'%20viewBox='0%200%2018%2018'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20width='18'%20height='18'%20rx='4'%20fill='%230FB698'%20fill-opacity='0.15'/%3e%3cpath%20d='M9.97015%204L14%2014H11.5577L10.7978%2011.9781H7.03935L6.30665%2014H4L7.9213%204H9.97015ZM10.0651%2010.0246L8.88467%206.90984L7.75848%2010.0246H10.0651Z'%20fill='%230FB698'/%3e%3c/svg%3e",alt:"answer",className:U}),t]}),i.jsxs("div",{className:L,children:["得分： ",a]})]})}),P=({trainingData:e})=>{var t,o,c,x,m,u,h,v,g,p,j,f;const y=l.useRef(null),[S,N]=l.useState(!1),[w,C]=l.useState([{question:"歼-20战斗机的隐身能力和战略地位如何体现,以及其在中国空军中的历史意义？",text:"取值为0时任务随机选择1条数据用于测试，且无论比例取值多少，任务最多拆分100条数据用于测试。",answer:'歼-20,被称为"威龙",是中国解放军自主研发的第五代制空占战斗机,具备高隐身性、高态势感知和高机动XXXXXXXXXXXXX……',score:32.9}]),k=[{key:"1",label:"模型评分",children:e.name},{key:"2",label:"PPL",children:(null==(o=null==(t=e.data)?void 0:t.predictRougeL)?void 0:o.toFixed(2))||"-"},{key:"3",label:"ROUGE-1",children:(null==(x=null==(c=e.data)?void 0:c.predictRouge1)?void 0:x.toFixed(2))||"-"},{key:"4",label:"ROUGE-2",children:(null==(u=null==(m=e.data)?void 0:m.predictRouge2)?void 0:u.toFixed(2))||"-"},{key:"5",label:"BLEU-1",children:(null==(v=null==(h=e.data)?void 0:h.predictBleu2)?void 0:v.toFixed(2))||"-"},{key:"6",label:"BLEU-2",children:(null==(p=null==(g=e.data)?void 0:g.predictBleu3)?void 0:p.toFixed(2))||"-"},{key:"7",label:"BERTScore",children:(null==(f=null==(j=e.data)?void 0:j.predictBleu4)?void 0:f.toFixed(2))||"-"}];return l.useEffect((()=>{(()=>{var l,t,i,a,s,n,d,r,o,c,x,m;if(y.current){const u=b(y.current),h={title:{text:`—————  ${e.name}\n`,subtext:`—————  ${e.modelBaseName}`,left:"right",textAlign:"left",textStyle:{color:"rgba(57, 208, 181, 1)",fontSize:14},subtextStyle:{color:"rgba(41, 85, 231, .8)",fontSize:14}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:e=>e},splitLine:{show:!0,lineStyle:{color:"rgba(208, 241, 255, 1)"}},color:["#39D0B5"],radar:{axisName:{show:!0,color:"#000",fontSize:14},indicator:[{name:"PPL\n衡量模型好坏程度",max:100},{name:"ROUGE-1\n衡量单个词在生成文本与\n参考文本之间的覆盖率",max:100},{name:"ROUGE-2\n衡量两个词在生成文本与\n参考文本之间的覆盖率",max:100},{name:"BESTSCore\n衡量生成文本与参考文本句子之间语义\n相似度",max:100},{name:"BLEU-2\n衡量两个词在生成文本与\n参考文本之间的准确率",max:100},{name:"BLEU-1\n衡量单个词在生成文本与\n参考文本之间的准确率",max:100}],axisLine:{show:!0,symbol:["none","none"]}},series:[{name:`${e.name}`,type:"radar",symbol:"none",data:[{value:[null==(l=e.data)?void 0:l.predictRougeL.toFixed(2),null==(t=e.data)?void 0:t.predictRouge1.toFixed(2),null==(i=e.data)?void 0:i.predictRouge2.toFixed(2),null==(a=e.data)?void 0:a.predictBleu2.toFixed(2),null==(s=e.data)?void 0:s.predictBleu3.toFixed(2),null==(n=e.data)?void 0:n.predictBleu4.toFixed(2)],name:"XZmodel"}],areaStyle:{color:"rgba(57, 208, 181, 0.6)"},lineStyle:{color:"rgba(57, 208, 181, .6)"}},{name:`${e.modelBaseName}`,type:"radar",symbol:"none",data:[{value:[null==(d=e.data)?void 0:d.basePredictRougeL.toFixed(2),null==(r=e.data)?void 0:r.basePredictRouge1.toFixed(2),null==(o=e.data)?void 0:o.basePredictRouge2.toFixed(2),null==(c=e.data)?void 0:c.basePredictBleu2.toFixed(2),null==(x=e.data)?void 0:x.basePredictBleu3.toFixed(2),null==(m=e.data)?void 0:m.basePredictBleu4.toFixed(2)],name:"XZmodel"}],areaStyle:{color:"rgba(41, 85, 231,.8)"},lineStyle:{color:"rgba(41, 85, 231, .8)"}}]};u.setOption(h)}})()})),i.jsxs(i.Fragment,{children:[i.jsx("div",{children:i.jsx("div",{style:{fontSize:"16px",fontWeight:"500",height:"40px",margin:"8px",padding:"0 16px"},children:"评估报告"})}),i.jsx("div",{ref:y,className:B,style:{width:"100%",height:"450px"}}),i.jsx("div",{className:R,children:i.jsx(n,{size:"small",layout:"vertical",bordered:!0,items:k,column:7})}),i.jsx(d,{title:i.jsxs(s,{children:[i.jsx("label",{children:"难点样本"}),i.jsx(a,{title:"集中展示倒数20个测试集中对应的问题，模型生成的答案与真实答案的对比。",children:i.jsx(r,{style:{color:"rgba(195, 202, 213, 1)"}})})]}),onClose:()=>{N(!1)},open:S,closeIcon:!1,size:"large",children:w.map((e=>i.jsx(z,{question:e.question,text:e.text,answer:e.answer,score:e.score})))})]})},X=({status:e,text:l})=>i.jsx(u,{status:e,text:l}),E=()=>{const a=t(),[n,d]=l.useState(!1),[r,u]=l.useState(null),{id:v,name:p}=e(),[j,b]=l.useState(!1),[w,C]=l.useState(new Object),[B,R]=l.useState(new Object),[_,F]=l.useState(),[L,U]=l.useState(new Object),[z,E]=l.useState();l.useRef();const D={id:v||"-",name:w.modelName,desc:w.introduction,datasetName:(null==L?void 0:L.datasets)||"-",testSetRadio:(null==L?void 0:L.datasetRadio)?100*(null==L?void 0:L.datasetRadio)+"%":"-",modelBaseName:(null==L?void 0:L.modelBaseName)||"-",trainingStrategy:(null==L?void 0:L.trainStrategy)||"-",data:_,modelIntro:w.introduction,interationNumber:(null==L?void 0:L.interationNumber)||"-",batchSize:(null==L?void 0:L.batchSize)||"-",learnRate:(null==L?void 0:L.learnRate)||"-",trainProgress:(null==L?void 0:L.trainProgress)?i.jsx(i.Fragment,{children:i.jsx(o,{percent:Number(null==L?void 0:L.trainProgress),type:"line"})}):"-",status:(H=null==L?void 0:L.status,(7===H?"在线":5===H?"离线":2===H?"训练中":3===H?"训练失败":void 0)||"-"),serverName:(null==z?void 0:z.serverName)||"-",modelUrl:(null==w?void 0:w.modelUrl)&&"null"!==w.modelUrl?w.modelUrl:"离线"};var H;const O=()=>{v&&y(v).then((e=>{var l,t,i,a,s,n,d;if(200===(null==(l=e.data)?void 0:l.code)){const l=null==(t=e.data)?void 0:t.data.modelInfo,r=null==(i=e.data)?void 0:i.data.trainDetail,o=null==(s=null==(a=e.data)?void 0:a.data.modelRadar)?void 0:s.data,c=null==(d=null==(n=e.data)?void 0:n.data)?void 0:d.serverResponse;null===o&&h.warning("模型评分生成中，请耐心等待"),C(l),U(r),F(o),E(c)}}))};return l.useEffect((()=>{O()}),[]),i.jsxs(i.Fragment,{children:[i.jsx(c,{children:i.jsxs("div",{className:"createTaskContent",children:[i.jsxs("div",{className:N,children:[i.jsxs(s,{size:20,style:{display:"inline-flex",alignItems:"center"},children:[i.jsx(x,{style:{fontSize:"12px",width:"36px",height:"36px"},shape:"circle",icon:i.jsx(m,{}),onClick:()=>a("/main/finetune")}),i.jsx("div",{className:"mediumText",style:{fontSize:"20px",lineHeight:"36px",fontWeight:"500"},children:"模型详情"}),i.jsx(X,{status:7===w.status||6===w.status?"success":"error",text:7===w.status||6===w.status?"在线":"离线"})]}),i.jsx(s,{children:7!==w.status&&i.jsx(x,{type:"primary",size:"large",shape:"round",style:{backgroundColor:"black",fontSize:"14px",fontWeight:"700",width:"100px"},className:"boldText",onClick:()=>{d(!0)},children:"删除"})})]}),i.jsxs("div",{style:{display:"flex",gap:"20px",padding:"20px",borderRadius:"8px",width:"100%",margin:"0 auto"},children:[i.jsxs("div",{style:{flex:1,backgroundColor:"#ffffff",borderRadius:"24px",height:"740px",boxShadow:"0 4px 8px rgba(0, 0, 0, 0.1)"},children:[i.jsx("div",{style:{fontSize:"16px",fontWeight:"500",height:"40px",margin:"8px",padding:"0 16px"},children:"模型训练"}),i.jsx(k,{trainingData:D})]}),i.jsx("div",{style:{flex:3,backgroundColor:"#ffffff",borderRadius:"24px",height:"740px",boxShadow:"0 4px 8px rgba(0, 0, 0, 0.1)"},children:i.jsx(P,{trainingData:D})})]}),i.jsx(g,{visible:j,titltext:"选择上线时间",buttontext:"确认上线",timetext:"持续",onButtonClick:()=>{b(!1)},OnClose:()=>{b(!1)},children:i.jsx(i.Fragment,{})})]})}),i.jsx(S,{visible:n,onCancel:()=>d(!1),onDelete:async()=>{try{const e=Number(v);await f(e)?(a(-1),h.success("删除成功")):h.error("删除失败")}catch(e){h.error("删除失败")}},title:"提示",content:"确定要删除模型吗？"})]})};export{E as default};

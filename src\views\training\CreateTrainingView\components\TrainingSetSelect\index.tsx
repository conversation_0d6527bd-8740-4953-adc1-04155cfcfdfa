import { Flex, Form, RadioChangeEvent, Segmented, Select, Tooltip } from "antd";
import classes from "./index.module.css";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import group151 from "../../../../../assets/img/group-151.svg";
import Slider, { SliderMarks } from "antd/es/slider";
import {
  batchInfo,
  iteratInfo,
  learnInfo,
  trainingSet,
} from "../../../../../utils/conts";
import { TaskType } from "../../../../../types";
import { TariningType, ModelFailData } from "../../type";

interface TrainSelectProp {
  // Trains?: TaskType[];
  // TrainSetConfig?: string;
  // TrainSet?: string;
  modelData: TariningType;
}
interface MyObject {
  modelId: number;
  modelConfigData: ModelFailData;
}
const iteratSetValueMap = (testSetStr: string) => {
  const index = iteratInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
const learnSetValueMap = (testSetStr: string) => {
  const index = learnInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
  
  
};
const changeLearnValueMap = (num: number) => {
  switch (num) {
    case 0.00001:
      return 0
      break;
    case 0.00005:
      return 25;
      break;
    case 0.0001:
        return 50;
    break;
    case 0.0005:
        return 75;
        break;
    case 0.001:
        return 100;
        break
  }
};
const batchSetValueMap = (testSetStr: string) => {
  const index = batchInfo.indexOf(testSetStr);
  console.log("index", index,testSetStr);
  
  return index > -1 ? index * 25 : 0;
};
const TrainingSetSelect = forwardRef((prop: TrainSelectProp, ref) => {
  const { srategy, learningValue, iterationLevel, batchValue, trainSetConfig } =
    prop.modelData;
    const [form] = Form.useForm();
  const training: string = localStorage.getItem("trainingData") || "{}";
  const objectArray: MyObject[] = JSON.parse(training) as MyObject[];
  const [modelFailProp, setModelFailProp] = useState<ModelFailData>(
    objectArray[0]?.modelConfigData
  );


  const [value, setValue] = useState(0);
  const [selectedItems, setSelectedItems] = useState<string>(
    srategy || modelFailProp?.properties.trainConfig.trainStrategy
  );
  const OPTIONS = ["LoRA"];
  const onChange = (e: RadioChangeEvent) => {
    setValue(e.target.value);
  };
  const [modelConfig, setModelConfig] = useState<string | number>(
    trainSetConfig || "自动配置"
  );
  const [trainingSettings, setTrainingSettings] = useState({
    iterationLevel: iterationLevel ? iteratSetValueMap(iterationLevel) : 50,
    learningValue: learningValue ? learnSetValueMap(learningValue) : 50,
    batchValue: batchValue ? batchSetValueMap(batchValue) : 50,
  });
  console.log("trainingSettings", trainingSettings.iterationLevel,
  trainingSettings.learningValue,trainingSettings.batchValue);
  
  interface CreateTaskFormType {
    taskName: string;
    fileList: string[];
    iteration: number;
    learningrate: number;
    batchprocessing: number;
  }

  const iterationroundsMarks: SliderMarks = {
    0: (
      <>
        <div style={{ color: "#8E98A7" }}>{iteratInfo[0]}</div>
        <Tooltip title={trainingSet[0]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    25: (
      <>
        <div style={{ color: "#8E98A7" }}>{iteratInfo[1]}</div>
        <Tooltip title={trainingSet[1]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    50: (
      <>
        <div style={{ color: "#8E98A7" }}>{iteratInfo[2]}</div>
        <Tooltip title={trainingSet[2]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    75: (
      <>
        <div style={{ color: "#8E98A7" }}>{iteratInfo[3]}</div>
        <Tooltip title={trainingSet[3]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    100: (
      <>
        <div style={{ color: "#8E98A7" }}>{iteratInfo[4]}</div>
        <Tooltip title={trainingSet[4]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
  };
  const learningrateMarks: SliderMarks = {
    0: (
      <>
        <div style={{ color: "#8E98A7" }}>{learnInfo[0]}</div>
        <Tooltip title={trainingSet[0]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    25: (
      <>
        <div style={{ color: "#8E98A7" }}>{learnInfo[1]}</div>
        <Tooltip title={trainingSet[1]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    50: (
      <>
        <div style={{ color: "#8E98A7" }}>{learnInfo[2]}</div>
        <Tooltip title={trainingSet[2]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    75: (
      <>
        <div style={{ color: "#8E98A7" }}>{learnInfo[3]}</div>
        <Tooltip title={trainingSet[3]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    100: (
      <>
        <div style={{ color: "#8E98A7", width: "40px" }}>{learnInfo[4]}</div>
        <Tooltip title={trainingSet[4]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
  };
  const batchprocessingMarks: SliderMarks = {
    0: (
      <>
        <div style={{ color: "#8E98A7" }}>{batchInfo[0]}</div>
        <Tooltip title={trainingSet[0]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    25: (
      <>
        <div style={{ color: "#8E98A7" }}>{batchInfo[1]}</div>
        <Tooltip title={trainingSet[1]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    50: (
      <>
        <div style={{ color: "#8E98A7" }}>{batchInfo[2]}</div>
        <Tooltip title={trainingSet[2]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    75: (
      <>
        <div style={{ color: "#8E98A7" }}>{batchInfo[3]}</div>
        <Tooltip title={trainingSet[3]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    100: (
      <>
        <div style={{ color: "#8E98A7" }}>{batchInfo[4]}</div>
        <Tooltip title={trainingSet[4]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
  };
  useImperativeHandle(ref, () => ({
    getTaskSelectData: () => {
      return {
        TrainSetConfig: modelConfig,
        Srategy: selectedItems,
        IterationLevel: iteratInfo[trainingSettings.iterationLevel / 25],
        LearningValue: learnInfo[trainingSettings.learningValue / 25],
        BatchValue: batchInfo[trainingSettings.batchValue / 25],
      };
    },
  }));
  // const getModelEstimate =() => {
  //     getEstimate()
  // }}
  const initForm=() => {
      if(prop.modelData){
        form.setFieldsValue({
         'iteration':trainingSettings.iterationLevel,
         'learningrate':trainingSettings.learningValue,
         'batchprocessing':trainingSettings.batchValue
        })
      }
  }
  useEffect(() => {
    initForm();
  })

  return (
    <>
      <div className={classes["training-content"]}>
        <div className={classes["step3"]} style={{ width: "15%" }}>
          {/* Step 3 */}
          训练配置
        </div>
        <div className={classes["operatearea"]}>
          <div className={classes["strategy"]}>
            训练策略:
            <Flex style={{ width: "340px", margin: "-10px 0 0 55px" }}>
              <Select
                size="large"
                placeholder="选择要训练的策略"
                value={selectedItems}
                onChange={setSelectedItems}
                style={{ flex: 1 }}
                //open={isTextVisible && !isDropdownDisabled}
                options={OPTIONS.map((item) => ({
                  value: item,
                  label: item,
                }))}
              />
            </Flex>
            <Tooltip
              placement="rightTop"
              title="在固定预训练大模型本身的参数的基础上，在保留自注意力模块中原始权重矩阵的基础上，
                    对权重矩阵进行低秩分解，训练过程中只更新低秩部分的参数。"
            >
              <img
                className="frame-child179"
                style={{ marginLeft: "30px" }}
                src={group151}
              />
            </Tooltip>
          </div>
          <div className={classes["parameter"]}>
            参数配置:
            <div style={{ margin: "-10px 0 0 55px" }}>
              <Form.Item<CreateTaskFormType>>
                <Segmented
                  className="createtask-segmented"
                  size="large"
                  options={[
                    {
                      label: (
                        <Tooltip title="自动配置">
                          <a
                            onClick={() => setModelConfig("自动配置")}
                            className={
                              modelConfig === "自动配置"
                                ? "model-config-active"
                                : "model-config"
                            }
                          >
                            自动配置
                          </a>
                        </Tooltip>
                      ),
                      value: "自动配置",
                    },
                    {
                      label: (
                        <Tooltip title="手动配置">
                          <a
                            onClick={() => setModelConfig("手动配置")}
                            className={
                              modelConfig === "手动配置"
                                ? "model-config-active"
                                : "model-config"
                            }
                          >
                            手动配置
                          </a>
                        </Tooltip>
                      ),
                      value: "手动配置",
                    },
                  ]}
                  value={modelConfig}
                  onChange={setModelConfig}
                />
              </Form.Item>
            </div>
          </div>
          <div className="creative">
            {modelConfig === "自动配置" ? (
                 <div>
                <Form
                form={form} 
                initialValues={
                  {
                    iteration: (modelFailProp?.properties.trainConfig.interationNumber-1)*25||0,
                    learningrate:( changeLearnValueMap(modelFailProp?.properties.trainConfig.learnRate))||0,
                    batchprocessing: (modelFailProp?.properties.trainConfig.batchSize/2)*25||0
                  }
                }>
             
                <div className={classes["disposition"]}>
                  <div className={classes["dispositionlabel"]}>
                    <label style={{ color: "#000000", lineHeight: "19px" }}>
                      迭代轮次
                    </label>
                    <Tooltip title="模型在学习过程中完整遍历训练数据集的次数，每次迭代都涉及模型尝试学习并改进其预测能力">
                      <img className="frame-child179" src={group151} />
                    </Tooltip>
                  </div>
                  <Form.Item<CreateTaskFormType> name="iteration" noStyle>
                    <Slider
                  
                      className="create-tasks-slider"
                      dots
                      tooltip={{
                        formatter: (val) => {
                          const q = val ? val : 0;
                          return trainingSet[q / 25];
                        },
                      }}
                      step={25}
                      marks={iterationroundsMarks}
                    
                      value={trainingSettings.iterationLevel}
                      onChange={(val) => {
                        //setIterationLevel(val);
                        setTrainingSettings((prevSettings) => ({
                          ...prevSettings,
                          iterationLevel: val, // 假设newValue是您要设置的新值
                        }));
                        setModelConfig("手动配置");
                      }}
                      railStyle={{
                        height: "6px",
                        background: "#F1F6F9",
                        borderTop: "1px solid #E1EAEF",
                        borderBottom: "1px solid #E1EAEF",
                      }}
                      trackStyle={{
                        height: "6px",
                        background: "#0FB698",
                        borderTop: "1px solid #0CA287",
                        borderBottom: "1px solid #0CA287",
                      }}
                      handleStyle={{}}
                      style={{
                        width: "100%",
                        display: "inline-flex",
                        margin: "unset",
                      }}
                    />
                  </Form.Item>
                </div>
                <div className={classes["disposition"]}>
                  <div className={classes["dispositionlabel"]}>
                    <label style={{ color: "#000000", lineHeight: "19px" }}>
                      学习率
                    </label>
                    <Tooltip title="在训练过程中调整模型权重的步长大小">
                      <img className="frame-child179" src={group151} />
                    </Tooltip>
                  </div>
                  <Form.Item<CreateTaskFormType> name="learningrate" noStyle>
                    <Slider
                    
                      className="create-tasks-slider"
                      dots
                      tooltip={{
                        formatter: (val) => {
                          const q = val ? val : 0;
                          return trainingSet[q / 25];
                        },
                      }}
                      step={25}
                      marks={learningrateMarks}
                      value={trainingSettings.learningValue}
                      onChange={(val) => {
                        //setLearninValue(val);
                        setTrainingSettings((prevSettings) => ({
                          ...prevSettings,
                          learningValue: val, // 假设newValue是您要设置的新值
                        }));
                        setModelConfig("手动配置");
                      }}
                      railStyle={{
                        height: "6px",
                        background: "#F1F6F9",
                        borderTop: "1px solid #E1EAEF",
                        borderBottom: "1px solid #E1EAEF",
                      }}
                      trackStyle={{
                        height: "6px",
                        background: "#0FB698",
                        borderTop: "1px solid #0CA287",
                        borderBottom: "1px solid #0CA287",
                      }}
                      handleStyle={{}}
                      style={{
                        width: "100%",
                        display: "inline-flex",
                        margin: "unset",
                      }}
                    />
                  </Form.Item>
                </div>
                <div className={classes["disposition"]}>
                  <div className={classes["dispositionlabel"]}>
                    <label style={{ color: "#000000", lineHeight: "19px" }}>
                      Top-p采样
                    </label>
                    <Tooltip title="通过动态调整候选词池的大小，影响文本的创新性和多样性。">
                      <img className="frame-child179" src={group151} />
                    </Tooltip>
                  </div>
                  <Form.Item<CreateTaskFormType> name="batchprocessing" noStyle>
                    <Slider
                     
                      className="create-tasks-slider"
                      dots
                      tooltip={{
                        formatter: (val) => {
                          const q = val ? val : 0;
                          return trainingSet[q / 25];
                        },
                      }}
                      step={25}
                      marks={batchprocessingMarks}
                      value={trainingSettings.batchValue}
                      onChange={(val) => {
                        //setBatchValue(val);
                        setTrainingSettings((prevSettings) => ({
                          ...prevSettings,
                          batchValue: val, // 假设newValue是您要设置的新值
                        }));
                        setModelConfig("手动配置");
                      }}
                      railStyle={{
                        height: "6px",
                        background: "#F1F6F9",
                        borderTop: "1px solid #E1EAEF",
                        borderBottom: "1px solid #E1EAEF",
                      }}
                      trackStyle={{
                        height: "6px",
                        background: "#0FB698",
                        borderTop: "1px solid #0CA287",
                        borderBottom: "1px solid #0CA287",
                      }}
                      handleStyle={{}}
                      style={{
                        width: "100%",
                        display: "inline-flex",
                        margin: "unset",
                      }}
                    />
                  </Form.Item>
                </div>
             </Form> 
             </div>
            ) : (
              <div>
                 <Form
                  form={form} 
                 initialValues={
                  {
                    iteration: (modelFailProp?.properties.trainConfig.interationNumber-1)*25||0,
                    learningrate:( changeLearnValueMap(modelFailProp?.properties.trainConfig.learnRate))||0,
                    batchprocessing: (modelFailProp?.properties.trainConfig.batchSize/2)*25||0
                  }
                }>
                <div className={classes["disposition"]}>
                  <div className={classes["dispositionlabel"]}>
                    <label style={{ color: "#000000", lineHeight: "19px" }}>
                      迭代轮次
                    </label>
                    <Tooltip title="模型在学习过程中完整遍历训练数据集的次数，每次迭代都涉及模型尝试学习并改进其预测能力">
                      <img className="frame-child179" src={group151} />
                    </Tooltip>
                  </div>
                  <Form.Item<CreateTaskFormType> name="iteration" noStyle>
                    <Slider
                    
                      className="create-tasks-slider"
                      dots
                      tooltip={{
                        formatter: (val) => {
                          const q = val ? val : 0;
                          return trainingSet[q / 25];
                        },
                      }}
                      step={25}
                      marks={iterationroundsMarks}
                    
                      value={trainingSettings.iterationLevel}
                      onChange={(val) => {
                        //setIterationLevel(val);
                        setTrainingSettings((prevSettings) => ({
                          ...prevSettings,
                          iterationLevel: val, // 假设newValue是您要设置的新值
                        }));
                      }}
                      railStyle={{
                        height: "6px",
                        background: "#F1F6F9",
                        borderTop: "1px solid #E1EAEF",
                        borderBottom: "1px solid #E1EAEF",
                      }}
                      trackStyle={{
                        height: "6px",
                        background: "#0FB698",
                        borderTop: "1px solid #0CA287",
                        borderBottom: "1px solid #0CA287",
                      }}
                      handleStyle={{}}
                      style={{
                        //width: "100%",
                        width: "100%",
                        display: "inline-flex",
                        margin: "unset",
                      }}
                    />
                  </Form.Item>
                </div>
                <div className={classes["disposition"]}>
                  <div className={classes["dispositionlabel"]}>
                    <label style={{ color: "#000000", lineHeight: "19px" }}>
                      学习率
                    </label>
                    <Tooltip title="在训练过程中调整模型权重的步长大小">
                      <img className="frame-child179" src={group151} />
                    </Tooltip>
                  </div>
                  <Form.Item<CreateTaskFormType> name="learningrate" noStyle>
                    <Slider
                     
                      className="create-tasks-slider"
                      dots
                      tooltip={{
                        formatter: (val) => {
                          const q = val ? val : 0;
                          return trainingSet[q / 25];
                        },
                      }}
                      step={25}
                      marks={learningrateMarks}
                      value={trainingSettings.learningValue}
                      onChange={(val) => {
                        //setLearninValue(val);
                        setTrainingSettings((prevSettings) => ({
                          ...prevSettings,
                          learningValue: val, // 假设newValue是您要设置的新值
                        }));
                      }}
                      railStyle={{
                        height: "6px",
                        background: "#F1F6F9",
                        borderTop: "1px solid #E1EAEF",
                        borderBottom: "1px solid #E1EAEF",
                      }}
                      trackStyle={{
                        height: "6px",
                        background: "#0FB698",
                        borderTop: "1px solid #0CA287",
                        borderBottom: "1px solid #0CA287",
                      }}
                      handleStyle={{}}
                      style={{
                        width: "100%",
                        display: "inline-flex",
                        margin: "unset",
                      }}
                    />
                  </Form.Item>
                </div>
                <div className={classes["disposition"]}>
                  <div className={classes["dispositionlabel"]}>
                    <label style={{ color: "#000000", lineHeight: "19px" }}>
                      Top-p采样
                    </label>
                    <Tooltip title="通过动态调整候选词池的大小，影响文本的创新性和多样性。">
                      <img className="frame-child179" src={group151} />
                    </Tooltip>
                  </div>
                  <Form.Item<CreateTaskFormType> name="batchprocessing" noStyle>
                    <Slider
                     
                      className="create-tasks-slider"
                      dots
                      tooltip={{
                        formatter: (val) => {
                          const q = val ? val : 0;
                          return trainingSet[q / 25];
                        },
                      }}
                      step={25}
                      marks={batchprocessingMarks}
                      value={trainingSettings.batchValue}
                      onChange={(val) => {
                        //setBatchValue(val);
                        setTrainingSettings((prevSettings) => ({
                          ...prevSettings,
                          batchValue: val, // 假设newValue是您要设置的新值
                        }));
                      }}
                      railStyle={{
                        height: "6px",
                        background: "#F1F6F9",
                        borderTop: "1px solid #E1EAEF",
                        borderBottom: "1px solid #E1EAEF",
                      }}
                      trackStyle={{
                        height: "6px",
                        background: "#0FB698",
                        borderTop: "1px solid #0CA287",
                        borderBottom: "1px solid #0CA287",
                      }}
                      handleStyle={{}}
                      style={{
                        width: "100%",
                        display: "inline-flex",
                        margin: "unset",
                      }}
                    />
                  </Form.Item>
                </div>
                </Form>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
});
export default TrainingSetSelect;

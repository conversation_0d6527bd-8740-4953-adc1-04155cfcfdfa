import{bM as e}from"./vendor-5xQGrmEQ.js";import{c as t,g as r}from"./file-utils-DYD-epjE.js";function n(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var o="function"==typeof Symbol&&Symbol.observable||"@@observable",i=function(){return Math.random().toString(36).substring(7).split("").join(".")},a={INIT:"@@redux/INIT"+i(),REPLACE:"@@redux/REPLACE"+i(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+i()}};function f(e,t,r){var i;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(n(1));return r(f)(e,t)}if("function"!=typeof e)throw new Error(n(2));var u=e,c=t,l=[],s=l,p=!1;function h(){s===l&&(s=l.slice())}function d(){if(p)throw new Error(n(3));return c}function v(e){if("function"!=typeof e)throw new Error(n(4));if(p)throw new Error(n(5));var t=!0;return h(),s.push(e),function(){if(t){if(p)throw new Error(n(6));t=!1,h();var r=s.indexOf(e);s.splice(r,1),l=null}}}function g(e){if(!function(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))throw new Error(n(7));if(void 0===e.type)throw new Error(n(8));if(p)throw new Error(n(9));try{p=!0,c=u(c,e)}finally{p=!1}for(var t=l=s,r=0;r<t.length;r++){(0,t[r])()}return e}return g({type:a.INIT}),(i={dispatch:g,subscribe:v,getState:d,replaceReducer:function(e){if("function"!=typeof e)throw new Error(n(10));u=e,g({type:a.REPLACE})}})[o]=function(){var e,t=v;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(n(11));function r(){e.next&&e.next(d())}return r(),{unsubscribe:t(r)}}})[o]=function(){return this},e},i}function u(e){for(var t=Object.keys(e),r={},o=0;o<t.length;o++){var i=t[o];"function"==typeof e[i]&&(r[i]=e[i])}var f,u=Object.keys(r);try{!function(e){Object.keys(e).forEach((function(t){var r=e[t];if(void 0===r(void 0,{type:a.INIT}))throw new Error(n(12));if(void 0===r(void 0,{type:a.PROBE_UNKNOWN_ACTION()}))throw new Error(n(13))}))}(r)}catch(c){f=c}return function(e,t){if(void 0===e&&(e={}),f)throw f;for(var o=!1,i={},a=0;a<u.length;a++){var c=u[a],l=r[c],s=e[c],p=l(s,t);if(void 0===p)throw t&&t.type,new Error(n(14));i[c]=p,o=o||p!==s}return(o=o||u.length!==Object.keys(e).length)?i:e}}function c(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function l(){for(var t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];return function(t){return function(){var o=t.apply(void 0,arguments),i=function(){throw new Error(n(15))},a={getState:o.getState,dispatch:function(){return i.apply(void 0,arguments)}},f=r.map((function(e){return e(a)}));return i=c.apply(void 0,f)(o.dispatch),e(e({},o),{},{dispatch:i})}}}function s(e){return function(t){var r=t.dispatch,n=t.getState;return function(t){return function(o){return"function"==typeof o?o(r,n,e):t(o)}}}}var p=s();p.withExtraArgument=s;var h,d={exports:{}};const v=r((h||(h=1,function(e){function r(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}function n(e,t){Object.defineProperty(this,"kind",{value:e,enumerable:!0}),t&&t.length&&Object.defineProperty(this,"path",{value:t,enumerable:!0})}function o(e,t,r){o.super_.call(this,"E",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0}),Object.defineProperty(this,"rhs",{value:r,enumerable:!0})}function i(e,t){i.super_.call(this,"N",e),Object.defineProperty(this,"rhs",{value:t,enumerable:!0})}function a(e,t){a.super_.call(this,"D",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0})}function f(e,t,r){f.super_.call(this,"A",e),Object.defineProperty(this,"index",{value:t,enumerable:!0}),Object.defineProperty(this,"item",{value:r,enumerable:!0})}function u(e,t,r){var n=e.slice(t+1||e.length);return e.length=t<0?e.length+t:t,e.push.apply(e,n),e}function c(e){var t=void 0===e?"undefined":P(e);return"object"!==t?t:e===Math?"math":null===e?"null":Array.isArray(e)?"array":"[object Date]"===Object.prototype.toString.call(e)?"date":"function"==typeof e.toString&&/^\/.*\//.test(e.toString())?"regexp":"object"}function l(e,t,r,n,s,p,h){h=h||[];var d=(s=s||[]).slice(0);if(void 0!==p){if(n){if("function"==typeof n&&n(d,p))return;if("object"===(void 0===n?"undefined":P(n))){if(n.prefilter&&n.prefilter(d,p))return;if(n.normalize){var v=n.normalize(d,p,e,t);v&&(e=v[0],t=v[1])}}}d.push(p)}"regexp"===c(e)&&"regexp"===c(t)&&(e=e.toString(),t=t.toString());var g=void 0===e?"undefined":P(e),y=void 0===t?"undefined":P(t),b="undefined"!==g||h&&h[h.length-1].lhs&&h[h.length-1].lhs.hasOwnProperty(p),w="undefined"!==y||h&&h[h.length-1].rhs&&h[h.length-1].rhs.hasOwnProperty(p);if(!b&&w)r(new i(d,t));else if(!w&&b)r(new a(d,e));else if(c(e)!==c(t))r(new o(d,e,t));else if("date"===c(e)&&e-t!=0)r(new o(d,e,t));else if("object"===g&&null!==e&&null!==t)if(h.filter((function(t){return t.lhs===e})).length)e!==t&&r(new o(d,e,t));else{if(h.push({lhs:e,rhs:t}),Array.isArray(e)){var m;for(e.length,m=0;m<e.length;m++)m>=t.length?r(new f(d,m,new a(void 0,e[m]))):l(e[m],t[m],r,n,d,m,h);for(;m<t.length;)r(new f(d,m,new i(void 0,t[m++])))}else{var E=Object.keys(e),j=Object.keys(t);E.forEach((function(o,i){var a=j.indexOf(o);a>=0?(l(e[o],t[o],r,n,d,o,h),j=u(j,a)):l(e[o],void 0,r,n,d,o,h)})),j.forEach((function(e){l(void 0,t[e],r,n,d,e,h)}))}h.length=h.length-1}else e!==t&&("number"===g&&isNaN(e)&&isNaN(t)||r(new o(d,e,t)))}function s(e,t,r,n){return n=n||[],l(e,t,(function(e){e&&n.push(e)}),r),n.length?n:void 0}function p(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":p(o[r.path[n]],r.index,r.item);break;case"D":delete o[r.path[n]];break;case"E":case"N":o[r.path[n]]=r.rhs}}else switch(r.kind){case"A":p(e[t],r.index,r.item);break;case"D":e=u(e,t);break;case"E":case"N":e[t]=r.rhs}return e}function h(e,t,r){if(e&&t&&r&&r.kind){for(var n=e,o=-1,i=r.path?r.path.length-1:0;++o<i;)void 0===n[r.path[o]]&&(n[r.path[o]]="number"==typeof r.path[o]?[]:{}),n=n[r.path[o]];switch(r.kind){case"A":p(r.path?n[r.path[o]]:n,r.index,r.item);break;case"D":delete n[r.path[o]];break;case"E":case"N":n[r.path[o]]=r.rhs}}}function d(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":d(o[r.path[n]],r.index,r.item);break;case"D":case"E":o[r.path[n]]=r.lhs;break;case"N":delete o[r.path[n]]}}else switch(r.kind){case"A":d(e[t],r.index,r.item);break;case"D":case"E":e[t]=r.lhs;break;case"N":e=u(e,t)}return e}function v(e,t,r){if(e&&t&&r&&r.kind){var n,o,i=e;for(o=r.path.length-1,n=0;n<o;n++)void 0===i[r.path[n]]&&(i[r.path[n]]={}),i=i[r.path[n]];switch(r.kind){case"A":d(i[r.path[n]],r.index,r.item);break;case"D":case"E":i[r.path[n]]=r.lhs;break;case"N":delete i[r.path[n]]}}}function g(e,t,r){e&&t&&l(e,t,(function(n){r&&!r(e,t,n)||h(e,t,n)}))}function y(e){return"color: "+_[e].color+"; font-weight: bold"}function b(e){var t=e.kind,r=e.path,n=e.lhs,o=e.rhs,i=e.index,a=e.item;switch(t){case"E":return[r.join("."),n,"→",o];case"N":return[r.join("."),o];case"D":return[r.join(".")];case"A":return[r.join(".")+"["+i+"]",a];default:return[]}}function w(e,t,r,n){var o=s(e,t);try{n?r.groupCollapsed("diff"):r.group("diff")}catch(i){r.log("diff")}o?o.forEach((function(e){var t=e.kind,n=b(e);r.log.apply(r,["%c "+_[t].text,y(t)].concat(C(n)))})):r.log("—— no diff ——");try{r.groupEnd()}catch(i){r.log("—— diff end —— ")}}function m(e,t,r,n){switch(void 0===e?"undefined":P(e)){case"object":return"function"==typeof e[n]?e[n].apply(e,C(r)):e[n];case"function":return e(t);default:return e}}function E(e){var t=e.timestamp,r=e.duration;return function(e,n,o){var i=["action"];return i.push("%c"+String(e.type)),t&&i.push("%c@ "+n),r&&i.push("%c(in "+o.toFixed(2)+" ms)"),i.join(" ")}}function j(e,t){var r=t.logger,n=t.actionTransformer,o=t.titleFormatter,i=void 0===o?E(t):o,a=t.collapsed,f=t.colors,u=t.level,c=t.diff,l=void 0===t.titleFormatter;e.forEach((function(o,s){var p=o.started,h=o.startedTime,d=o.action,v=o.prevState,g=o.error,y=o.took,b=o.nextState,E=e[s+1];E&&(b=E.prevState,y=E.started-p);var j=n(d),x="function"==typeof a?a((function(){return b}),d,o):a,O=N(h),S=f.title?"color: "+f.title(j)+";":"",A=["color: gray; font-weight: lighter;"];A.push(S),t.timestamp&&A.push("color: gray; font-weight: lighter;"),t.duration&&A.push("color: gray; font-weight: lighter;");var k=i(j,O,y);try{x?f.title&&l?r.groupCollapsed.apply(r,["%c "+k].concat(A)):r.groupCollapsed(k):f.title&&l?r.group.apply(r,["%c "+k].concat(A)):r.group(k)}catch(M){r.log(k)}var D=m(u,j,[v],"prevState"),P=m(u,j,[j],"action"),C=m(u,j,[g,v],"error"),T=m(u,j,[b],"nextState");if(D)if(f.prevState){var _="color: "+f.prevState(v)+"; font-weight: bold";r[D]("%c prev state",_,v)}else r[D]("prev state",v);if(P)if(f.action){var I="color: "+f.action(j)+"; font-weight: bold";r[P]("%c action    ",I,j)}else r[P]("action    ",j);if(g&&C)if(f.error){var F="color: "+f.error(g,v)+"; font-weight: bold;";r[C]("%c error     ",F,g)}else r[C]("error     ",g);if(T)if(f.nextState){var R="color: "+f.nextState(b)+"; font-weight: bold";r[T]("%c next state",R,b)}else r[T]("next state",b);c&&w(v,b,r,x);try{r.groupEnd()}catch(M){r.log("—— log end ——")}}))}function x(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Object.assign({},I,e),r=t.logger,n=t.stateTransformer,o=t.errorTransformer,i=t.predicate,a=t.logErrors,f=t.diffPredicate;if(void 0===r)return function(){return function(e){return function(t){return e(t)}}};if(e.getState&&e.dispatch)return function(){return function(e){return function(t){return e(t)}}};var u=[];return function(e){var r=e.getState;return function(e){return function(c){if("function"==typeof i&&!i(r,c))return e(c);var l={};u.push(l),l.started=D.now(),l.startedTime=new Date,l.prevState=n(r()),l.action=c;var s=void 0;if(a)try{s=e(c)}catch(h){l.error=o(h)}else s=e(c);l.took=D.now()-l.started,l.nextState=n(r());var p=t.diff&&"function"==typeof f?f(r,c):t.diff;if(j(u,Object.assign({},t,{diff:p})),u.length=0,l.error)throw l.error;return s}}}}var O,S,A=function(e,t){return new Array(t+1).join(e)},k=function(e,t){return A("0",t-e.toString().length)+e},N=function(e){return k(e.getHours(),2)+":"+k(e.getMinutes(),2)+":"+k(e.getSeconds(),2)+"."+k(e.getMilliseconds(),3)},D="undefined"!=typeof performance&&null!==performance&&"function"==typeof performance.now?performance:Date,P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},C=function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)},T=[];O="object"===(void 0===t?"undefined":P(t))&&t?t:"undefined"!=typeof window?window:{},(S=O.DeepDiff)&&T.push((function(){void 0!==S&&O.DeepDiff===s&&(O.DeepDiff=S,S=void 0)})),r(o,n),r(i,n),r(a,n),r(f,n),Object.defineProperties(s,{diff:{value:s,enumerable:!0},observableDiff:{value:l,enumerable:!0},applyDiff:{value:g,enumerable:!0},applyChange:{value:h,enumerable:!0},revertChange:{value:v,enumerable:!0},isConflict:{value:function(){return void 0!==S},enumerable:!0},noConflict:{value:function(){return T&&(T.forEach((function(e){e()})),T=null),s},enumerable:!0}});var _={E:{color:"#2196F3",text:"CHANGED:"},N:{color:"#4CAF50",text:"ADDED:"},D:{color:"#F44336",text:"DELETED:"},A:{color:"#2196F3",text:"ARRAY:"}},I={level:"log",logger:console,logErrors:!0,collapsed:void 0,predicate:void 0,duration:!1,timestamp:!0,stateTransformer:function(e){return e},actionTransformer:function(e){return e},errorTransformer:function(e){return e},colors:{title:function(){return"inherit"},prevState:function(){return"#9E9E9E"},action:function(){return"#03A9F4"},nextState:function(){return"#4CAF50"},error:function(){return"#F20404"}},diff:!1,diffPredicate:void 0,transformer:void 0},F=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.dispatch,r=e.getState;return"function"==typeof t||"function"==typeof r?x()({dispatch:t,getState:r}):void 0};e.defaults=I,e.createLogger=x,e.logger=F,e.default=F,Object.defineProperty(e,"__esModule",{value:!0})}(d.exports)),d.exports));export{l as a,u as b,f as c,v as l,p as t};

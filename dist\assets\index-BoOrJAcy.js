import{r as e,j as t,K as s,B as a,u as l,k as i,I as r,p as n,S as o,f as d,l as c,q as m,n as h,o as u,i as x,ab as p,ac as g,ad as j,t as f,_ as v}from"./react-core-BrQk45h1.js";import{U as y}from"./UploadList-CCFxdIyn.js";import{U as S}from"./UploadErrorModal-Dpa2hJTx.js";import{D as b}from"./DatasetTable-qqjeGPbM.js";import{u as k}from"./dataset-CNkWbBvO.js";import{D as N}from"./types-ccCJOgIs.js";import{g as T,c as w,a as I}from"./task-ClBf-SyM.js";import{g as C,q as E}from"./conts-BxhewW4k.js";import{g as z}from"./group-151-0eGrjKh_.js";import{i as L}from"./info-icon-DzE4dkR7.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./empty-logo-5H_PPUCG.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const A=e.forwardRef((({OnClose:l,visible:i,onRemoveSelection:r},n)=>{const[o,d]=e.useState("datasetName"),[c,m]=e.useState(""),[h,u]=e.useState([]),[x,p]=e.useState("asc"),[g,j]=e.useState([]),f=e.useRef(null);e.useImperativeHandle(n,(()=>({removeSelection:e=>{f.current&&f.current.removeSelection(e)}})));const v=()=>{l(g),y()},y=()=>{d("datasetName"),m(""),u([]),p("asc"),j([])};return t.jsx(s,{centered:!0,title:"选择源数据集",keyboard:!1,maskClosable:!1,width:"55%",open:i,onOk:v,onCancel:()=>{l(),y()},footer:[t.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[t.jsx("label",{className:"info-label",children:"只能选择解析完成的源数据集哦"}),t.jsx(a,{type:"primary",onClick:v,shape:"round",className:"primary-btn boldText",style:{width:124},children:"确认选择"})]})],children:t.jsx(b,{ref:f,ShowActionColumn:!1,noEditTag:!0,onSelectRows:e=>j(e)},"ModalTable")})})),F=["“你是一个军工领域算法科学家，你要使用资料中的武器装备信息训练领域问答大模型”","“你是一位军事情报分析算法科学家，你要使用资料中的军事情报信息训练领域问答大模型”","“你是一位网络安全算法科学家，你要使用资料中的网络威胁和安全漏洞信息训练领域问答大模型”","“你是一位医学研究算法科学家，你要使用资料中的临床试验数据和医疗诊断信息训练领域问答大模型”","“你是一位生物安全领域的算法科学家，你要使用资料中的生物危害数据信息训练领域问答大模型”","“你是一位医疗设备优化算法科学家，你要使用资料中的医疗设备性能和技术规格信息训练领域问答大模型”"],D=["需求描述内的文本均可更改，字数限制600字"],{Dragger:R}=v,B=()=>{const{state:s}=l(),v=i(),[b,R]=e.useState(""),[B,q]=e.useState("自动配置"),[O,$]=e.useState("军工领域"),[M,U]=e.useState(""),[P,H]=e.useState([]),[V,_]=e.useState(!1),[W,Z]=e.useState(!1),{TextArea:G}=r,[K,Q]=e.useState(""),[J,X]=e.useState(-1),[Y,ee]=e.useState({id:0,templateName:"",roleBackground:"",detailedDescription:""}),[te,se]=e.useState(!1),[ae,le]=e.useState("装备维修，运用参考，性能参考"),[ie,re]=e.useState(!1),[ne,oe]=e.useState([]),[de,ce]=e.useState([]),[me,he]=e.useState(0),ue=e.useRef(null),xe=e.useRef(null),[pe,ge]=e.useState(!0),[je,fe]=e.useState(50),[ve,ye]=e.useState(50),[Se]=n.useForm(),be={0:t.jsx(h,{title:C[0],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})}),25:t.jsx(h,{title:C[1],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})}),50:t.jsx(h,{title:C[2],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})}),75:t.jsx(h,{title:C[3],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})}),100:t.jsx(h,{title:C[4],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})},ke={0:t.jsx(h,{title:E[0],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})}),25:t.jsx(h,{title:E[1],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})}),50:t.jsx(h,{title:E[2],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})}),75:t.jsx(h,{title:E[3],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})}),100:t.jsx(h,{title:E[4],children:t.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})};e.useEffect((()=>{if(s&&s.selectedRows){const{selectedRows:e}=s,t=P;e.forEach((e=>{const s=new Object;s.dataSetId=e.id,s.name=e.datasetName,s.tags=e.tags,s.parseProcess=e.progress,s.dataSource=0,e.datasetStatus===N.sucess?s.status="success":e.datasetStatus===N.failed&&(s.status="error"),t.push(s)})),H(t)}}),[]),e.useEffect((()=>{const e=ue.current,t=()=>{ge(!1)},s=()=>{ge(!0)};return e&&(e.addEventListener("mouseenter",t),e.addEventListener("mouseleave",s)),()=>{e&&(e.removeEventListener("mouseenter",t),e.removeEventListener("mouseleave",s))}}),[]),e.useEffect((()=>{let e;return pe?(e=setInterval((()=>{he((e=>(e+1)%F.length))}),2e3),()=>clearInterval(e)):()=>clearInterval(e)}),[pe]),e.useEffect((()=>{T().then((e=>{if(200===e.data.code){const t=e.data;ce(t.data)}})).catch((e=>{}))}),[]);return t.jsx(o,{children:t.jsxs("div",{className:"createTaskContent",children:[t.jsxs(d,{size:20,style:{display:"inline-flex",alignItems:"center"},children:[t.jsx(a,{style:{fontSize:"12px",width:"36px",height:"36px"},shape:"circle",icon:t.jsx(c,{}),onClick:()=>v(-1)}),t.jsx("div",{className:"mediumText",style:{fontSize:"28px",lineHeight:"36px",fontWeight:"500"},children:"生成训练数据"})]}),t.jsxs("div",{className:"createTaskArea",style:{marginBottom:"47px"},children:[t.jsxs(n,{form:Se,className:"reqularText",name:"createTaskForm",layout:"vertical",initialValues:{},onFinish:e=>{if(!/^(?!.*(?:^|[,，])\d+(?:[,，]|$))[\u4e00-\u9fa5a-zA-Z0-9,，]+$/.test(ae))return void f.error("标签格式不正确，请勿输入特殊符号或纯数字标签");ae&&(e.description=K),e.domain=Y.templateName;const t={splitLevel:3,questionDensity:3};"手动配置"===B&&(t.splitLevel=je?je/25+1:3,t.questionDensity=ve?ve/25+1:3);const s={taskName:e.taskName,datasetList:P.map((e=>e.dataSetId)),taskConfigMap:t,description:e.description,domain:e.domain,tags:te?ae.split("，").filter((e=>e.trim())):[]};w(s).then((e=>{var t,s;200===(null==(t=null==e?void 0:e.data)?void 0:t.code)?v("/main/task"):f.error(null==(s=null==e?void 0:e.data)?void 0:s.message)}))},onFinishFailed:e=>{},autoComplete:"off",children:[t.jsx(n.Item,{name:"taskName",label:"任务名称",rules:[{max:32,message:"任务名称不能超过32个字符"},{pattern:/^[\u4e00-\u9fa5a-zA-Z0-9,——()“”_]+$/,message:"仅允许中文、英文、数字及符号 ,——()“”_"}],children:t.jsx(r,{placeholder:"请输入任务名称",value:b,onChange:e=>R(e.target.value),style:{width:"30rem",height:"2.5rem"}})}),t.jsxs(n.Item,{name:"fileList",label:"源数据集",children:[t.jsx(a,{className:"createTaskSelectBtn reqularText",onClick:()=>{_(!0)},children:"在平台库中选择"}),t.jsx("div",{children:t.jsx(y,{fileList:P,onAddTag:(e,t)=>{const s=P;s.forEach((s=>{s.dataSetId===e&&(s.tags=t)})),H(s)},noEditTag:!0,onDelFile:e=>{const t=P[e];t.dataSetId&&xe.current&&xe.current.removeSelection(t.dataSetId);const s=P.filter(((t,s)=>s!==e));H(s)},className:"create-task-file-list",onReUpload:e=>{const t=P[e];const s=t;s.status="uploading",k({importDataset:t,tags:[]}).then((t=>{var a;200===(null==(a=t.data)?void 0:a.code)?(s.status="success",s.dataSetId=t.data.data):s.status="error",P[e]=s,H(P)}))}})})]}),t.jsx(n.Item,{label:"模型参数输入",children:t.jsx(m,{className:"createtask-segmented",size:"large",options:[{label:t.jsx(h,{title:"自动配置",children:t.jsx("a",{onClick:()=>q("自动配置"),className:"自动配置"===B?"model-config-active":"model-config",children:"自动配置"})}),value:"自动配置"},{label:t.jsx(h,{title:"手动配置",children:t.jsx("a",{onClick:()=>q("手动配置"),className:"手动配置"===B?"model-config-active":"model-config",children:"手动配置"})}),value:"手动配置"}],value:B,onChange:q})}),"手动配置"===B?t.jsxs("div",{children:[t.jsxs("div",{className:"createTaskItem",children:[t.jsxs("div",{className:"customConfigLabel",children:[t.jsx("label",{className:"crateTaskDraggerLabel reqularText",children:"段落精细度"}),t.jsx(h,{title:"该参数决定推理过程中每次输入的上下文长度，精细度越高，上下文长度越小，每篇文档分割出的文本段落就越多。",children:t.jsx("img",{className:"frame-child179",src:z})})]}),t.jsx(n.Item,{name:"splitLevel",noStyle:!0,children:t.jsx(u,{defaultValue:je,className:"create-task-slider",dots:!0,tooltip:{formatter:e=>C[(e||0)/25]},step:25,marks:be,value:je,onChange:e=>{fe(e)},railStyle:{height:"6px",background:"#F1F6F9",borderTop:"1px solid #E1EAEF",borderBottom:"1px solid #E1EAEF"},trackStyle:{height:"6px",background:"#0FB698",borderTop:"1px solid #0CA287",borderBottom:"1px solid #0CA287"},handleStyle:{},style:{width:"39.25rem",display:"inline-flex",margin:"unset"}})}),t.jsx("label",{className:"info-label",style:{display:"inline-block",marginLeft:"1rem"},children:t.jsx("div",{className:"slider-label reqularText",children:C[je/25]})})]}),t.jsxs("div",{className:"createTaskItem",style:{margin:"1rem 0"},children:[t.jsxs("div",{className:"customConfigLabel",children:[t.jsx("label",{className:"crateTaskDraggerLabel reqularText",children:"提问密度"}),t.jsx(h,{title:"该参数决定每段文本推理的问答对数量，密度越大，模型会尽量从每个段落中推理越多的问题和答案。",children:t.jsx("img",{className:"frame-child179",src:z})})]}),t.jsx(n.Item,{name:"questionDensity",noStyle:!0,children:t.jsx(u,{defaultValue:ve,className:"create-task-slider",dots:!0,tooltip:{formatter:e=>E[(e||0)/25]},step:25,marks:ke,value:ve,onChange:e=>{ye(e)},railStyle:{height:"6px",background:"#F1F6F9",borderTop:"1px solid #E1EAEF",borderBottom:"1px solid #E1EAEF"},trackStyle:{height:"6px",background:"#0FB698",borderTop:"1px solid #0CA287",borderBottom:"1px solid #0CA287"},handleStyle:{},style:{width:"39.25rem",display:"inline-flex",margin:"unset"}})}),t.jsx("label",{className:"info-label",style:{display:"inline-block",marginLeft:"1rem"},children:t.jsx("div",{className:"slider-label reqularText",children:E[ve/25]})})]}),t.jsx(x,{})]}):null,t.jsx(n.Item,{name:"domain",label:"描述你的需求",children:t.jsx("div",{children:t.jsxs(p,{style:{display:"flex",margin:"0",padding:"0"},children:[t.jsx(g,{header:t.jsx("div",{children:"描述模板"}),className:"listStyle",bordered:!0,dataSource:de,renderItem:(e,s)=>t.jsxs(g.Item,{style:{backgroundColor:J===s?"rgb(234, 247, 245)":"initial"},onClick:()=>(e=>{X(e)})(s),children:[e.templateName,J===s&&t.jsx(a,{type:"link",size:"small",className:"show-btn",onClick:()=>((e,t)=>{ee(e);const s=e.roleBackground.replace(/^\s+|\s+$/g,""),a=e.detailedDescription.replace(/^\s+|\s+$/g,"");Q(`角色背景：\n${s}\n详情描述：\n${a}`)})(e),children:"应用"})]},s)}),t.jsxs("div",{className:"context",children:[t.jsxs("p",{children:["需求描述"," ",t.jsx(h,{title:D,children:t.jsx("img",{src:L,style:{width:"16px",height:"16px"}})})]}),t.jsx(G,{value:K,onChange:e=>{Q(e.target.value),X(-1)},placeholder:"请输入需求描述",autoSize:{minRows:10,maxRows:13},showCount:!0,maxLength:600,style:{width:"58rem",backgroundColor:"rgb(242, 246, 249)",fontSize:"16px"}})]})]})})}),t.jsxs("div",{className:"auto-label",children:[t.jsx("span",{style:{marginRight:"15px"},children:"自动标签"}),t.jsx(j,{size:"small",onChange:e=>{se(e),e||le("装备维修，运用参考，性能参考")}})]}),te&&t.jsx("div",{style:{marginTop:"16px"},children:t.jsxs("div",{style:{position:"relative"},children:[t.jsx(G,{value:ae,onChange:e=>(e=>{let t=e.target.value;if(t=t.replace(/\s*，\s*/g,"，"),t=t.replace(/，{2,}/g,"，"),t.trimStart().startsWith("，")){const e=t.indexOf("，");-1!==e&&(t=t.substring(0,e)+t.substring(e+1))}le(t.trim())})(e),autoSize:{minRows:2,maxRows:6},onPressEnter:e=>e.preventDefault(),placeholder:"请输入分类标签，以逗号分隔。示例:飞机，电磁，坦克..……，请勿使用特殊符号",className:"label-textarea"}),t.jsxs("div",{className:"label-textarea-footer",children:[t.jsx("div",{style:{marginTop:"8px",fontSize:"14px",color:"#666",lineHeight:"1.4"},children:"请输入分类标签，以逗号分隔。示例:飞机，电磁，坦克..……，请勿使用特殊符号"}),t.jsx(a,{type:"primary",loading:ie,onClick:async()=>{var e,t;if(0===P.length)return void f.warning("请先选择源数据集");if((ae?ae.split("，").length:0)>=20)f.warning("生成的标签数量不能超过20个");else{re(!0);try{const s=P.map((e=>e.dataSetId)),a=await I(s);if(200===(null==(e=a.data)?void 0:e.code)){const e=a.data.data;oe(e);const t=e.filter((e=>e));if(0===t.length)f.warning("暂无推荐标签数据");else{const e=ae?ae.split("，"):[],s=new Set;e.forEach((e=>s.add(e))),t.forEach((e=>s.add(e)));let a=Array.from(s);if(a.length<20&&t.length>0){const e=20-a.length,l=[...t.filter((e=>!s.has(e)))].sort((()=>.5-Math.random())),i=null==l?void 0:l.slice(0,Math.min(e,l.length));a=[...a,...i]}const l=(null==a?void 0:a.slice(0,20)).join("，");le(l),f.success("标签生成成功！")}}else f.error((null==(t=a.data)?void 0:t.message)||"标签生成失败，请重试")}catch(s){f.error("标签生成失败，请重试")}finally{re(!1)}}},className:"createAiBtn",children:"AI智能生成"})]})]})}),t.jsx("div",{style:{textAlign:"center",marginTop:"90px"},children:t.jsx(a,{shape:"round",size:"large",htmlType:"submit",className:"createBtn",disabled:!b||0===(null==b?void 0:b.length)||0===P.length||(null==P?void 0:P.some((e=>"success"!==e.status))),children:"开始任务"})})]}),t.jsx(A,{ref:xe,visible:V,OnClose:e=>{const t=P;e&&(e.forEach((e=>{if(!t.some((t=>t.dataSetId===e.id))){const s=new Object;s.dataSetId=e.id,s.name=e.datasetName,s.tags=e.tags,s.parseProcess=e.progress,s.dataSource=0,e.datasetStatus===N.sucess?s.status="success":e.datasetStatus===N.failed&&(s.status="error"),t.push(s)}})),H(t)),_(!1)}}),t.jsx(S,{visible:W,OnClose:e=>{Z(!1)},rowData:void 0})]})]})})};export{B as default};

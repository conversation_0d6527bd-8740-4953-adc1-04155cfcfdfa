import { Button, Form } from 'antd';
import classes from './index.module.css';
import { useState } from 'react';
import SelectDatasetModal from '../../../../../components/SelectDatasetModal';
import { DatasetFile } from '../../../../task/CreateTaskView';
import { DataSetStatus, TaskType } from '../../../../../types';

interface CreateTaskFormType {
    taskName: string;
    fileList: string[];
    splitLevel: number; //对应prd中的6个句子分组
    questionDensity: number; //对应prd中的11~20个问题/组
    description: string;
    domain: string;
};

const DatasetSelect: React.FC = () => {
    const [showDataModal, setShowDataModal] = useState<boolean>(false);
    const [fileList, setFileList] = useState<TaskType[]>([]);
    return <>
        <div className={classes['training-content']}>
            <div className={classes['step2']} style={{ width: "15%" }}>Step 1</div>
            <div className={classes['operatearea']}>
                <div className={classes['modleselect']}>
                    <Form.Item<CreateTaskFormType> name="fileList" label="数据集选择:">
                        <Button
                            className="createTaskSelectBtn reqularText"
                            onClick={() => {
                                setShowDataModal(true);
                            }}
                        >
                            在平台库中选择
                        </Button>
                    </Form.Item>
                </div>
                <div className={classes['modelName']}>
                    测试及比例：

                </div>
                <SelectDatasetModal
                    visible={showDataModal}
                    OnClose={(rows) => {
                        console.log(rows);
                        const files = fileList;
                        if (rows) {
                            rows.forEach(row => {
                                const data: any = new Object();
                                data.dataSetId = row.id;
                                data.name = row.name;
                                data.tags = row.tags;
                                data.parseProcess = row.progress;
                                data.dataSource = 0;
                                if (row.datasetStatus === DataSetStatus.sucess) {
                                    data.status = "success";
                                } else if (row.datasetStatus === DataSetStatus.failed) {
                                    data.status = "error";
                                }
                                files.push(data);
                            });
                            setFileList(files);
                        }
                        setShowDataModal(false);
                    }}
                ></SelectDatasetModal>
            </div>
        </div>
    </>;
}
export default DatasetSelect;
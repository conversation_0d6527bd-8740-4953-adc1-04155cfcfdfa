{"name": "pollux-frontend", "version": "1.0.1", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "@antv/g2": "^5.2.7", "@cyntler/react-doc-viewer": "^1.13.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@emotion/css": "^11.11.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.12.4", "axios": "^1.4.0", "dotenv": "^16.3.1", "echarts": "^5.5.1", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspreadsheet-ce": "^4.13.4", "jszip": "^3.10.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "os-browserify": "^0.3.0", "rc-virtual-list": "^3.11.3", "react": "^18.2.0", "react-custom-scrollbars": "^4.2.1", "react-dom": "^18.2.0", "react-file-viewer": "^1.2.1", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-scrollbars-custom": "^4.1.1", "react-wordcloud": "^1.2.7", "redux": "^4.2.1", "redux-logger": "^3.0.6", "redux-thunk": "^2.4.2", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "serve": "vite preview", "test": "vitest", "coverage": "vitest run --coverage"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/jest": "^27.5.2", "@types/js-cookie": "^3.0.6", "@types/node": "^22.10.5", "@types/nprogress": "^0.2.0", "@types/react": "^18.2.21", "@types/react-custom-scrollbars": "^4.0.12", "@types/react-dom": "^18.2.7", "@types/redux-logger": "^3.0.9", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^1.0.0", "dotenv-cli": "^7.3.0", "typescript": "^4.9.5", "vite": "^5.0.0", "vite-plugin-env-compatible": "^2.0.1", "vitest": "^1.0.0"}}
import React, { useEffect, useRef, useState } from 'react';
import { Button, Form, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { Scrollbar } from 'react-scrollbars-custom';
import UploadErrorModal from '@/components/UploadErrorModal';
import SelectDatasetModal from '@/components/SelectDatasetModal';
import { CreateTaskFieldType } from '@/types';
import { createTask, getAllTemplates, getLabelList } from '@/api/task';
import {
  CreateTaskFormType,
  TemplateType,
  DatasetFile,
  ModelConfigType,
  CreateTaskParams,
  TaskConfigMap,
} from './types';
import {
  DEFAULT_VALUES,
  TASK_CONFIG_MAPPING,
  VALIDATION_RULES,
  MESSAGES,
  CSS_CLASSES,
} from './constants';
import FormHeader from './components/FormHeader';
import FileUpload from './components/FileUpload';
import ModelConfig from './components/ModelConfig';
import TemplateSelection from './components/TemplateSelection';
import LabelManagement from './components/LabelManagement';

const CreateTaskView: React.FC = () => {
  const navigate = useNavigate();

  // 状态管理
  const [taskName, setTaskName] = useState<string>(DEFAULT_VALUES.TASK_NAME);
  const [modelConfig, setModelConfig] = useState<ModelConfigType>(DEFAULT_VALUES.MODEL_CONFIG);
  const [fileList, setFileList] = useState<DatasetFile[]>([]);
  const [showDataModal, setShowDataModal] = useState<boolean>(false);
  const [uploadErrorModal, setUploadErrorModal] = useState<boolean>(false);
  const [textValue, setTextValue] = useState<string>('');
  const [selectedIndex, setSelectedIndex] = useState<number>(DEFAULT_VALUES.SELECTED_INDEX);
  const [textValueTemplate, setTextValueTemplate] = useState<TemplateType>({
    id: 0,
    templateName: '',
    roleBackground: '',
    detailedDescription: '',
  });
  const [autoLabel, setAutoLabel] = useState<boolean>(DEFAULT_VALUES.AUTO_LABEL);
  const [labelText, setLabelText] = useState<string>(DEFAULT_VALUES.LABEL_TEXT);
  const [isGeneratingLabels, setIsGeneratingLabels] = useState<boolean>(false);
  const [exampleText, setExampleText] = useState<TemplateType[]>([]);
  const [splitLevel, setSplitLevel] = useState<number>(DEFAULT_VALUES.SPLIT_LEVEL);
  const [questionDensityValue, setQuestionDensity] = useState<number>(
    DEFAULT_VALUES.QUESTION_DENSITY
  );

  // Refs
  const datasetModalRef = useRef<any>(null);
  const [form] = Form.useForm();

  // 获取模板数据
  useEffect(() => {
    getAllTemplates()
      .then((response) => {
        if (response.data.code === 200) {
          const res = response.data;
          setExampleText(res.data);
        }
      })
      .catch((error) => {
        console.log('获取模板数据失败', error);
      });
  }, []);

  // AI智能生成标签函数
  const handleAIGenerate = async () => {
    if (fileList.length === 0) {
      message.warning(MESSAGES.WARNING.SELECT_DATASET);
      return;
    }
    const currentLabelCount = labelText ? labelText.split('，').length : 0;
    if (currentLabelCount >= DEFAULT_VALUES.MAX_LABEL_COUNT) {
      message.warning(MESSAGES.WARNING.MAX_LABELS);
      return;
    }

    setIsGeneratingLabels(true);

    try {
      const datasetIds = fileList.map((file) => file.dataSetId);
      const res = await getLabelList(datasetIds);

      if (res.data?.code === 200) {
        const labels = res.data.data;
        const uniqueLabels = labels.filter((tag: any) => tag);

        if (uniqueLabels.length === 0) {
          message.warning(MESSAGES.WARNING.NO_RECOMMENDED_LABELS);
        } else {
          const currentLabels = labelText ? labelText.split('，') : [];
          const mergedSet = new Set<string>();
          currentLabels.forEach((label) => mergedSet.add(label));
          uniqueLabels.forEach((label: string) => mergedSet.add(label));

          let mergedLabels = Array.from(mergedSet);
          if (mergedLabels.length < DEFAULT_VALUES.MAX_LABEL_COUNT && uniqueLabels.length > 0) {
            const neededLabels = DEFAULT_VALUES.MAX_LABEL_COUNT - mergedLabels.length;
            const availableLabels = uniqueLabels.filter((label: string) => !mergedSet.has(label));
            const shuffled = [...availableLabels].sort(() => 0.5 - Math.random());
            const additionalLabels = shuffled?.slice(0, Math.min(neededLabels, shuffled.length));
            mergedLabels = [...mergedLabels, ...additionalLabels];
          }
          const finalLabels = mergedLabels?.slice(0, DEFAULT_VALUES.MAX_LABEL_COUNT);
          const newLabelText = finalLabels.join('，');
          setLabelText(newLabelText);
          message.success(MESSAGES.SUCCESS.LABEL_GENERATED);
        }
      } else {
        message.error(res.data?.message || MESSAGES.ERROR.LABEL_GENERATION_FAILED);
      }
    } catch (error) {
      message.error(MESSAGES.ERROR.LABEL_GENERATION_FAILED);
      console.error('生成标签失败:', error);
    } finally {
      setIsGeneratingLabels(false);
    }
  };

  // 事件处理函数
  const handleTemplateSelect = (index: number) => {
    setSelectedIndex(index);
  };

  const handleTemplateApply = (item: TemplateType) => {
    setTextValueTemplate(item);
  };

  const handleTextChange = (text: string) => {
    setTextValue(text);
  };

  const handleShowDataModal = () => {
    setShowDataModal(true);
  };

  const handleAddTag = () => {
    // 标签添加逻辑已移至FileUpload组件
  };

  const handleDelFile = () => {
    // 文件删除逻辑已移至FileUpload组件
  };

  const handleReUpload = () => {
    // 重新上传逻辑已移至FileUpload组件
  };

  // 表单验证和提交
  const onFinish = (values: CreateTaskFormType) => {
    if (!VALIDATION_RULES.LABEL.PATTERN.test(labelText)) {
      message.error(MESSAGES.ERROR.LABEL_FORMAT_ERROR);
      return;
    }

    if (labelText) values.description = textValue;
    values.domain = textValueTemplate.templateName;

    const taskConfigMap: TaskConfigMap = {
      splitLevel: TASK_CONFIG_MAPPING.DEFAULT_SPLIT_LEVEL,
      questionDensity: TASK_CONFIG_MAPPING.DEFAULT_QUESTION_DENSITY,
    };

    if (modelConfig === '手动配置') {
      taskConfigMap.splitLevel = splitLevel
        ? splitLevel / TASK_CONFIG_MAPPING.STEP_SIZE + TASK_CONFIG_MAPPING.OFFSET
        : TASK_CONFIG_MAPPING.DEFAULT_SPLIT_LEVEL;
      taskConfigMap.questionDensity = questionDensityValue
        ? questionDensityValue / TASK_CONFIG_MAPPING.STEP_SIZE + TASK_CONFIG_MAPPING.OFFSET
        : TASK_CONFIG_MAPPING.DEFAULT_QUESTION_DENSITY;
    }

    const params: CreateTaskParams = {
      taskName: values.taskName,
      datasetList: fileList.map((file) => file.dataSetId!),
      taskConfigMap,
      description: values.description,
      domain: values.domain,
      tags: autoLabel ? labelText.split('，').filter((item) => item.trim()) : [],
    };

    createTask(params as CreateTaskFieldType).then((res) => {
      if (res?.data?.code === 200) {
        navigate('/main/task');
      } else {
        message.error(res?.data?.message || MESSAGES.ERROR.TASK_CREATION_FAILED);
      }
    });
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <Scrollbar>
      <div className={CSS_CLASSES.CREATE_TASK_CONTENT}>
        <div className={CSS_CLASSES.CREATE_TASK_AREA} style={{ marginBottom: '47px' }}>
          <Form
            form={form}
            className={CSS_CLASSES.REGULAR_TEXT}
            name="createTaskForm"
            layout="vertical"
            initialValues={{}}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
          >
            <FormHeader
              taskName={taskName}
              onTaskNameChange={setTaskName}
              onBack={() => navigate(-1)}
            />

            <FileUpload
              fileList={fileList}
              onFileListChange={setFileList}
              onShowDataModal={handleShowDataModal}
              onAddTag={handleAddTag}
              onDelFile={handleDelFile}
              onReUpload={handleReUpload}
              datasetModalRef={datasetModalRef}
            />

            <ModelConfig
              modelConfig={modelConfig}
              splitLevel={splitLevel}
              questionDensity={questionDensityValue}
              onModelConfigChange={setModelConfig}
              onSplitLevelChange={setSplitLevel}
              onQuestionDensityChange={setQuestionDensity}
            />

            <TemplateSelection
              templates={exampleText}
              selectedIndex={selectedIndex}
              textValue={textValue}
              onTemplateSelect={handleTemplateSelect}
              onTemplateApply={handleTemplateApply}
              onTextChange={handleTextChange}
            />

            <LabelManagement
              autoLabel={autoLabel}
              labelText={labelText}
              isGeneratingLabels={isGeneratingLabels}
              fileList={fileList}
              onAutoLabelChange={setAutoLabel}
              onLabelTextChange={setLabelText}
              onAIGenerate={handleAIGenerate}
            />

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                className={CSS_CLASSES.CREATE_TASK_SELECT_BTN}
                style={{ marginTop: '2rem' }}
              >
                创建任务
              </Button>
            </Form.Item>
          </Form>
        </div>

        <SelectDatasetModal
          ref={datasetModalRef}
          visible={showDataModal}
          OnClose={(rows) => {
            if (rows) {
              const newFiles: DatasetFile[] = rows.map((row) => ({
                uid: row.id,
                dataSetId: row.id,
                name: row.name,
                tags: row.tags,
                parseProcess: row.progress,
                dataSource: 0,
                status: 'done' as const,
              }));
              setFileList([...fileList, ...newFiles]);
            }
            setShowDataModal(false);
          }}
        />

        <UploadErrorModal
          visible={uploadErrorModal}
          OnClose={() => setUploadErrorModal(false)}
          rowData={undefined}
        />
      </div>
    </Scrollbar>
  );
};

export default CreateTaskView;

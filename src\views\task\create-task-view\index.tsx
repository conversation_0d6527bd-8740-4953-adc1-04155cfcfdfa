import {
    Button,
    Input,
    Segmented,
    Select,
    Upload,
    UploadProps,
    UploadFile,
    Space,
    Form,
    Divider,
    Slider,
    Tooltip,
    message,
    List,
    Typography,
    Card,
    Switch,
  } from "antd";
  import { ChangeEvent, useEffect, useRef, useState } from "react";
  import { useLocation, useNavigate } from "react-router-dom";
  import UploadList from "../../../components/UploadList";
  import UploadErrorModal from "../../../components/UploadErrorModal";
  import { LeftOutlined } from "@ant-design/icons";
  import SelectDatasetModal from "../../../components/SelectDatasetModal";
  import { Scrollbar } from "react-scrollbars-custom";
  import { SliderMarks } from "antd/es/slider";
  import { uploadDataset } from "../../../api/dataset";
  import { CreateTaskFieldType, DataSetStatus, DataSetType } from "../../../types";
  import { createTask,getAllTemplates } from "../../../api/task";
  import { paragraph, questionDensity } from "../../../utils/conts";
  import group151 from "../../../assets/img/group-151.svg";
  import infoIcon from "../../../assets/img/info-icon.svg";
  import { EXAMPLE_MESSAGE } from "./constants";
  const TOOLTIP = ["需求描述内的文本均可更改，绿色文本为自定义内容"];

  const { Dragger } = Upload;

  interface CreateTaskFormType {
    taskName: string;
    fileList: string[];
    splitLevel: number; //对应prd中的6个句子分组
    questionDensity: number; //对应prd中的11~20个问题/组
    description: string;
    domain: string;
  }
  interface TemplateType {
    id: number;
    templateName: string;
    roleBackground: string;
    detailedDescription: string;
  }
  export interface DatasetFile extends UploadFile {
    parseState?: string; //解析状态
    parseProcess?: number; //解析进度
    tags?: string[];
    name: string;
    dataSetId: string;
    dataSource?: number; //0-平台库/1-用户上传
  }

  const CreateTaskView: React.FC = () => {
    const { state } = useLocation();
    const navigate = useNavigate();
    // 定义用户名和密码
    const [taskName, setTaskName] = useState("");
    const [modelConfig, setModelConfig] = useState<string | number>("自动配置");
    const [requiredArea, setRequiredArea] = useState<string>("军工领域");
    const [requiredText, setRequiredText] = useState<string>("");
    const [fileList, setFileList] = useState<DatasetFile[]>([]);
    const [showDataModal, setShowDataModal] = useState<boolean>(false);
    const [uploadErrorModal, setUploadErrorModal] = useState<boolean>(false);
    const { TextArea } = Input;
    const [textValue, setTextValue] = useState('');
    const [selectedIndex, setSelectedIndex] = useState(-1);
    const [textValueTemplate, setTextValueTemplate] = useState({
      id: 0,
      templateName: "",
      roleBackground: "",
      detailedDescription: "",
    });
    const [autoLabel, setAutoLabel] = useState(false);
    const [labelText, setLabelText] = useState('装备维修，运用参考，性能参考'); // 默认标签
    const [isGeneratingLabels, setIsGeneratingLabels] = useState(false); // AI生成状态


    const [exampleText, setExampleText] = useState<TemplateType[]>([]);
    const [currentExample, setCurrentExample] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);
    const [scrolling, setScrolling] = useState(true);

    const [splitLevel, setSplitLevel] = useState(50);
    const [questionDensityValue, setQuestionDensity] = useState(50);
    const [form] = Form.useForm();
    const paragraphMarks: SliderMarks = {
      0: (
        <Tooltip title={paragraph[0]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      ),
      25: (
        <Tooltip title={paragraph[1]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      ),
      50: (
        <Tooltip title={paragraph[2]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      ),
      75: (
        <Tooltip title={paragraph[3]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      ),
      100: (
        <Tooltip title={paragraph[4]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      ),
    };
    const questionDensityMarks: SliderMarks = {
      0: (
        <Tooltip title={questionDensity[0]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      ),
      25: (
        <Tooltip title={questionDensity[1]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      ),
      50: (
        <Tooltip title={questionDensity[2]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      ),
      75: (
        <Tooltip title={questionDensity[3]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      ),
      100: (
        <Tooltip title={questionDensity[4]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      ),
    };
    useEffect(() => {
      if (state && state.selectedRows) {
        const { selectedRows } = state;
        const updateFiles = fileList;
        selectedRows.forEach((row: DataSetType) => {
          const data: any = new Object();
          data.dataSetId = row.id;
          data.name = row.datasetName;
          data.tags = row.tags;
          data.parseProcess = row.progress;
          data.dataSource = 0;
          if (row.datasetStatus === DataSetStatus.sucess) {
            data.status = "success";
          } else if (row.datasetStatus === DataSetStatus.failed) {
            data.status = "error";
          }
          updateFiles.push(data);
        });
        setFileList(updateFiles);
      }
    }, []);

    useEffect(() => {
      const container = containerRef.current;

      const handleMouseEnter = () => {
        setScrolling(false);
      };

      const handleMouseLeave = () => {
        setScrolling(true);
      };

      if (container) {
        container.addEventListener("mouseenter", handleMouseEnter);
        container.addEventListener("mouseleave", handleMouseLeave);
      }

      return () => {
        if (container) {
          container.removeEventListener("mouseenter", handleMouseEnter);
          container.removeEventListener("mouseleave", handleMouseLeave);
        }
      };
    }, []);

    useEffect(() => {
      let interval: NodeJS.Timeout;

      if (scrolling) {
        interval = setInterval(() => {
          setCurrentExample((prev) => (prev + 1) % EXAMPLE_MESSAGE.length);
        }, 2000);
        return () => clearInterval(interval);
      }

      return () => clearInterval(interval);
    }, [scrolling]);
    // 获取模板数据

      useEffect(() => {
        getAllTemplates()
        .then((response) => {
              // 将获取到的数据存储到 exampleText 状态中
              //
              if(response.data.code===200){
                const res = response.data
                setExampleText(res.data);
                // console.log('exampleText',response.data.data)
              }

          })
        .catch((error) => {
              console.log('获取模板数据失败', error);
          });

      },[]);

    const props: UploadProps = {
      name: "file",
      multiple: true,
      action: "/api/qa_generator/upload_data",
      accept: ".txt,.docx,.doc,.pdf,.zip,.rar,.csv",
      showUploadList: false,
      fileList: fileList,
      onDrop(e) {
        console.log("Dropped files", e.dataTransfer.files);
      },
      customRequest: (options) => {
        console.log({options});

        uploadDataset({ importDataset: options.file, tags: [] })
          .then((res: any) => {
            if (res.data?.code === 200) {
              if (options.onSuccess) {
                options.onSuccess(res, options.file as any);
              }
            } else {
              if (options.onError) {
                options.onError(res.data);
              }
            }
          })
          .catch(options.onError);
      },
      onChange(info) {
        let newFileList = [...info.fileList];
        console.log({newFileList});


        // if (info.file.status !== 'uploading') {
        //   console.log(info.file, info.fileList);
        // }
        // if (info.file.status === 'done') {
        //   message.success(`${info.file.name} 上传成功`);
        // } else if (info.file.status === 'error') {
        //   message.error(`${info.file.name} 上传失败`);
        // }
        setFileList(newFileList as any);
      },
      // beforeUpload(file, FileList) {
      //   let status = "uploading";
      //   const importFile: any = file;
      //   importFile.status = status;
      //   uploadDataset({ importDataset: file, tags: [] }).then((res) => {
      //     setFileList((prevFileList) => {
      //       const updatedFileList = [...prevFileList];
      //       const index = updatedFileList.findIndex((_file) => _file.uid === file.uid);

      //       if (res.data?.code === 200) {
      //         if (index !== -1) {
      //           updatedFileList[index].status = "done";
      //           updatedFileList[index].dataSetId = res.data.data;
      //         } else {
      //           importFile.status = "done";
      //           importFile.dataSetId = res.data.data;
      //           updatedFileList.push(importFile);
      //         }
      //       } else {
      //         if (index !== -1) {
      //           updatedFileList[index].status = "error";
      //         } else {
      //           importFile.status = "error";
      //           updatedFileList.push(importFile);
      //         }
      //       }
      //       return updatedFileList;
      //     });
      //   });
      //   return false;
      // },
    };
    const autoChange=(checked: boolean) => {
      setAutoLabel(checked);
      console.log(`switch to ${checked}`);
    }

    // AI智能生成标签函数
    const handleAIGenerate = async () => {
      setIsGeneratingLabels(true);
      try {
        // 模拟AI生成标签的API调用
        // 这里您需要替换为实际的API调用
        await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟API延迟

        // 根据需求描述内容生成相关标签
        const contextBasedLabels = [];
        if (textValue.includes('军工') || textValue.includes('装备')) {
          contextBasedLabels.push('军工装备', '武器系统', '防务技术');
        }
        if (textValue.includes('维修') || textValue.includes('保养')) {
          contextBasedLabels.push('设备维护', '故障诊断', '预防性维修');
        }
        if (textValue.includes('性能') || textValue.includes('测试')) {
          contextBasedLabels.push('性能测试', '质量评估', '技术指标');
        }

        // 如果没有匹配的上下文，使用通用标签
        const defaultAILabels = ['智能识别', '数据分析', '自动分类', '机器学习'];
        const aiGeneratedLabels = contextBasedLabels.length > 0 ? contextBasedLabels : defaultAILabels;

        // 将AI生成的标签添加到现有标签后面
        const currentLabels = labelText.split('，').filter(label => label.trim() !== '');
        const newLabels = [...currentLabels, ...aiGeneratedLabels];
        const uniqueLabels = Array.from(new Set(newLabels)); // 去重

        setLabelText(uniqueLabels.join('，'));
        message.success('AI标签生成成功！');
      } catch (error) {
        message.error('AI标签生成失败，请重试');
        console.error('AI生成标签失败:', error);
      } finally {
        setIsGeneratingLabels(false);
      }
    };
    const handleLabelChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
      let inputValue = e.target.value;
      inputValue = inputValue.replace(/\s*，\s*/g, '，');
      inputValue = inputValue.replace(/，{2,}/g, '，');
      if (inputValue.trimStart().startsWith('，')) {
        const firstCommaIndex = inputValue.indexOf('，');
        if (firstCommaIndex !== -1) {
          inputValue = inputValue.substring(0, firstCommaIndex) + inputValue.substring(firstCommaIndex + 1);
        }
      }
      setLabelText(inputValue.trim());
    };
    // 验证成功
    const onFinish = (values: CreateTaskFormType) => {

      values.description = textValue
      values.domain = textValueTemplate.templateName
      // navigate("/main/createtask");
      const taskConfigMap = {
        splitLevel: 3,
        questionDensity: 3,
      };
      if (modelConfig === "手动配置") {
        taskConfigMap.splitLevel = splitLevel ? splitLevel / 25 + 1 : 3;
        taskConfigMap.questionDensity = questionDensityValue
          ? questionDensityValue / 25 + 1
          : 3;
      }
      const params: CreateTaskFieldType = {
        taskName: values.taskName,
        datasetList: fileList.map((file) => file.dataSetId!),
        // datasetList:fileList[0].originFileObj as any,
        taskConfigMap,
        description: values.description,
        domain: values.domain,
      };
      createTask(params).then((res) => {
        if (res?.data?.code === 200) {
          navigate("/main/task");
        } else {
          message.error(res?.data?.message);
        }
      });
    };
    // 验证失败
    const onFinishFailed = (errorInfo: any) => {
      console.log("Failed:", errorInfo);
    };
    const handleApplyClick = (item: any, index: number) => {
      // const currentText = textValue.trim();
  
      setTextValueTemplate(item);
  
      // 去除roleBackground开头和结尾的换行符
      const processedRoleBackground = item.roleBackground.replace(/^\s+|\s+$/g, '');
      // 去除detailedDescription开头和结尾的换行符
      const processedDetailedDescription = item.detailedDescription.replace(/^\s+|\s+$/g, '');
  
      const newText = `角色背景：\n${processedRoleBackground}\n详情描述：\n${processedDetailedDescription}`;
  
      setTextValue(newText);
  };
    // 处理点击行的函数
    const handleItemClick = (index:number) => {
      setSelectedIndex(index);
  };
    return (
      <Scrollbar>
        <div className="createTaskContent">
          <Space
            size={20}
            style={{ display: "inline-flex", alignItems: "center" }}
          >
            <Button
              style={{ fontSize: "12px", width: "36px", height: "36px" }}
              shape="circle"
              icon={<LeftOutlined />}
              onClick={() => navigate(-1)}
            />
            <div
              className="mediumText"
              style={{ fontSize: "28px", lineHeight: "36px", fontWeight: "500" }}
            >
              {/* 新建问答推理任务 */}
              生成训练数据
            </div>
          </Space>
          <div className="createTaskArea" style={{ marginBottom: "47px" }}>
            <Form
              form={form}
              className="reqularText"
              name="createTaskForm"
              layout="vertical"
              initialValues={{}}
              onFinish={onFinish}
              onFinishFailed={onFinishFailed}
              autoComplete="off"
            >
              <Form.Item<CreateTaskFormType>
                name="taskName"
                label="任务名称"
                rules={[
                  { required: true, message: "请输入任务名称" },
                  {
                    pattern: /^[\u4e00-\u9fa5a-zA-Z,——()“”_]+$/,
                    message: "任务名称不符合要求！",
                  },
                ]}
              >
                <Input
                  placeholder="请输入任务名称"
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                  style={{ width: "30rem", height: "2.5rem" }}
                />
              </Form.Item>
              <Form.Item<CreateTaskFormType> name="fileList" label="源数据集">
                <Button
                  className="createTaskSelectBtn reqularText"
                  onClick={() => {
                    setShowDataModal(true);
                  }}
                >
                  在平台库中选择
                </Button>
                {/* <Dragger {...props} className="createTaskDragger">
                  <div className="createTaskDraggerInner">
                    <img
                      className="createTaskDraggerIcon"
                      alt=""
                      src={uploadcomputer}
                    />
                    <p className="reqularText">
                      拖入您需要解析的本地数据集文档，或点击进行选择
                    </p>
                  </div>
                  <div className="createTaskDraggerInfo">
                    <label className="crateTaskDraggerLabel reqularText">
                      请将文档打包为zip格式进行上传
                    </label>
                    <br />
                    <label className="crateTaskDraggerLabel reqularText">
                      支持解析的文档类型有txt、doc、docx、rtf、pdf、json、csv
                    </label>
                  </div>
                </Dragger> */}
                <div>
                  <UploadList
                    fileList={fileList}
                    onAddTag={(dataSetId: string, tags: string[]) => {
                      const updateList = fileList;
                      updateList.forEach((file) => {
                        if (file.dataSetId === dataSetId) {
                          file.tags = tags;
                        }
                      });
                      setFileList(updateList);
                    }}
                    onDelFile={(index) => {
                      // const list = fileList;
                      // list.splice(index, 1);
                      const updatedDataList = fileList.filter(
                        (item, i) => i !== index
                      );
                      setFileList(updatedDataList);
                    }}
                    className="create-task-file-list"
                    onReUpload={(index: number) => {
                      const file = fileList[index];
                      let status = "uploading";
                      const importFile: any = file;
                      importFile.status = status;
                      uploadDataset({ importDataset: file, tags: [] }).then(
                        (res: any) => {
                          if (res.data?.code === 200) {
                            importFile.status = "success";
                            importFile.dataSetId = res.data.data;
                          } else {
                            importFile.status = "error";
                          }
                          fileList[index] = importFile;
                          setFileList(fileList);
                        }
                      );
                    }}
                  ></UploadList>
                </div>
              </Form.Item>
              <Form.Item<CreateTaskFormType> label="模型参数输入">
                <Segmented
                  className="createtask-segmented"
                  size="large"
                  options={[
                    {
                      label: (
                        <Tooltip title="自动配置">
                          <a
                            onClick={() => setModelConfig("自动配置")}
                            className={
                              modelConfig === "自动配置"
                                ? "model-config-active"
                                : "model-config"
                            }
                          >
                            自动配置
                          </a>
                        </Tooltip>
                      ),
                      value: "自动配置",
                    },
                    {
                      label: (
                        <Tooltip title="手动配置">
                          <a
                            onClick={() => setModelConfig("手动配置")}
                            className={
                              modelConfig === "手动配置"
                                ? "model-config-active"
                                : "model-config"
                            }
                          >
                            手动配置
                          </a>
                        </Tooltip>
                      ),
                      value: "手动配置",
                    },
                  ]}
                  value={modelConfig}
                  onChange={setModelConfig}
                />
              </Form.Item>
              {modelConfig === "手动配置" ? (
                <div>
                  <div className="createTaskItem">
                    <div className="customConfigLabel">
                      <label className="crateTaskDraggerLabel reqularText">
                        段落精细度
                      </label>
                      <Tooltip title="该参数决定推理过程中每次输入的上下文长度，精细度越高，上下文长度越小，每篇文档分割出的文本段落就越多。">
                        <img className="frame-child179" src={group151} />
                      </Tooltip>
                    </div>
                    <Form.Item<CreateTaskFormType> name="splitLevel" noStyle>
                      <Slider
                        defaultValue={splitLevel}
                        className="create-task-slider"
                        dots
                        tooltip={{
                          formatter: (val) => {
                            const q = val ? val : 0;
                            return paragraph[q / 25];
                          },
                        }}
                        step={25}
                        marks={paragraphMarks}
                        // defaultValue={0}
                        value={splitLevel}
                        onChange={(val) => {
                          console.log(val);
                          setSplitLevel(val);
                        }}
                        railStyle={{
                          height: "6px",
                          background: "#F1F6F9",
                          borderTop: "1px solid #E1EAEF",
                          borderBottom: "1px solid #E1EAEF",
                        }}
                        trackStyle={{
                          height: "6px",
                          background: "#0FB698",
                          borderTop: "1px solid #0CA287",
                          borderBottom: "1px solid #0CA287",
                        }}
                        handleStyle={{}}
                        style={{
                          width: "39.25rem",
                          display: "inline-flex",
                          margin: "unset",
                        }}
                      />
                    </Form.Item>
                    <label
                      className="info-label"
                      style={{ display: "inline-block", marginLeft: "1rem" }}
                    >
                      <div className="slider-label reqularText">
                        {paragraph[splitLevel / 25]}
                      </div>
                    </label>
                  </div>
                  <div className="createTaskItem" style={{ margin: "1rem 0" }}>
                    <div className="customConfigLabel">
                      <label className="crateTaskDraggerLabel reqularText">
                        提问密度
                      </label>
                      <Tooltip title="该参数决定每段文本推理的问答对数量，密度越大，模型会尽量从每个段落中推理越多的问题和答案。">
                        <img className="frame-child179" src={group151} />
                      </Tooltip>
                    </div>
                    <Form.Item<CreateTaskFormType> name="questionDensity" noStyle>
                      <Slider
                        defaultValue={questionDensityValue}
                        className="create-task-slider"
                        dots
                        tooltip={{
                          formatter: (val) => {
                            const q = val ? val : 0;
                            return questionDensity[q / 25];
                          },
                        }}
                        step={25}
                        marks={questionDensityMarks}
                        value={questionDensityValue}
                        onChange={(val) => {
                          console.log(val);
                          setQuestionDensity(val);
                        }}
                        railStyle={{
                          height: "6px",
                          background: "#F1F6F9",
                          borderTop: "1px solid #E1EAEF",
                          borderBottom: "1px solid #E1EAEF",
                        }}
                        trackStyle={{
                          height: "6px",
                          background: "#0FB698",
                          borderTop: "1px solid #0CA287",
                          borderBottom: "1px solid #0CA287",
                        }}
                        handleStyle={{}}
                        style={{
                          width: "39.25rem",
                          display: "inline-flex",
                          margin: "unset",
                        }}
                      />
                    </Form.Item>
                    <label
                      className="info-label"
                      style={{ display: "inline-block", marginLeft: "1rem" }}
                    >
                      <div className="slider-label reqularText">
                        {questionDensity[questionDensityValue / 25]}
                      </div>
                    </label>
                  </div>
                  {/* <label className="reqularText">
                    预计生成xxxx对问答数据，预计耗时xxxxh
                  </label> */}
                  <Divider />
                </div>
              ) : null}
              <Form.Item<CreateTaskFormType> name="domain" label="描述你的需求">
                {/* <div>
                  <Space size={24}>
                    <label className="crateTaskDraggerLabel reqularText">
                      领域选择
                    </label>
                    <Form.Item<CreateTaskFormType>
                      name="domain"
                      initialValue="军工领域"
                      noStyle
                    >
                      <Select
                        style={{ width: "8.75rem" }}
                        defaultValue="军工领域"
                        value={requiredArea}
                        size="large"
                        onChange={setRequiredArea}
                        options={[
                          { value: "安全领域", label: "安全领域" },
                          { value: "医疗领域", label: "医疗领域" },
                          { value: "军工领域", label: "军工领域" },
                        ]}
                      />
                    </Form.Item>
                  </Space>
                </div>
                <div style={{ marginTop: "1rem" }}>
                  <div
                    style={{ display: "flex", alignItems: "center", gap: "24px" }}
                  >
                    <label className="crateTaskDraggerLabel reqularText">
                      需求描述
                    </label>
                    <div style={{ flex: 1 }}>
                      <Form.Item<CreateTaskFormType>
                        name="description"
                        noStyle
                      // rules={[{ required: true, message: '请输入需求描述' }]}
                      >
                        <Input
                          size="large"
                          value={requiredText}
                          onChange={(e) => setRequiredText(e.target.value)}
                        />
                      </Form.Item>
                    </div>
                  </div>
                </div> */}
                <div>
                  <Card style={{ display: "flex", margin: "0", padding: "0" }}>
                    <List
                      header={<div>描述模板</div>}
                      className="listStyle"
                      bordered
                      dataSource={exampleText}
                      renderItem={(item,index) => (
                        <List.Item key={index}
                        style={{
                          backgroundColor: selectedIndex === index? "rgb(234, 247, 245)" : "initial",
                        }}
                        onClick={() => handleItemClick(index)}
                        >

                          {item.templateName}
                          {/* <Button type="link" size="small" className="show-btn"
                           onClick={()=>handleApplyClick(item,index)}
                           >
                            应用
                          </Button> */}
                           {selectedIndex === index && (
                          <Button type="link" size="small" className="show-btn"
                              onClick={() => handleApplyClick(item, index)}
                          >
                              应用
                          </Button>
                      )}
                        </List.Item>
                      )}
                    />
                    <div className="context">
                      <p>
                        需求描述{" "}
                        <Tooltip title={TOOLTIP}>
                          <img
                            src={infoIcon}
                            style={{ width: "16px", height: "16px" }}
                          />
                        </Tooltip>
                      </p>
                      <TextArea
                        value={textValue}
                        onChange={(e) => {
                          setTextValue(e.target.value)
                          setSelectedIndex(-1);}}
                        placeholder="请输入需求描述"
                        autoSize={{ minRows: 10, maxRows: 13 }}
                        style={{ width: "58rem" ,backgroundColor:'rgb(242, 246, 249)',fontSize:'16px'}}
                      />
                    </div>
                  </Card>
                </div>
              </Form.Item>
              <div className="auto-label">
                <span style={{marginRight:'15px'}}>自动标签</span>
                <Switch size="small" onChange={autoChange} />
              </div>
              {autoLabel && (
                <div style={{ marginTop: '16px' }}>
                  <div style={{ position: 'relative' }}>
                    <TextArea
                      value={labelText}
                      onChange={handleLabelChange} 
                      autoSize={{ minRows: 2, maxRows: 6 }}
                      onPressEnter={(e) => e.preventDefault()} // 阻止回车换行
                      placeholder="请输入分类标签，以逗号分隔。示例：飞机，电磁，坦克,……"
                      className="label-textarea"
                    />
                    <div className="label-textarea-footer">
                      <div style={{
                        marginTop: '8px',
                        fontSize: '14px',
                        color: '#666',
                        lineHeight: '1.4'
                        }}>
                        请输入分类标签，以逗号分隔。示例：飞机，电磁，坦克,……
                      </div>
                      <Button
                        type="primary"
                        loading={isGeneratingLabels}
                        onClick={handleAIGenerate}
                        className="createAiBtn"
                      >
                        AI智能生成
                    </Button>
                    </div>
                  </div>
                  
                </div>
              )}
              <div style={{ textAlign: "center", marginTop: "90px" }}>
                <Button
                  shape="round"
                  size="large"
                  htmlType="submit"
                  className="createBtn"
                  disabled={
                    !taskName ||
                    taskName?.length === 0 ||
                    fileList.length === 0 ||
                    fileList?.some((file) => (file.status as any) !== "success")
                  }
                >
                  开始任务
                </Button>
              </div>

            </Form>

            <SelectDatasetModal
              visible={showDataModal}
              OnClose={(rows) => {
                console.log(rows);
                const files = fileList;
                if (rows) {
                  rows.forEach((row) => {
                    const data: any = new Object();
                    data.dataSetId = row.id;
                    data.name = row.datasetName;
                    data.tags = row.tags;
                    data.parseProcess = row.progress;
                    data.dataSource = 0;
                    if (row.datasetStatus === DataSetStatus.sucess) {
                      data.status = "success";
                    } else if (row.datasetStatus === DataSetStatus.failed) {
                      data.status = "error";
                    }
                    files.push(data);
                  });
                  setFileList(files);
                }
                setShowDataModal(false);
              }}
            ></SelectDatasetModal>

            <UploadErrorModal
              visible={uploadErrorModal}
              OnClose={(rows) => {
                console.log(rows);
                setUploadErrorModal(false);
              }}
              rowData={undefined}
            />
          </div>
        </div>
      </Scrollbar>
    );
  };
  export default CreateTaskView;

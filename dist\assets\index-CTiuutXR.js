import{s as e,k as s,p as t,r as a,j as r,S as l,f as i,B as n,l as d,v as o,o as c,w as m,x as u,t as h,n as x}from"./react-core-BrQk45h1.js";import{a as p,b as v,c as j,d as g,e as f}from"./modle-Dr2iwiy1.js";import{p as b}from"./conts-BxhewW4k.js";import{E as y}from"./charts-C9p0W68L.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const N=({statusData:e,text:s})=>r.jsx(u,{status:e,text:s}),w=()=>{const{task_id:u,name:w,status:S}=e(),z=s(),[C]=t.useForm(),[I,k]=a.useState([]),[A,F]=a.useState(),[_,E]=a.useState(),[L,T]=a.useState([]),[q,D]=a.useState(),[O,V]=a.useState(!1),[R,B]=a.useState(""),[H,W]=a.useState([]),[$,G]=a.useState(!0),[J,K]=a.useState({splitLevel:50}),M={0:r.jsxs(r.Fragment,{children:[r.jsx("div",{style:{color:"#8E98A7"}}),r.jsx(x,{title:b[0],children:r.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),25:r.jsxs(r.Fragment,{children:[r.jsx("div",{style:{color:"#8E98A7"}}),r.jsx(x,{title:b[1],children:r.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),50:r.jsxs(r.Fragment,{children:[r.jsx("div",{style:{color:"#8E98A7"}}),r.jsx(x,{title:b[2],children:r.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),75:r.jsxs(r.Fragment,{children:[r.jsx("div",{style:{color:"#8E98A7"}}),r.jsx(x,{title:b[3],children:r.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]}),100:r.jsxs(r.Fragment,{children:[r.jsx("div",{style:{color:"#8E98A7"}}),r.jsx(x,{title:b[4],children:r.jsx("div",{style:{width:10,height:10,marginTop:-10,zIndex:10}})})]})},P=e=>{f(e).then((e=>{var s,t;if((null==(s=e.data)?void 0:s.code)&&200===(null==(t=e.data)?void 0:t.code)){const s=e.data.data.gpu_usage,t={};Object.keys(s).forEach((e=>{const a=`A100_${("0"+(Number(e)+1)).slice(-2)}`;t[a]=s[e]}));const a=Object.entries(t).map((([e,s])=>({name:e,data:s})));W(a);const r=a.map(((e,t)=>({id:Object.keys(s)[t],label:e.name,value:e.name})));D(r)}}))},Q=()=>{var e,s;const t=u;if(7===Number(S)||6===Number(S))j(t).then((e=>{var s;200===(null==(s=e.data)?void 0:s.code)?(h.success("模型部署下线成功!"),z("/main/finetune")):h.error("模型部署下线失败")}));else{const a=Number(null==(e=null==H?void 0:H[0])?void 0:e.data.usage_rate),r=Number(null==(s=null==H?void 0:H[0])?void 0:s.data.estimated_increased_rate)+a;C.validateFields().then((()=>{if(r>1)h.error("服务器使用率大于100%,无法部署上线");else{const e={computeResource:Array.isArray(L)?L:[L],serverConfigId:R,deploymentFramework:I,performanceConfig:J.splitLevel,server:A};g(t,e).then((e=>{200===e.data.code&&j(t).then((e=>{var s;200===(null==(s=e.data)?void 0:s.code)?(h.success("配置成功,模型部署上线成功!"),z("/main/finetune")):h.error("模型部署上线失败")}))}))}})).catch((e=>{}))}};return a.useEffect((()=>{p(u).then((e=>{var s;if(200===(null==(s=e.data)?void 0:s.code)){const s=e.data.data;k(s.deploymentFramework),F(s.server),P(s.serverConfigId),T(s.computeResource),B(s.serverConfigId),V(!0),O&&(P(R),V(!0))}})),v().then((e=>{var s;if(200===(null==(s=e.data)?void 0:s.code)){const s=e.data.data.server_config;Array.isArray(s)?E(s.map((e=>({label:e.server_name,value:e.server_name,id:e.id})))):E([])}})),setInterval((()=>{G(!1)}),500)}),[]),r.jsx(l,{children:r.jsxs("div",{className:"configureView",children:[r.jsxs("div",{className:"configure-header",children:[r.jsxs(i,{size:20,style:{display:"inline-flex",alignItems:"center"},children:[r.jsx(n,{style:{fontSize:"12px",width:"36px",height:"36px"},shape:"circle",icon:r.jsx(d,{}),onClick:()=>z("/main/finetune")}),r.jsxs("div",{className:"mediumText",style:{fontSize:"28px",lineHeight:"36px",fontWeight:"500"},children:[w,"/配置"]}),r.jsx(N,{statusData:7===Number(S)||6===Number(S)?"success":"error",text:7===Number(S)||6===Number(S)?"在线":"离线"})]}),r.jsx(i,{children:7!==Number(S)?r.jsx(n,{className:"save-btn",size:"large",shape:"round",onClick:Q,children:"部署上线"}):r.jsx(n,{className:"save-btn",size:"large",shape:"round",onClick:Q,children:"部署下线"})})]}),r.jsxs("div",{className:"configure-info",children:[r.jsx("div",{className:"configure-title",children:"部署配置"}),r.jsxs(t,{form:C,initialValues:{framework:I,serverSelection:A,arithmeticConfiguration:L},labelCol:{span:6},wrapperCol:{span:18,offset:2},className:"configure-form",children:[r.jsx(t.Item,{label:"部署框架:",rules:[{required:!0,message:"请选择部署框架"}],style:{width:"511px",marginLeft:"28px"},children:r.jsx(o,{size:"large",disabled:7===Number(S)||6===Number(S),showSearch:!0,placeholder:"请选择部署的框架",value:I,onChange:e=>{k(e)},style:{flex:1},options:[{label:"vllm",value:"vllm"}]})}),r.jsxs(t.Item,{label:"性能配置：",style:{width:"620px"},rules:[{required:!0,message:"请选择性能配置"}],children:[r.jsx("span",{className:"span-text",children:"并发优先"}),r.jsx(c,{defaultValue:J.splitLevel,dots:!0,disabled:7===Number(S)||6===Number(S),tooltip:{formatter:e=>"优先度:"+(e||0)},step:25,marks:M,value:J.splitLevel,onChange:e=>{K((s=>({...s,splitLevel:e})))},style:{width:"67.8%",display:"inline-flex",margin:"unset"}}),r.jsx("span",{className:"span-text1",children:"速度优先"})]}),r.jsx(t.Item,{label:"服务器选择:",rules:[{required:!0,message:"请选择服务器"}],style:{width:"620px"},children:r.jsx(o,{size:"large",disabled:7===Number(S)||6===Number(S),showSearch:!0,placeholder:"请选择服务器",value:A,onChange:e=>{F(e),V(!0);const s=String(null==_?void 0:_.filter((s=>s.value===e))[0].id);B(s),P(s)},style:{flex:1},options:_})}),r.jsx(t.Item,{label:"算力配置:",rules:[{required:!0,message:"请选择算力配置"}],style:{width:"620px"},children:r.jsx(o,{size:"large",disabled:7===Number(S)||6===Number(S),showSearch:!0,placeholder:"请选择算力配置",value:L,onChange:e=>{T(e)},style:{flex:1},options:q})}),$?r.jsx(m,{spin:!0,style:{fontSize:"104px"}}):H.length>0&&r.jsx("div",{className:"configure-chart",children:r.jsx(y,{powerData:H,status:S})})]})]})]})})};export{w as default};

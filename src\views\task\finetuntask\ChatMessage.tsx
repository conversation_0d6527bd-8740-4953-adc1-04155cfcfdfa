import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import avatar from '../../../assets/img/avatar-1.svg'
import '../../../css/AdjustmentView.css'
interface ChatMessageProp {
    fromUser: boolean;
    text: string;
}

const ChatMessage = forwardRef((prop: ChatMessageProp, ref) => {

    const { fromUser, text } = prop;
    const [displayedText, setDisplayedText] = useState<string>('');
    const name = sessionStorage.getItem("name");

    useEffect(() => {
        setDisplayedText(text)
    }, [text])

    return <>

        <div className="chat-message-container">
            <img src={avatar} />
            <span style={{ marginLeft: "11px", fontSize: "16px",color: "#000000" }}>{name}</span>
        </div>
        <div className={fromUser ? 'chat-message-one' : 'chat-message-ai-one'} >
            {displayedText}
        </div>
    </>
})

export default ChatMessage;
import { Button, Empty, Modal, Space, Table, message } from "antd";
import { useRef, useState } from "react";
import { deleteDataset } from "../../api/dataset";
import "../../css/SourceDatasetView.css";
import { useNavigate } from "react-router-dom";
import DatasetTable from "../../components/DatasetTable";
import UploadErrorModal from "../../components/UploadErrorModal";
import { Scrollbar } from "react-scrollbars-custom";
import UploadDatasetModal from "../../components/UploadDatasetModal";
import {
  DataSetType,
  RelatedTaskType,
  TaskDeleteReviewType,
} from "../../types";
import bxcodeblock from "../../assets/img/bxcodeblock.svg";
import bxtrash from "../../assets/img/bxtrash.svg";
import { ExclamationCircleFilled } from "@ant-design/icons";
import { ColumnsType } from "antd/es/table";
import { deleteTask } from "../../api/task";
const SourceDatasetView: React.FC = () => {
  const navigate = useNavigate();
  const [uploadErrorModal, setUploadErrorModal] = useState<boolean>(false);
  const [uploadDatasetModal, setUploadDatasetModal] = useState(false);
  const [selectedRows, setSelectedRows] = useState<DataSetType[]>([]);
  const { confirm } = Modal;
  const tableRef = useRef(null);
  return (
    <Scrollbar>
      <div className="createTaskContent">
        <div
          className="mediumText"
          style={{
            fontSize: "28px",
            lineHeight: "36px",
            fontWeight: "500",
            marginLeft: "2rem",
          }}
        >
          源数据集
        </div>
        <div className="createTaskArea">
          <DatasetTable
            ref={tableRef}
            onSelectRows={(data: DataSetType[]) => setSelectedRows(data)}
            ShowActionColumn={true}
            Extra={
              <div style={{ display: "inline-flex" }}>
                <Button
                  size="large"
                  onClick={() => {
                    setUploadDatasetModal(true);
                  }}
                  className="upload-dataset-btn"
                  style={{ marginRight: "8px" }}
                >
                  <img className="btn-icon" src={bxcodeblock} />
                  导入数据集
                </Button>
                <Button
                  disabled={selectedRows.length === 0}
                  size="large"
                  className="upload-dataset-btn"
                  style={{ marginRight: "16px" }}
                  onClick={() => {
                    const hasRelatedTask = selectedRows.some(
                      (row) => row.relatedQATaskList.length > 0
                    );
                    let relatedTaskList: any[] = [];
                    if (
                      selectedRows.filter(
                        (row) => row.relatedQATaskList.length > 0
                      ).length > 0
                    ) {
                      relatedTaskList = selectedRows
                        .filter((row) => row.relatedQATaskList.length > 0)
                        .map((item) =>
                          item.relatedQATaskList.map((task) => {
                            return {
                              ...task,
                              dataSetName: item.name,
                              dataSetId: item.id,
                            };
                          })
                        )
                        .reduce((pre, cur) => pre.concat(cur));
                    }

                    const columns: ColumnsType<TaskDeleteReviewType> = [
                      {
                        title: "数据集名称",
                        dataIndex: "datasetName",
                        key: "datasetName",
                        render: (_, taskInfo) => {
                          const { datasetName } = taskInfo;
                          return <a className="dataset-name">{datasetName}</a>;
                        },
                      },
                      {
                        title: "任务名称",
                        dataIndex: "name",
                        key: "name",
                        render: (_, taskInfo) => {
                          const { taskName, taskId } = taskInfo;
                          return (
                            <a
                              className="dataset-name"
                              onClick={(e) => {
                                e.preventDefault();
                                console.log(e);
                                navigate(`/main/task/detail/${taskId}`);
                                modal.destroy();
                              }}
                            >
                              {taskName}
                            </a>
                          );
                        },
                      },
                      {
                        title: "Action",
                        key: "action",
                        render: (_, { taskId }) => (
                          <Space>
                            <Button
                              type="link"
                              style={{
                                color: "#0fb698",
                                fontFamily: "HarmonyOS Sans SC Reqular",
                              }}
                              onClick={() => {
                                navigate(`/main/task/detail/${taskId}`);
                                modal.destroy();
                              }}
                            >
                              详情
                            </Button>
                            <Button
                              type="link"
                              style={{
                                color: "#0fb698",
                                fontFamily: "HarmonyOS Sans SC Reqular",
                              }}
                              onClick={() => {
                                const taskModal = confirm({
                                  centered: true,
                                  title: "删除提示",
                                  icon: <ExclamationCircleFilled />,
                                  width: 540,
                                  content: (
                                    <>
                                      <div
                                        className="default-info"
                                        style={{ color: "black" }}
                                      >
                                        确定要删除所选任务吗？
                                      </div>
                                    </>
                                  ),
                                  footer: [
                                    <div
                                      style={{
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "flex-end",
                                        padding: "2rem 0 0 0",
                                        gap: "8px",
                                      }}
                                    >
                                      <Button
                                        type="text"
                                        onClick={() => taskModal.destroy()}
                                        shape="round"
                                      >
                                        取消
                                      </Button>
                                      <Button
                                        type="primary"
                                        onClick={() => {
                                          deleteTask([taskId]).then((res) => {
                                            if (res.data.code === 200) {
                                              taskModal.destroy();
                                              modal.destroy();
                                            } else {
                                              message.error(res.data.message);
                                            }
                                          });
                                        }}
                                        shape="round"
                                        className="primary-btn"
                                        style={{ width: "120px" }}
                                      >
                                        确认删除
                                      </Button>
                                    </div>,
                                  ],
                                  onOk() {
                                    deleteTask([taskId]).then((res) => {
                                      if (res.data.code === 200) {
                                        taskModal.destroy();
                                        modal.destroy();
                                      } else {
                                        message.error(res.data.message);
                                      }
                                    });
                                  },
                                  onCancel() {
                                    taskModal.destroy();
                                  },
                                });
                              }}
                            >
                              删除
                            </Button>
                          </Space>
                        ),
                      },
                    ];
                    const modal = confirm({
                      centered: true,
                      title: "删除提示",
                      icon: <ExclamationCircleFilled />,
                      width: 540,
                      content: (
                        <>
                          <div
                            className="default-info"
                            style={{ color: "black" }}
                          >
                            确定要删除所选数据集吗？
                          </div>
                          {hasRelatedTask ? (
                            <>
                              <div
                                className="upload-error-label"
                                style={{ marginTop: "8px" }}
                              >
                                所选源数据集有关联的推理任务，无法进行删除。如需删除，请先删除关联任务。
                              </div>
                              <Table
                                scroll={{ y: 400 }}
                                size="small"
                                pagination={false}
                                columns={columns}
                                dataSource={relatedTaskList}
                                style={{ flex: 1 }}
                                tableLayout={"fixed"}
                                rowKey="taskId"
                                className="dataset-table"
                              />
                            </>
                          ) : null}
                        </>
                      ),
                      footer: [
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "flex-end",
                            padding: "2rem 0 0 0",
                            gap: "8px",
                          }}
                        >
                          <Button
                            type="text"
                            onClick={() => modal.destroy()}
                            shape="round"
                          >
                            取消
                          </Button>
                          <Button
                            disabled={hasRelatedTask}
                            type="primary"
                            onClick={() => {
                              const rowIds = selectedRows.map(
                                (item) => item.id
                              );
                              (tableRef.current as any)?.handleDeleteRows(
                                rowIds
                              );
                              deleteDataset(rowIds).then((res) => {
                                modal.destroy();
                                if (
                                  res.data.code === 0 ||
                                  res.data.code === 200
                                ) {
                                  if (tableRef.current) {
                                    (tableRef.current as any).onRefresh();
                                  }
                                }
                              });
                            }}
                            shape="round"
                            className="primary-btn"
                            style={{ width: "120px" }}
                          >
                            确认删除
                          </Button>
                        </div>,
                      ],
                      onOk() {
                        const rowIds = selectedRows.map((item) => item.id);
                        (tableRef.current as any)?.handleDeleteRows(rowIds);
                        deleteDataset(rowIds).then((res) => {
                          modal.destroy();
                          if (res.data.code === 0 || res.data.code === 200) {
                            if (tableRef.current) {
                              (tableRef.current as any).onRefresh();
                            }
                          }
                        });
                      },
                      onCancel() {
                        modal.destroy();
                      },
                    });
                  }}
                >
                  <img className="btn-icon" src={bxtrash} />
                  删除数据集
                </Button>
                <Button
                  type="primary"
                  size="large"
                  shape="round"
                  style={{
                    backgroundColor: "black",
                    fontSize: "14px",
                    fontWeight: "700",
                  }}
                  className="boldText"
                  onClick={() => {
                    navigate("/main/task/create", {
                      state: { selectedRows },
                    });
                  }}
                >
                  {/* 创建推理任务 */}
                  生成训练数据
                </Button>
              </div>
            }
          />
        </div>
        <UploadDatasetModal
          visible={uploadDatasetModal}
          OnClose={() => {
            setUploadDatasetModal(false);
            if (tableRef.current) {
              (tableRef.current as any).onRefresh();
            }
          }}
        ></UploadDatasetModal>
        <UploadErrorModal
          visible={uploadErrorModal}
          OnClose={(rows) => {
            console.log(rows);
            setUploadErrorModal(false);
          }}
        ></UploadErrorModal>
      </div>
    </Scrollbar>
  );
};

export default SourceDatasetView;

@font-face {
  font-family: 'HarmonyOS Sans SC Reqular';
  font-display: 'swap';
  src: url('./assets/fonts/HarmonyOS_Sans_SC_Regular.woff2');
}

@font-face {
  font-family: 'HarmonyOS Sans SC Black';
  font-display: 'swap';
  src: url('./assets/fonts/HarmonyOS_Sans_SC_Black.woff2');
}

@font-face {
  font-family: 'HarmonyOS Sans SC Bold';
  font-display: 'swap';
  src: url('./assets/fonts/HarmonyOS_Sans_SC_Bold.woff2');
}

@font-face {
  font-family: 'HarmonyOS Sans SC Light';
  font-display: 'swap';
  src: url('./assets/fonts/HarmonyOS_Sans_SC_Light.woff2');
}

@font-face {
  font-family: 'HarmonyOS Sans SC Medium';
  font-display: 'swap';
  src: url('./assets/fonts/HarmonyOS_Sans_SC_Medium.woff2');
}

@font-face {
  font-family: 'HarmonyOS Sans SC Thin';
  font-display: 'swap';
  src: url('./assets/fonts/HarmonyOS_Sans_SC_Thin.woff2');
}

@font-face {
  font-family: '<PERSON>roy ExtraBold';
  font-display: 'swap';
  src: url('./assets/fonts/Gilroy-ExtraBold.woff2');
}

html {
  overflow-y: hidden;
  font-family: 'HarmonyOS Sans SC Reqular';
  color: "#111111";
}

body {
  margin: 0;
  font-family: 'HarmonyOS Sans SC Reqular';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: "#111111";
}

main {
  font-family: 'HarmonyOS Sans SC Reqular';
}

.enText {
  font-family: 'Gilroy ExtraBold', sans-serif;
}

.reqularText {
  font-family: 'HarmonyOS Sans SC Reqular', sans-serif;
}

.boldText {
  font-family: 'HarmonyOS Sans SC Bold', sans-serif;
  font-weight: 700;
  color: "#111111";
}

.mediumText {
  font-family: 'HarmonyOS Sans SC Medium', sans-serif;
}

.lightText {
  font-family: 'HarmonyOS Sans SC Light', sans-serif;
  font-weight: 300;
}

.blackText {
  font-family: 'HarmonyOS Sans SC Black', sans-serif;
}

h2 {
  font-size: 24px;
  font-weight: 600;
}

/* Button {
  font-family: 'HarmonyOS Sans SC Bold', sans-serif !important;
  font-weight: 700;
} */

.error-info {
  color: red !important;
}

.warning-info {
  color: orange;
}

.default-info {
  color: #6D7279;
  font-size: 14px;
  font-weight: 400;
}

.important-info {
  color: #0FB698;
  font-size: 14px;
  font-weight: 700;
}


.bg {
  /* background-image: linear-gradient(160deg, rgb(255 231 249) 20%,  rgb(223 252 255)  80%); */
  width: 100%;
  height: 100%;
  min-width: 1440px;
  min-height: 900px;
}

span.ant-descriptions-item-label {
  /* min-width: 100px; */
  min-width: 5.4rem;
}

button.linkBtn:not(disabled) {
  border-color: #23FFBB;
  color: #23FFBB;
}

.loginBox {
  /* padding: 1rem 0 12rem 0; */
  /* width: 50%; */
  /* margin-top: 16.5rem; */
  top: 50%;
  position: relative;
  transform: translateY(-50%);
}

.loginInput {
  border-radius: var(--br-5xs) !important;
  width: 26.25rem;
  height: 3.5rem;
}

.loginVerifyCodeInput {
  border-radius: var(--br-5xs) !important;
  height: 3.5rem;
  width: 17.38rem;
}

.loginLf {
  /* background-color: #000000; */
}

.info-label {
  color: #b0b0b0;
  font-weight: 500;
  font-size: 12px;
  display: inline-block;
}

.licenseMessage {
  color: #b0b0b0;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  bottom: 4rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
}

.navTabs {
  display: inline-flex;
  margin: 0 1rem;
}

.navTabItem {
  margin-right: 0.5rem;
  padding: 0.5rem 0.5rem;
  color: #fff;
  font-weight: 500;
  min-width: 72px;
  text-align: center;
  cursor: pointer;
}

.navTabItem.isActive {
  border-bottom: 4px solid #23FFBB;
}

.createTaskBtn {
  width: 400px;
  height: 90px !important;
  font-size: 40px !important;
  left: 50%;
  position: absolute;
  top: 75%;
  transform: translateX(-50%) translateY(-50%);
}

.preAnnotationBtn {
  width: 250px;
  left: 50%;
  position: absolute;
  bottom: 15%;
  transform: translateX(-50%) translateY(-50%);
  background-color: #b5b5b5;
  font-size: 18px !important;
  font-weight: 600;
}

.createTaskBackBtn {
  position: absolute;
  top: 5rem;
  left: 2rem;
}

.createTaskContent {
  max-width: 1920px;
  margin: 0 auto;
  /* width: 76.1%; */
  width: 97.1%;
  padding-top: 2rem;
  text-align: start;
  left: 50%;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  max-width: 1920px;
}

.uploadDataset {
  max-width: 1920px;
  margin: 0 auto;
  /* width: 76.1%; */
  width: 92.1%;
  padding-top: 2.5rem;
  text-align: start;
  left: 50%;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  max-width: 1920px;
}

.tag-item-close-btn {
  background-color: transparent;
  color: 'white';
  /* background-color: #a5b1c5; */
  border-radius: "50%";
  font-size: "12px";
  transform: translate(-1px, -4px);
}

.dataset-table {
  flex: 1 1 0%;
  border-radius: var(--br-5xs);
  border: 1px solid var(--color-whitesmoke-300);
  box-sizing: border-box;
  overflow-y: auto;
}

.dataset-table th.ant-table-cell {
  color: var(--color-lightslategray-100) !important;
  background-color: var(--color-gray-200) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: unset !important;
}

.createTaskArea,
.uploadDatasetArea {
  border-radius: var(--br-5xl);
  padding: 2rem;
  margin: 2rem 0;
  background-color: var(--color-white);
  height: max-content;
  display: flex;
  min-height: 680px;
  flex-direction: column;
}

.ant-spin-nested-loading {
  height: 100%;
}

.ant-spin-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ant-table {
  flex: 1;
}
.taskDetailBottom {
color: #8E98A7;
padding: 10px;
}
.taskDetailBottom .span1{
  background-color: rgb(153, 215, 202);
}
.taskDetailBottom .span2{
  background-color: rgba(153, 215, 202,.6);
}
.taskDetailBottom .span3{
  background-color: rgba(153, 215, 202,.3);
}
.createTaskSelectBtn {
  border-radius: var(--br-5xs);
  background: linear-gradient(180deg, #fff, #f3f6f9);
  border: 1px solid var(--color-whitesmoke-300);
  box-sizing: border-box;
  width: 8.5rem;
  height: 2.25rem;
  padding: var(--padding-3xs) 0;
  align-items: center;
  justify-content: center;
  display: inline-flex;
}

.crateTaskDraggerLabel {
  line-height: 1.38rem;
  color: var(--color-lightslategray-100);
}

.createTaskLabel {
  font-size: 16px;
  font-weight: bolder;
  margin-right: 1rem;
}

.customConfigLabel {
  font-size: 14px;
  min-width: 100px;
  margin-right: 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
}

.sliderNode {
  width: 12px;
  height: 12px;
  background: #F1F6F9;
  border: 1px solid #E1EAEF;
  box-sizing: border-box;
  cursor: pointer;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.sliderNode.active-node {
  background: #0FB698;
  border: 1px solid #0CA287;
}

.sliderNode.current-active-node {
  background-color: #23FFBB;
  width: 18px;
  height: 18px;
  background: #0FB698;
  border: 1px solid #6FCEBD;
  box-shadow: 0px 2px 6px rgba(15, 182, 152, 0.4);
}

.sliderTrack {
  height: 6px;
  background: #F1F6F9;
  border-top: 1px solid #E1EAEF;
  border-bottom: 1px solid #E1EAEF;
  padding: 0 .25rem;
}

.sliderTrack.active-track {
  background: #0FB698;
  border-top: 1px solid #0CA287;
  border-bottom: 1px solid #0CA287;
}

.slider-label {
  box-sizing: border-box;
  width: 132px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid #EEF1F5;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.segmentedSlider {
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.taskDetailLf {
  /* height: 300px; */
  /* padding: 8px 12px;
  padding-inline-start: 8px;
  margin-bottom: 0.5rem; */
 
    background: rgb(255,255,255);
  box-shadow: 0px 4px 4px rgba(119, 146, 185, 0.1);
  border-radius: 24px;
  padding: 1rem;
  margin: 1rem 1rem 1rem 0;

}
.taskDetailLf1 {
    background: rgb(255,255,255);
  box-shadow: 0px 4px 4px rgba(119, 146, 185, 0.1);
  /* border-radius: 24px; */
  padding: 1rem;
  margin: 1rem 0 0 0;

}

.taskDetailContent {
  padding: 1rem;
  border: 1px solid #efefef;
}

.taskNameInput {
  width: 50%;
  border-radius: 0;
  border-top: unset;
  border-left: unset;
  border-right: unset;
}

input.ant-input.taskNameInput {
  box-shadow: unset;
}

.uploadDatasetDragger,
.createTaskDragger {
  width: 100%;
  height: 300px;
  display: inline-block;
  margin-top: 1rem;
}

.uploadDatasetDragger {
  height: 239px;
}



.createTaskDraggerInner {
  transform: translateY(-40%);
}

.uploadDatasetDragger .ant-upload.ant-upload-drag,
.createTaskDragger .ant-upload.ant-upload-drag {
  border-radius: var(--br-base);
  background-color: var(--color-mediumaquamarine-400);
  border: 1px dashed var(--color-mediumaquamarine-300);
  box-sizing: border-box;
}

.createTaskDescExample {
  border-radius: var(--br-9xs);
  background-color: var(--color-mediumpurple-200);
  width: 2.75rem;
  height: 1.5rem;
  color: var(--color-mediumpurple-100);
  display: inline-flex;
  justify-content: center;
}

.createTaskDraggerIcon {
  /* position: absolute;
  top: 3.5rem;
  left: 42.56rem; */
  width: 1.5rem;
  height: 1.5rem;
  overflow: hidden;
  margin-bottom: 2rem;
}

.createTaskRequiredLabel {
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.mainContent {
  display: flex;
  left: 50%;
  position: absolute;
  top: 45%;
  transform: translateX(-50%) translateY(-50%);
}

.mainFuncItem {
  width: 250px;
  height: 250px;
  background-color: #d9d9d9;
  margin-right: 4rem;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;
  cursor: pointer;
}

.createTaskDraggerInfo {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 1rem;
}

.createTaskInfo {
  margin: 0 0 0.5rem 1rem;
}

.pageLayout {
  /* background-color: #000000; */
  background: linear-gradient(to bottom, #020F1F, #000000);
  /* 设置背景颜色在所有方向上铺满整个页面 */
  background-size: 100% 100%;
  /* 可选：设置背景颜色固定在页面上，不随内容滚动 */
  background-attachment: fixed;
  /* 可选：设置背景颜色的平铺方式 */
  background-repeat: no-repeat;
}

.loginBox input {
  background-color: #f4f4f4;
  border-radius: 20px;
}

.registerForm {
  margin-top: 1.5rem;
}

.registerForm label.ant-form-item-required {
  width: 75px;
}

span.ant-input-affix-wrapper.ant-input-password {
  background-color: #f4f4f4;
  border-radius: 20px;
}

.fogotPassword {
  position: absolute;
  right: 0;
  transform: translateY(-90%);
}

.loginRt {
  /* height: 100vh; */
  display: flex;
  flex-direction: column;
  /* justify-content: center; */
  align-items: center;
}

.loginTitle {
  text-align: start;
  font-weight: bolder;
  font-size: 2.5rem;
  color: var(--color-black);
}

.loginTabs {
  margin-top: 2.5rem;
}

.loginTabs .ant-tabs-tab {
  padding: .25rem 0 !important;
  width: inherit;
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--color-lightslategray-100);
  text-align: center;
  margin: 0 24px 0 0 !important;
}

.loginTabs .ant-tabs-tab.ant-tabs-tab-active {
  font-size: var(--font-size-xl);
  color: var(--color-mediumaquamarine-300);
}

.loginTabs .ant-tabs-content-holder {
  padding-top: 1rem;
}

.loginBox .ant-divider {
  margin: 0 0 18px 0 !important;
  color: "#fff";
  border-color: "#d9d9d9";
}

.loginBtn {
  border-radius: var(--br-5xs);
  height: 3.5rem;
  background-color: #000000;
  color: white;
  font-weight: 700;
}

.verifyloginBtn {
  width: 26.25rem;
}

.registerBtn {
  height: 3.5rem;
  font-weight: 500;
  color: var(--color-mediumaquamarine-300);
  border-radius: var(--br-40xl);
  border-color: var(--color-mediumaquamarine-300);
  font-weight: 700;
}

.sendVerifyCodeBtn {
  height: 3.5rem;
  font-weight: 700;
  color: var(--color-mediumaquamarine-300);
  border-radius: var(--br-40xl);
  border-color: var(--color-mediumaquamarine-300);
  width: 8.13rem;
}

.ant-tabs-nav {
  border: unset !important;
}

.pageHeader {
  top: 0;
  background: conic-gradient(from 78.02deg at 50% 50%, #000f94 0deg, #111 73.13deg, #107177 198.75deg, #0f927a 232.5deg, #16c7a7 256.88deg, #903ef9 315deg, #2235da 343.13deg, #000f94 360deg, #111 433.13deg);
  -webkit-backdrop-filter: blur(36px);
  backdrop-filter: blur(36px);
  height: 5.88rem;
  font-size: var(--font-size-base);
  color: var(--color-white);
}

.frame-child179,
.frame-child180 {
  width: 1rem;
  height: 1rem;
}

.dataset-table-empty-label {
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;

  color: #8E98A7;
}

.createBtn {
  width: 202px;
  height: 48px;
  background: #0FB698;
  border-radius: 24px;
  font-weight: 700;
  font-size: 16px;
  line-height: 19px;
  color: #FFFFFF;
}

.create-task-file-list {
  width: 790px;
  left: 50%;
  transform: translateX(-50%);
  position: relative;
}

.upload-list-item {
  box-sizing: border-box;
  width: 100%;
  height: 48px;
  background: #FFFFFF;
  border: 1px solid #EEEEEE;
  box-shadow: 0px 2px 4px rgba(119, 146, 185, 0.05);
  border-radius: 8px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 1rem;
  margin-bottom: .5rem;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.ParsingDragger {
  width: 1053px;
  height: 197px;
}

.ParsingDragger .ant-upload.ant-upload-drag {
  border: 4px dashed #707070;
  background: #2D323C;
}

.qaListItemDiv {
  text-align: start;
  width: 90%;
}

.qa-list li.ant-list-item {
  padding: 8px 8px;
}

.qa-list-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  white-space: pre-line;
}

.qa-item-container {
  display: inline-flex;
  align-items: center;
  gap: 20px;
  width: 100%;
  flex-direction: row;
  transition: height 0.3s ease;
}

.qa-item-container-expanded {
  height: 150px;
}


.qa-list-label {
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  transition: height 0.5s ease;
  /* 添加宽度过渡动画 */
}

.animated-div {
  width: 200px;
  background-color: #3498db;
  color: #fff;
  cursor: pointer;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.animated-div.expanded {
  max-height: 500px;
  /* Adjust the maximum height as needed */
}

.text-container {
  max-height: 50px;
  /* Set the initial height for the ellipsis */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: max-height 0.3s ease;
}

.animated-div.expanded .text-container {
  max-height: none;
  /* Remove the max-height to reveal the entire text */
}

span.anticon.anticon-form.QaListItemEdit {
  color: #23FFBB;
  cursor: pointer;
}

.QaListItem:hover {
  background-color: #23FFBB;
}

.QaListItem:hover .QaListItemDiv {
  color: #000000;
}

.QaListItem:hover .QaListItemDiv .QaListItemLabelQ {
  color: #000000;
}

/* .QaListItem:hover .QaListItemDiv .QaListItemLabelA {
  color: #000000;
} */

.QaListItem:hover span.anticon.anticon-form.QaListItemEdit {
  color: #000000;
}

.primary-btn {
  font-weight: 700;
  font-size: 14px;
  line-height: 16px;
  color: #FFFFFF;
  background: #111111;
  border-radius: 59px;
  height: 40px;
}

.default-btn {
  box-sizing: border-box;
  background: linear-gradient(180deg, #FFFFFF 0%, #F3F6F9 100%);
  border: 1px solid #EEF1F5;
  border-radius: 8px;
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #000000;
  display: inline-flex;
  align-items: center;
  gap: 16px;
  height: 40px;
}

.defualt-input {
  border-radius: 8px;
  border: 1px solid #D7DDE7;
  background: #FFF;
}

.upload-error-label {
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;
  color: #8E98A7;
  margin-bottom: 12px;
}

.detailContent {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 20px;
}

.task-detail-info {
  flex: 1.3;
  display: flex;
  flex-direction: column;

}
.head_info{
  background: rgb(255,255,255);
  box-shadow: 0px 4px 4px rgba(119, 146, 185, 0.1);
  border-radius: 24px;
  padding: 1rem;
  margin: 1rem 1rem 1rem 0;
  height: 22rem;
  
}
.ant-tree {
  font-size: 14px;
}
.ant-space-gap-col-small{
  column-gap: 0;
}
.ant-collapse-small >.ant-collapse-item >.ant-collapse-content>.ant-collapse-content-box{
  padding: 0;
}
.ant-descriptions-item-content{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis !important;
}
.task-detail-info label {
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #111111;
}

.task-detail-info Button {
  width: 120px;
  height: 40px;
}

.task-detail-info .ant-descriptions {
  padding: unset ;
}

.task-detail-content {
  flex: 4;
  background: #FFFFFF;
  border-radius: 24px;
  height: 820px;
  padding: 2rem 2.5rem 2.5rem 2.5rem;
  margin-top: 1rem;
}

.task-detail-content .task-title {
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  color: #111111;
  flex-shrink: 0;
}


.review-tag {
  width: 88px;
  height: 32px;

  background: linear-gradient(180deg, #111111 0%, #0FB698 100%);
  border-radius: 8px;
  font-weight: 700;
  font-size: 16px;
  line-height: 19px;
  color: #FFFFFF;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.default-review-lf {
  width: 33.7%;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  border: 1px solid #EEF1F5;
  display: inline-block;
}

.default-review-rg {
  width: 66.3%;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border-top: 1px solid #EEF1F5;
  border-right: 1px solid #EEF1F5;
  border-bottom: 1px solid #EEF1F5;
  display: inline-block;
}

.avatar-edit-btn {
  position: absolute;
  top: 8px;
  width: 24px !important;
  min-width: 24px !important;
  height: 24px;
  font-size: 12px;
  right: -6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-edit-btn EditOutlined {
  height: 12px;
  width: 12px !important;
  min-width: 12px !important;
}

.avatar-edit-btn span.anticon.anticon-edit {
  font-size: 12px;
  color: #7886AA;
}

.edit-avatar-list {
  overflow: hidden;
  height: 157px;
  padding: 15px 20px;
  font-size: 14px;
  font-weight: 500;
  line-height: 16px;
}

.edit-avatar-pre-btn {
  width: 32px;
  height: 177px;
  flex-shrink: 0;
  background: rgba(244, 246, 247, 0.80);
  backdrop-filter: blur(8px) !important;
  position: absolute;
  left: 0;
  bottom: 0;
  border-bottom-left-radius: 24px;
  border: unset;
  z-index: 1;
}

.edit-avatar-next-btn {
  width: 32px;
  height: 177px;
  flex-shrink: 0;
  background: rgba(244, 246, 247, 0.80);
  backdrop-filter: blur(8px) !important;
  position: absolute;
  right: 0;
  bottom: 0;
  border-bottom-right-radius: 24px;
  border: unset;
  z-index: 1;
}

.tag-item:hover .tag-item-close-btn {
  /* display: initial; */
  visibility: inherit;
}

.tag-item-close-btn {
  /* display: none; */
  visibility: hidden;
  /* position: 'absolute';
  top: '-8px'; */
}

.preview-list-item {
  display: flex;
  width: 100%;
  justify-content: space-between;
  cursor: pointer;
}


.preview-list-item-label {
  display: inline-flex;
  width: 100%;
  align-items: center;
  line-height: 24px;
  text-decoration-line: underline;
  gap: 8px;
  cursor: pointer;
}


.preview-list-item-btn {
  display: none;
}

.preview-list-item:hover .preview-list-item-btn {
  display: inline-flex;
}

.preview-folder-modal .ant-list-header {
  border: 1px solid #EEE;
  background: #F7FBFD;
}

.text-ellipsis {
  overflow-wrap: break-word;
  overflow-x: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.scroll-page {
  width: 1876px;
  height: 1056px;
  flex-shrink: 0;
}

.primary-btn:hover {
  background-color: #4096ff !important;
}

.carousel-img {
  width: 831px;
  height: 468px;
  flex-shrink: 0;
  border-radius: 12px;
  border: 8px solid #000;
  background: lightgray 50% / cover no-repeat;
  box-shadow: 0px 24px 44px 0px rgba(0, 0, 0, 0.25);
  position: relative;
  left: 39.6%;
  position: absolute;
  margin-top: 19.5%;
}

.scroll-snap-item {
  scroll-snap-align: start;
  scroll-snap-stop: always;
  position: relative;
}

.div648 .ScrollbarsCustom-Scroller {
  scroll-snap-type: y mandatory;
}

.grid-link-btn {
  padding-left: unset;
  padding-right: unset;
}

.add-btn {
  /* display: none; */
  visibility: hidden;
}

.tag-list {
  overflow: hidden;
  padding: 4px 0;
  width: 100%;
}

.tag-list:hover .add-btn {
  /* display: initial; */
  visibility: visible;
}

.dataset-name:not(:hover) {
  color: #000;
}

.carousel {
  height: 440px;
  display: inline-flex;
  gap: 16px;
}

.carousel-item {
  width: 360px;
  height: 440px;
  background-size: contain;
}

.react-grid-Grid {
  min-height: 560px !important;
}

.pg-viewer-wrapper {
  overflow-y: auto !important;
}

.preview-folder-modal .ant-list-header {
  padding: 7px !important;
}

.preview-back-btn {
  border-radius: 4px !important;
  border: 1px solid #EEF1F5;
  background: #FFF;
  width: 88px;
  height: 36px !important;
  margin-right: 16px;
  padding: 8px 11px !important;
  color: #7886AA;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.preview-header {
  padding: 7px !important;
  border: 1px solid #EEE;
  background: #F7FBFD;
  padding-inline: 24px;
  color: #111;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 36px;
  border-bottom: unset;
}

.taskContent-segmented.ant-segmented {
  padding: 4px !important;
  background-color: rgba(241, 246, 249, 1);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

/* .example-scroll {
  animation: scrollVertical 5s linear infinite;
}

@keyframes scrollVertical {
  from {
    opacity: 0;
  }

  to {
    opacity: 1; 
  }
} */

span.ant-slider-dot {
  background: #f0f0f0 !important;
}

.ant-slider-handle {
  z-index: 100;
}

/* .ant-slider-handle::after {
  background-image: url("/public/slider-handle.svg");
  background-size: 36px;
  background-position-x: center;
  background-position-y: 28px;
} */

.label.ant-segmented-item.ant-segmented-item-selected .ant-segmented-item-label {
  color: #19C1A3 !important;
}

.model-config,
.model-config-active {
  font-family: 'HarmonyOS Sans SC Reqular', sans-serif;
  color: #8E98A7;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
}

.model-config-active {
  color: #19C1A3 !important;
}

.table-pagination {
  display: flex;
  align-items: center;
  padding-left: 2.8%;
  color: #8E98A7;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  justify-content: space-between;
  border-radius: 0px 0px 8px 8px;
  border-bottom: 1px solid #EEF1F5;
  border-left: 1px solid #EEF1F5;
  border-right: 1px solid #EEF1F5;
  border-top: unset;
}

.table-pagination .ant-select-selector,
.table-pagination Input,
.table-pagination Button {
  border-radius: 0px !important;
  border-left: 1px solid #EEF1F5 !important;
  border-right: 1px solid #EEF1F5 !important;
  border-top: unset !important;
  border-bottom: unset !important;
}

.table-pagination Button {
  border-radius: 0px 0px 8px 0px !important;
}

.ant-tabs-tab-active {
  font-family: 'HarmonyOS Sans SC Bold', sans-serif !important;
  font-weight: 700;
}

.slider-node-label {
  width: 10;
  height: 10;
  margin-top: -10;
  z-index: 10;
}

.task-list-active {
  border-radius: 0px 8px 8px 0px;
  background: #DBF4F0;
}

/* .task-list-item:hover {
  border-radius: 0px 8px 8px 0px;
  background: #f5f5f5;
} */

.task-list-review::before {
  content: '';
  display: block;
  width: 3px;
  height: 100%;
  background-color: rgba(15, 182, 152);
  position: absolute;
  left: 0;
  top: 0;
}

/* .task-list-active:hover {
  color: rgba(15, 182, 152);
} */

/* .task-list-active .expand-btn {
  display: none;
  padding: unset;
  height: 22px;
  position: absolute;
  right: 18px;
}

.task-list-active:hover .expand-btn {
  display: inherit;
} */


.create-task-slider .ant-slider-handle::after,
.review-slider-hight .ant-slider-handle::after {
  background-image: url("./assets/img/slider-handle.svg");
  background-size: 36px;
  background-position-x: center;
  background-position-y: 28px;
}

.create-task-slider span.ant-slider-dot.ant-slider-dot-active,
.review-slider-hight span.ant-slider-dot.ant-slider-dot-active {
  background: #0FB698 !important;
}

.create-task-slider span.ant-slider-dot.ant-slider-dot-active,
.review-slider-hight span.ant-slider-dot.ant-slider-dot-active {
  border-color: #0FB698 !important;
}

.create-task-slider .ant-slider-handle::after,
.review-slider-hight .ant-slider-handle::after {
  box-shadow: 0 0 0 4px #0FB698 !important;
}

.review-slider-mid span.ant-slider-dot.ant-slider-dot-active {
  border-color: rgb(235, 169, 0) !important;
}

.review-slider-mid .ant-slider-handle::after {
  box-shadow: 0 0 0 4px rgb(235, 169, 0) !important;
}

.review-slider-mid span.ant-slider-dot.ant-slider-dot-active {
  background: rgb(235, 169, 0) !important;
}

.review-slider-mid .ant-slider-handle::after {
  background-image: url("./assets/img/slider-handle-mid.svg");
  background-size: 36px;
  background-position-x: center;
  background-position-y: 28px;
}

.review-slider-low span.ant-slider-dot.ant-slider-dot-active {
  border-color: #DA2A2A !important;
}

.review-slider-low .ant-slider-handle::after {
  box-shadow: 0 0 0 4px #DA2A2A !important;
}

.review-slider-low span.ant-slider-dot.ant-slider-dot-active {
  background: #DA2A2A !important;
}

.review-slider-low .ant-slider-handle::after {
  background-image: url("./assets/img/slider-handle-low.svg");
  background-size: 36px;
  background-position-x: center;
  background-position-y: 28px;
}

label.ant-form-item-required,
.ant-form-item-label label {
  font-family: 'HarmonyOS Sans SC Reqular', sans-serif;
}

.ant-modal-title {
  font-family: 'HarmonyOS Sans SC Medium', sans-serif;
  font-weight: 500 !important;
}

span.ant-select-selection-item {
  font-family: 'HarmonyOS Sans SC Reqular', sans-serif;
}

span.ant-select-selection-item {
  font-size: 14px;
}

span.ant-descriptions-item-label {
  color: #8E98A7;
}

.color-dimgray {
  color: var(--color-dimgray);
}

.prompt-div {
  height: 20px;
  overflow: hidden;
  display: inline-flex;
}

.prompt-div .use-btn {
  display: none;
}

.prompt-div:hover .use-btn {
  display: inherit;
}

.review-qa-list {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  color: #444444
}

.review-qa-list .current-qa {
  color: rgba(15, 182, 152);
}

.review-textarea:disabled {
  color: rgb(109, 114, 121);
}

.normal-active {
  background-color: rgba(251, 226, 164, 1) !important;
  color: #000;
}

.great-active {
  background: #88DBCC !important;
  color: #000;
}

.bad-active {
  background: #B4B4B4 !important;
  color: #000;
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  50% {
    transform: translateX(5px);
  }

  75% {
    transform: translateX(-5px);
  }
}

.shake-text {
  display: inline-block;
  animation: shake 0.5s 1;
}

.review-btn-default,
.review-btn-delete,
.review-btn-confirm {
  font-weight: 700;
  font-size: 14px;
  line-height: 16px;
  color: #000;
  background: #D7DDE5;
  border-radius: 59px;
  height: 40px;
}

.review-btn-delete.ant-btn-primary:hover {
  color: #FFFFFF !important;
  background: #111111 !important;
}

.review-btn-default.ant-btn-primary:hover {
  background: var(--btn-hover-color, #1677ff) !important;
}

.review-btn-confirm {
  background: #0FB698;
  color: #FFF;
}

.text-spacing {
  letter-spacing: 5px;
}

.review-btn-default:disabled {
  color: #000;
  background: #D7DDE5;
}

.config-modal-container {
  padding: 26px 33px;
  display: flex;
  gap: 1rem;
  flex-direction: column;
}

.default-label {
  color: #6D7279;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.title-label {
  color: #111;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.step-label {
  color: #6D7279;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.btn-label {
  color: #111;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.taskid-copy-img1 {
  width: 10px;
  height: 12px;
  background: #FFFFFF;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  border: 2px solid #0FB698;
  transform: translateY(251%) translateX(-251%);
}

.taskid-copy-img1-1 {
  width: 10px;
  height: 12px;
  background: #FFFFFF;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  border: 2px solid #0FB698;
  transform: translateY(281%) translateX(-251%);
}

.taskid-copy-img2-1 {
  width: 10px;
  height: 12px;
  background: #FFFFFF;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  border: 2px solid #0FB698;
  transform: translateY(204%) translateX(-220%);
}

.taskid-copy-img2 {
  width: 10px;
  height: 12px;
  background: #FFFFFF;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  border: 2px solid #0FB698;
  transform: translateY(175%) translateX(-220%);
}

.checkprogess {
  /* transform: translateY(-73px); */
  left: 58%;
  top: -17.7%;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: 400;
}

.line {
  width: 95%;
  border-top: solid #EDF1F2 1px;
  position: absolute;
  top: 5rem;
  left: 1.5rem
}

.namesytle {
  display: flex;
  margin: 0 0 0 2.5rem;
  font-size: 14px;
  font-weight: 400;
  color: #111111;
  -webkit-background-clip: text;
}

.ant-progress-bg {
  background: linear-gradient(270deg, #42B893 0%, rgba(0, 184, 111, 0.4) 100%) !important;
}

.ant-select-selector {
  /* width: 70px; */
  height: 16px;
  font-size: 14px;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC !important;
  font-weight: 400;
  color: #8E98A7 !important;
  line-height: 16px;
  -webkit-background-clip: text;
}

.ant-table-header {
  background-color: var(--color-gray-200) !important;
}

.ant-modal-wrap .ant-modal-centered {
  overflow: hidden !important;
}

.ant-wave {
  display: none !important;
}

.ant-drawer-content {
  border-radius: 0 10px 0 0;
}

.myModelArea {
  border-radius: var(--br-5xl);
  background-color: var(--color-white);
  height: max-content;
  display: flex;
  min-height: 200px;
  flex-direction: column;
}

.moduletext {
  position: absolute;
  bottom: 102px;
  font-size: 14px;
}

.moduletextone {
  position: absolute;
  bottom: 102px;
  font-size: 14px;
  left: 23%;
}

.moduletext {
  position: absolute;
  bottom: 100px;
  background: #FFFFFF;
  box-shadow: 0px 4px 4px 0px rgba(119, 146, 185, 0.1);
  border-radius: 0px 0px 0px 0px;
  color: #111111;
  font-size: 14px;
}

.visableselect .ant-select-selector {
  border-color: transparent !important;
  box-shadow: none !important;
}

.react-mark-down {
  img {
    width: 100%;
  }
}

.review-btn-default:hover svg path {
  fill: white;
  stroke: white;
}

.listStyle {
  flex: 1;
}

.context {
  flex: 4;
  padding: 10px;

}

.ant-card-body {
  padding: 0 !important;
  display: flex;
}

.listStyle .ant-list-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  width: 300px;

}
.custom-button {
  border-radius: 20px; /* 圆角 */
  background-color: white; /* 白色背景 */
  color: black; /* 黑色字体 */
  border: 1px solid white; /* 边框 */
  transition: all 0.3s ease; /* 平滑过渡效果 */
  width: 100px;
  height: 40px;
}

.custom-button:hover {
  background-color: black; /* 悬浮时黑色背景 */
  color: white; /* 悬浮时白色字体 */
}
/* .listStyle .ant-list-item:hover{
  background-color:rgb(234, 247, 245);
} */
/* .listStyle .ant-list-item .show-btn{
  opacity: 0;
  transition: opacity 0.2s ease;
}
.listStyle .ant-list-item:hover .show-btn{
opacity: 1;
} */
/* .submitBut{
  width: 202px;
  height: 48px;
  background: #0FB698;
  border-radius: 12px;
  font-weight: 700;
  font-size: 16px;
  line-height: 19px;
  color: #FFFFFF;
}
.submitBut:hover{
  color: #FFFFFF !important;
} */

.auto-label {
  margin-top: 20px;
  display: flex;
  align-items: center;
}
.label-textarea {
  min-height: 130px;
  resize: none;
  flex: 1;
  border-color: #0FB698;
  padding: 14px 18px 70px 18px;
  font-size: 16px;
  box-sizing: border-box;
  height: auto;
}
.label-textarea.ant-input {
  /* height: auto !important; */
}

.label-textarea-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 3px 18px 1px;
  height: 65px;
  /* 使底部区域不拦截点击事件 */
  /* pointer-events: none;  */
  background: #fff;
}
.createAiBtn {
  height: 40px;
  white-space: nowrap;
  background: #0FB698;
}
.taskContent-segmented {
  width: 100%;
}
.taskContent-segmented.ant-segmented {
  background: #D8EBE3;
}
.taskContent-segmented.ant-segmented .ant-segmented-item  {
  width: 50%;
  color: #8E98A7;
}
.taskContent-segmented.ant-segmented .ant-segmented-item-selected {
  color: #19C1A3;
}
.taskContent-collapse {
  margin-top: 20px;
  overflow: auto;
  height: 22rem;
  scroll-snap-type: y mandatory;
}

.taskContent-collapse::-webkit-scrollbar,
.tag-container::-webkit-scrollbar {
  /*滚动条整体样式*/
  width : 10px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.taskContent-collapse::-webkit-scrollbar-thumb,
.tag-container::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background : rgba(0, 0, 0, 0.4);
  cursor: pointer;
}
.taskContent-collapse::-webkit-scrollbar-track,
.tag-container::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow : inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  background : #ededed;
}
.filter-container {
  display: flex;
  gap: 10px;
}
.tag-item,
.tag-selected {
   padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #333333;
  user-select: none;
  justify-content: space-between;
}
.tag-item:hover {
  background-color: rgba(0, 0, 0, 0.04) !important;
}
.tag-selected {
  background-color: #D8F2EF !important;
}
.reviewed-dropdown.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-vertical {
  width: 40%;
}
.reviewed-dropdown.ant-dropdown .ant-dropdown-menu {
  height: 300px;
}
.delete-model .ant-modal-content {
  height: 226px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 20px 40px;
}
.delete-model .ant-modal-content .ant-modal-body {
  display: flex;
  justify-content: center;
  align-items: center;
}
.delete-model .ant-modal-content .ant-modal-footer {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 100px
}
.modal-btn.ant-btn-default:not(:disabled):not(.ant-btn-disabled):hover {
  background: #111111 !important;
  width: 82px;
  height: 40px;
  border-radius: 59px;
  color: #FFFFFF;
  font-weight: 700;
  border-radius: 59px;
  border: none;
}
.modal-btn.ant-btn-default {
   border: none;
   width: 82px;
   height: 40px;
}
.tree-file.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: #D8F2EF !important;
}
.tree-file.ant-tree .ant-tree-node-content-wrapper {
  height: 36px;
  display: flex;
  align-items: center;
}
.tag-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow: auto;
  height: 22rem;
}
.qa-tag {
  margin: 0 3px 3px 0;
  min-height: 32px;
  border-radius: 2px;
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  padding: 0 10px;
  border: 1px solid #F1F1F1
}
.edit-tag,
.select-tag {
  color: #0FB698 !important;
  cursor: pointer;
  transition: all 0.3s ease;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 7px;
  margin-top: 4px;
}
.select-tag:hover {
  background-color: #99E2DA !important;
}
.qa-item-container:hover {
  border-radius: 0px 8px 8px 0px;
  background: #f5f5f5;
}
.qa-tag .ant-tag.ant-tag-has-color .anticon-close {
  color: #2C2C2C;
}
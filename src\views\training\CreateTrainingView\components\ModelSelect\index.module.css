.training-content {
    display: flex;
    
}

.step1 {
    font-size: 24px;
    font-weight: bold;
    color: #000000;
    line-height: 40px;
}

.operatearea {
    width: 100%;
}

.operatearea .css-dev-only-do-not-override-116m8fx .ant-form-item .ant-form-item-label >label {
    font-size: 16px !important;
}
.operatearea :global(.ant-form-item .ant-form-item-label >label::after) {
    margin-inline-end: 44px !important;
}
.modleselect {
    margin-top: 12px;
    font-size: 16px;
    font-weight: 500;
    color: #000000;
    line-height: 16px;
}
.form-container {
    margin-top: -3.6%;
}



.warntext {
    margin: -30px 0 0 130px;
    font-size: 16px;
    font-weight: 400;
    color: #FA0404;
    line-height: 16px;

}

.modelName {
    margin-top: 54px;
    font-size: 16px;
    font-weight: 400;
    color: #000000;
    line-height: 16px;
}

.filter-input {
    width: 463px;
    /* margin-left: 39px; */
}

.inputtext {
    font-size: 14px;
    font-weight: 400;
    color: #979797;
    line-height: 16px;
    position: absolute;
    left: 478px;
    top: 12px;
}
.modelIntroduction {
    margin-top: 69px;
    font-size: 16px;
    font-weight: 400;
    color: #000000;
    line-height: 16px;
}
.introdution-input {
    width: 900px;
    margin: -10px 0 0 0;
    resize: 'none';
  
}
.buttonstyle {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 350px;
}

import{r as e,j as s,K as a,f as t,v as n,B as o}from"./react-core-BrQk45h1.js";import{J as i,F as l}from"./file-utils-DYD-epjE.js";const r=({exportTaskData:r,visible:c,OnClose:d,OnQADonload:p,exportTypeValue:j})=>{const[h,x]=e.useState("json"),[y,b]=e.useState();return s.jsx(s.Fragment,{children:s.jsx(a,{centered:!0,title:"数据导出",keyboard:!1,maskClosable:!1,width:"520px",styles:{body:{height:"102px"}},open:c,onOk:d,onCancel:d,footer:[s.jsx(o,{type:"primary",className:"primary-btn",style:{width:"124px"},onClick:()=>{if(r){const e=new i;r.forEach((s=>{const a=JSON.stringify(s);e.file(`${s.taskName}.json`,a)})),e.generateAsync({type:"blob"}).then((e=>{l.saveAs(e,"任务数据.zip")}))}p&&p(h),d()},children:"导出"})],children:s.jsx(t,{direction:"vertical",size:20,children:s.jsxs(t,{direction:"vertical",size:12,children:[s.jsx("div",{children:"导出格式"}),s.jsx(n,{size:"large",style:{width:"156px"},value:h,onChange:e=>x(e),options:j?[{value:"JSON",label:"json"},{value:"WORD",label:"word"}]:[{value:"json",label:"json"}]})]})})})})};export{r as D};

import {
  Badge,
  Button,
  Dropdown,
  Empty,
  Input,
  MenuProps,
  message,
  Modal,
  Select,
  Space,
  Table,
} from 'antd';
import {
  UploadOutlined,
  CaretDownOutlined,
  SearchOutlined,
  ExclamationCircleFilled,
  QuestionCircleFilled,
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import { TaskFilter, TaskStatus, TaskType } from '../../../types';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Dispatch, useEffect, useRef, useState } from 'react';
import { formattedTime } from '../../../utils/formatred';
import UploadErrorModal from '../../../components/UploadErrorModal';
import DatasetExportModal from '../../../components/DatasetExportModal';
import TablePagination from '../../../components/TablePagination';
import { Scrollbar } from 'react-scrollbars-custom';
import {
  deleteTask,
  GetTaskParams,
  getTaskProblemDetail,
  getTasks,
  renameTask,
  retryTask,
  getTaskProcess,
  setReviewConfigInfo,
} from '../../../api/task';
import { getAllocateQA } from '../../../api/qa';
import bxtrash from '../../../assets/img/bxtrash.svg';
import bxdownload from '../../../assets/img/bxdownload.svg';
import emptyLogo from '../../../assets/img/empty-logo.svg';
import VisableRangeSelect from '../../../components/VisableRangeSelect';
import ReviewConfigModal from '../../../components/ReviewConfigModal';
import InputComponent from './components/InputComponent';
import { ACTION_ITEMS, DEFAULT_BTN_CONFIG } from './constants/task';
const TaskView: React.FC = () => {
  const { confirm } = Modal;
  const [taskData, setTaskData] = useState<TaskType[]>([]);
  const [uploadErrorModal, setUploadErrorModal] = useState<boolean>(false);
  const [exportModal, setExportModal] = useState<boolean>(false);
  const [reviewConfigModal, setReviewConfigModal] = useState<boolean>(false);
  const [currentTask, setCurrentTask] = useState<TaskType>();
  const [exportTaskData, setExportTaskData] = useState<TaskType[]>([]);
  const [filterAttribute, setFilterAttribute] = useState('taskName');
  const [sortAttribute, setSortAttribute] = useState('createTime');
  const [sortDirection, setSortDirection] = useState('desc');
  const [selectedRows, setSelectedRows] = useState<TaskType[]>([]);
  const [filterTaskName, setFilterTaskName] = useState<string>();
  const [filterInput, setFilterInput] = useState<string>();
  const [problemList, setProblemList] = useState<any[]>([]);
  const navigate = useNavigate();
  const dispath: Dispatch<any> = useDispatch();
  const [apiFlg, setApiFlg] = useState(false);

  const [renameList, setRenameList] = useState<Set<string>>(new Set<string>());
  const intervalRef = useRef<NodeJS.Timer>();
  const configRef = useRef<any>();
  // 人工审核按钮
  const [buttonStates, setButtonStates] = useState<TaskType[]>([]);
  // 分頁
  const [pagination, setPagination] = useState({
    page: 1,
    size: 10,
    total: 0,
  });
  const [tableIndex, setTableIndex] = useState(0);

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: TaskType[]) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
      setSelectedRows(selectedRows);
    },
    getCheckboxProps: (record: TaskType) => ({
      name: record.taskName,
      // disabled: record.status !== TaskStatus.sucess,
    }),
  };
  // 审核配置
  const handleReviewConfig = (id: string) => {
    const params = {
      areviewCriteria: '请审核生成的答案内容是否正确，如不正确请对其进行编辑修改',
      qreviewCriteria: '请审核所提出的问题是否具有价值，如不具有实际应用价值，应将其删除',
      scoreReviewCriteria: '请将生成数据的字符总长度、语言自然度作为主要指标进行综合打分评价',
      taskId: id,
      isStepTwo: true,
      scoreButtonInfoList: DEFAULT_BTN_CONFIG,
    };
    setReviewConfigInfo(params);
    const userId = sessionStorage.getItem('id');
    if (userId) {
      getAllocateQA(id, userId);
    }
  };
  // 人工审核功能
  const handleApply = (e: React.MouseEvent<HTMLElement>, rowdata: any) => {
    // 阻止默认
    e.preventDefault();

    // 如果创建者审核完了一次，则直接跳转到下一步审核页面
    if (rowdata.reviewConfigId === null) {
      handleReviewConfig(rowdata?.taskId);
    }
    const userId = sessionStorage.getItem('id');
    if (userId && rowdata?.taskId) {
      getAllocateQA(rowdata?.taskId, userId!);
    }
    navigate(`/main/task/review/${rowdata?.taskId}`);
    // 新需求，不需要配置直接开发权限

    // 创建者没有审核完成的话，协作者不能点击人工审核按钮
    // 创建者审核过的话，协作者就能点击人工审核按钮跳转到下一页面
  };
  const columns: ColumnsType<TaskType> = [
    {
      title: '序号',
      dataIndex: 'index',
      render: (text, record, index) => (
        // pagination.page - 1) * pagination.size
        <>{tableIndex + index + 1}</>
      ),
      width: '4.6%',
    },
    {
      title: '任务名称',
      dataIndex: 'name',
      shouldCellUpdate: (record: TaskType, prevRecord: TaskType) => {
        return true;
      },
      render: (_, taskInfo) => {
        const { taskName, status, taskId } = taskInfo;
        let updateName = taskName;
        return !renameList.has(taskId) ? (
          <a className="dataset-name">{taskName.replace(/"/g, '')}</a>
        ) : (
          <Space.Compact>
            <InputComponent
              taskInfo={taskInfo}
              renameList={renameList}
              setRenameList={setRenameList}
              getTasksList={getTasksList}
            ></InputComponent>
          </Space.Compact>
        );
      },
      width: '13.2%',
    },
    {
      title: '创建时间',
      dataIndex: 'createdate',
      width: '9.6%',
      render: (_, { createTime }) => {
        const timestamp = createTime;
        return formattedTime(new Date(timestamp));
      },
    },
    {
      title: () => {
        if (taskData.some((item) => item.failedReason)) {
          return (
            <Space>
              任务状态
              <Badge color="#E75252" />
            </Space>
          );
        } else {
          return '任务状态';
        }
      },
      dataIndex: 'status',
      render: (_, { taskId, status, failedReason, complete, total }) => {
        if (status === TaskStatus.error) {
          if (complete && complete === total) {
            return (
              <Space>
                <label className={'warning-info'}>任务完成{`(${complete}/${total})`}</label>
                <QuestionCircleFilled
                  style={{ color: '#E75252', cursor: 'pointer' }}
                  onClick={() => {
                    getTaskProblemDetail(taskId).then((res) => {
                      if (res.data?.code === 200) {
                        // res.data.data.problems.forEach((item: { fileTreeNodeForProblem: any; }) => {
                        //   const fileTreeNodeForProblem = item.fileTreeNodeForProblem;
                        //   setProblemList([fileTreeNodeForProblem]);
                        //   setUploadErrorModal(true);
                        // });
                        setProblemList(res.data.data);
                        setUploadErrorModal(true);
                      }
                    });
                  }}
                />
              </Space>
            );
          } else if (complete && complete < total) {
            return <label className="warning-info">进行中{`(${complete}/${total})`}</label>;
          }
        } else if (status === TaskStatus.failed) {
          return <label className="error-info"> 任务失败</label>;
        } else if (status === TaskStatus.inProgress) {
          return <label>进行中{`(${complete}/${total})`}</label>;
        } else if (status === TaskStatus.success) {
          if (complete && complete === total) {
            return <label className="default-info">任务完成{`(${complete}/${total})`}</label>;
          }
        }
      },
      width: '10.4%',
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      render: (_, { reviewCount, qaCount, creator }) => {
        return <label>{creator.userName}</label>;
      },
      width: '8%',
    },
    {
      title: '可见成员',
      dataIndex: 'visiblerange',
      render: (_, { taskId, reviewers }) => {
        return (
          <VisableRangeSelect
            className="visableselect"
            defaultSelectUser={reviewers}
            onChange={(users: string[]) => {
              console.log(users);
            }}
            taskId={taskId}
          />
        );
      },
      width: '15%',
    },
    {
      title: '来源',
      dataIndex: 'source',
      render: (_, { description }) => {
        if (description && description === '本地上传') {
          return <label>{description}</label>;
        } else {
          return <label>训练数据</label>;
        }
      },
      width: '8%',
    },
    {
      title: '数据审核进度',
      dataIndex: 'dataprocess',
      render: (_, { reviewCount, qaCount }) => {
        if (qaCount || qaCount === 0) {
          return <label>{reviewCount + ' / ' + qaCount}</label>;
        } else {
          return <label>-</label>;
        }
      },
      width: '9.3%',
    },
    {
      title: '操作',
      dataIndex: 'task',
      render: (_, rowData, index) => {
        const { status, reviewCount: progress, taskId, complete, total, buttonDisabled } = rowData;
        let actionNode = <></>;
        const handleActionMenuClick: MenuProps['onClick'] = (e) => {
          if (e.key === 'rename') {
            const renameSet = renameList;
            renameSet.add(taskId);
            setRenameList(renameSet);
          } else if (e.key === 'delete') {
            const modal = confirm({
              centered: true,
              title: '删除提示',
              icon: <ExclamationCircleFilled />,
              width: 540,
              content: (
                <>
                  <div className="default-info" style={{ color: 'black' }}>
                    确定要删除所选任务吗？
                  </div>
                </>
              ),
              footer: [
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    padding: '2rem 0 0 0',
                    gap: '8px',
                  }}
                >
                  <Button type="text" onClick={() => modal.destroy()} shape="round">
                    取消
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => {
                      setTaskData((pre) => pre.filter((item) => item.taskId !== taskId));
                      deleteTask([taskId]).then((res: any) => {
                        if (res.data.code === 200) {
                          getTasksList();
                        } else {
                          message.error(res.data.message);
                        }
                      });
                      modal.destroy();
                    }}
                    shape="round"
                    className="primary-btn"
                    style={{ width: '120px' }}
                  >
                    确认删除
                  </Button>
                </div>,
              ],
              onOk() {
                setTaskData((pre) => pre.filter((item) => item.taskId !== taskId));
                deleteTask([taskId]).then((res: any) => {
                  if (res.data.code === 200) {
                    getTasksList();
                  } else {
                    message.error(res.data.message);
                  }
                });
                modal.destroy();
              },
              onCancel() {
                modal.destroy();
              },
            });
          }
        };
        if (status === TaskStatus.inProgress || status === TaskStatus.success) {
          if (
            (complete && complete === total) ||
            (complete && complete < total) ||
            complete === 0
          ) {
            actionNode = (
              <Button
                type="link"
                className="grid-link-btn"
                onClick={() => {
                  navigate(`/main/task/detail/${taskId}`);
                }}
              >
                详情
              </Button>
            );
          } else {
            actionNode = <span style={{ color: '#0fb698' }}>{' ___'}</span>;
          }
        } else if (
          status === TaskStatus.failed ||
          status === TaskStatus.error ||
          status === TaskStatus.error
        ) {
          if ((complete && complete === total) || (complete && complete < total)) {
            actionNode = (
              <Button
                type="link"
                className="grid-link-btn"
                onClick={() => {
                  getTaskProblemDetail(taskId).then((res) => {
                    if (res.data?.code === 200) {
                      setProblemList(res.data.data);
                      setUploadErrorModal(true);
                    }
                  });
                }}
              >
                问题详情
              </Button>
            );
          } else {
            actionNode = (
              <Button
                type="link"
                className="error-info grid-link-btn"
                onClick={() => {
                  retryTask(taskId).then((res) => {
                    if (res.data?.code === 200) {
                      message.info(res.data.msg);
                    } else {
                      message.error(res.data.msg);
                    }
                  });
                }}
              >
                重新解析
              </Button>
            );
          }
        } else if (status === TaskStatus.inProgress) {
          actionNode = <></>;
        }
        return (
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            {actionNode}
            {status === TaskStatus.success ? (
              <Button
                type="link"
                className="grid-link-btn"
                onClick={(e) => {
                  // setExportTaskData([rowData]);
                  // setExportModal(true);
                  setCurrentTask(rowData);

                  handleApply(e, rowData);
                }}
                // disabled={buttonDisabled}
              >
                人工审核
              </Button>
            ) : (
              (actionNode = <span style={{ color: '#0fb698' }}>{' ___'}</span>)
            )}

            <Dropdown
              menu={{
                items: ACTION_ITEMS,
                onClick: handleActionMenuClick,
              }}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  更多
                  <CaretDownOutlined />
                </Space>
              </a>
            </Dropdown>
          </div>
        );
      },
      width: '20.8%',
    },
  ];

  function getTasksList() {
    if (!apiFlg) {
      setApiFlg(true);
      const params: GetTaskParams = {
        ...pagination,
        sortAttribute: sortAttribute,
        sortDirection: sortDirection,
        filterAttribute: filterAttribute,
        taskName: filterTaskName || '',
      };
      getTasks(params).then((res) => {
        setApiFlg(false);
        if (res?.data?.code === 200) {
          const storedName = sessionStorage.getItem('name');
          const updateData = res.data.data.map((task: any) => {
            let buttonDisabled = false;
            if (storedName !== task.creator.userName) {
              buttonDisabled = task.reviewConfigId === null;
            }
            return {
              ...task,
              buttonDisabled,
            };
          });

          setTaskData(updateData);
          if (res.data.data.length > 0) {
            setPagination({ ...pagination, total: res.data.totalCount });
            setTableIndex((pagination.page - 1) * pagination.size);
          }
        }
      });
    }
  }

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      setFilterTaskName(filterInput);
    } else if (filterInput?.trim().length === 0) {
      setFilterTaskName(filterInput);
    }
  };

  useEffect(() => {
    // clearInterval(intervalRef.current as NodeJS.Timeout);
    getTasksList();
    // intervalRef.current = setInterval(getTasksList, 5000);
    // return () => {
    //   if (intervalRef.current) clearInterval(intervalRef.current as NodeJS.Timeout); // 在组件卸载时清除定时器
    // };
  }, [
    pagination.page,
    pagination.size,
    sortDirection,
    filterAttribute,
    sortAttribute,
    filterTaskName,
  ]);

  useEffect(() => {
    const timerId = setTimeout(() => {
      setFilterTaskName(filterInput);
    }, 500);

    // 清除定时器，确保在下一次useEffect触发前清除之前的定时器
    return () => clearTimeout(timerId);
  }, [filterInput]);

  return (
    <Scrollbar>
      <div className="createTaskContent">
        <div
          style={{
            fontSize: '28px',
            lineHeight: '36px',
            fontWeight: '500',
            marginLeft: '2rem',
          }}
          className="mediumText"
        >
          训练数据
        </div>
        <div className="createTaskArea">
          <div
            style={{
              textAlign: 'start',
              justifyContent: 'space-between',
              display: 'inline-flex',
              marginBottom: '1rem',
              width: '100%',
            }}
          >
            <div>
              <Space size="small">
                <Space.Compact>
                  <Select
                    size="large"
                    className="filter-select"
                    value={filterAttribute}
                    onChange={(val) => setFilterAttribute(val)}
                    options={[{ value: 'taskName', label: '任务名' }]}
                  />
                  <Input
                    size="large"
                    className="filter-input"
                    suffix={<SearchOutlined />}
                    placeholder="请输入任务名称"
                    value={filterInput}
                    onChange={(e) => {
                      setFilterInput(e.target.value);
                    }}
                    onKeyUp={handleKeyPress}
                  />
                </Space.Compact>
                <Select
                  size="large"
                  className="filter-select"
                  value={sortDirection}
                  onChange={(e) => setSortDirection(e)}
                  style={{ width: '9.75rem' }}
                  options={[
                    { value: 'desc', label: '按时间倒序' },
                    { value: 'asc', label: '按时间正序' },
                  ]}
                />
              </Space>
            </div>
            <Space>
              {selectedRows.length === 0 ? null : (
                <Button
                  size="large"
                  className="upload-dataset-btn"
                  onClick={() => {
                    const modal = confirm({
                      centered: true,
                      title: '删除提示',
                      icon: <ExclamationCircleFilled />,
                      width: 540,
                      content: (
                        <>
                          <div className="default-info" style={{ color: 'black' }}>
                            确定要删除所选任务吗？
                          </div>
                        </>
                      ),
                      footer: [
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'flex-end',
                            padding: '2rem 0 0 0',
                            gap: '8px',
                          }}
                        >
                          <Button type="text" onClick={() => modal.destroy()} shape="round">
                            取消
                          </Button>
                          <Button
                            type="primary"
                            onClick={() => {
                              const idList = selectedRows.map((item) => item.taskId);
                              setTaskData((pre) =>
                                pre.filter((item) => !idList.includes(item.taskId))
                              );
                              deleteTask(idList).then((res: any) => {
                                if (res.data.code === 200) {
                                  getTasksList();
                                } else {
                                  message.error(res.data.message);
                                }
                              });
                              modal.destroy();
                            }}
                            shape="round"
                            className="primary-btn"
                            style={{ width: '120px' }}
                          >
                            确认删除
                          </Button>
                        </div>,
                      ],
                      onOk() {
                        const idList = selectedRows.map((item) => item.taskId);
                        setTaskData((pre) => pre.filter((item) => !idList.includes(item.taskId)));
                        deleteTask(selectedRows.map((item) => item.taskId)).then((res: any) => {
                          if (res.data.code === 200) {
                            getTasksList();
                          } else {
                            message.error(res.data.message);
                          }
                        });
                        modal.destroy();
                      },
                      onCancel() {
                        modal.destroy();
                      },
                    });
                  }}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 12,
                  }}
                >
                  <img className="btn-icon" src={bxtrash} alt="删除任务" />
                  删除任务
                </Button>
              )}
              {selectedRows.length === 0 ? null : (
                <Button
                  size="large"
                  className="upload-dataset-btn"
                  onClick={() => {
                    setExportTaskData(selectedRows);
                    setExportModal(true);
                  }}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 12,
                  }}
                >
                  <img className="btn-icon" src={bxdownload} alt="导出结果" />
                  导出结果
                </Button>
              )}
              <Button
                size="large"
                icon={<UploadOutlined />}
                style={{
                  backgroundColor: '#EEF1F5',
                  fontSize: '14px',
                  fontWeight: '500',
                  width: '154px',
                  height: '40px',
                  marginRight: '10px',
                }}
                onClick={() => {
                  navigate('/main/task/uploadDataset');
                }}
              >
                上传本地数据
              </Button>
              <Button
                type="primary"
                size="large"
                shape="round"
                style={{
                  backgroundColor: 'black',
                  fontSize: '14px',
                  fontWeight: '700',
                  width: '122px',
                }}
                className="boldText"
                onClick={() => {
                  navigate('/main/task/create');
                }}
              >
                {/* 创建任务 */}
                生成训练数据
              </Button>
            </Space>
          </div>
          <Table
            locale={{
              emptyText: (
                <Empty
                  image={emptyLogo}
                  description={
                    <span className="dataset-table-empty-label">空空如也，去上传本地文件吧~</span>
                  }
                />
              ),
            }}
            tableLayout={'fixed'}
            rowKey="taskId"
            className="dataset-table"
            rowSelection={{
              type: 'checkbox',
              ...rowSelection,
            }}
            columns={columns}
            dataSource={taskData}
            pagination={false}
          />
          <TablePagination
            total={pagination.total}
            pageSize={pagination.size}
            page={pagination.page}
            OnChange={(page, pageSize) => {
              setPagination({ total: pagination.total, page, size: pageSize });
            }}
          />
        </div>
        <UploadErrorModal
          rowData={problemList}
          visible={uploadErrorModal}
          OnClose={(rows) => {
            console.log(rows);
            setUploadErrorModal(false);
          }}
        ></UploadErrorModal>
        <DatasetExportModal
          visible={exportModal}
          OnClose={() => setExportModal(false)}
          exportTaskData={exportTaskData}
        ></DatasetExportModal>
        <ReviewConfigModal
          visible={reviewConfigModal}
          OnClose={() => setReviewConfigModal(false)}
          taskData={currentTask}
        />
      </div>
    </Scrollbar>
  );
};

export default TaskView;

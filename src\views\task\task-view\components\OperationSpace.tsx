import React from "react";
import { Button, Input, Select, Space } from "antd";
import { UploadOutlined, SearchOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import bxtrash from "../../../../assets/img/bxtrash.svg";
import bxdownload from "../../../../assets/img/bxdownload.svg";

export interface OperationSpaceProps {
  selectedRows: TaskType[];
  filterAttribute: string;
  filterInput: string;
  sortDirection: string;
  setExportTaskData: React.Dispatch<React.SetStateAction<TaskType[]>>;
  setExportModal: React.Dispatch<React.SetStateAction<boolean>>;
  handleKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  setFilterAttribute: React.Dispatch<React.SetStateAction<string>>;
  setFilterInput: React.Dispatch<React.SetStateAction<string>>;
  setSortDirection: React.Dispatch<React.SetStateAction<string>>;
}

const OperationSpace: React.FC<OperationSpaceProps> = ({
  selectedRows,
  filterAttribute,
  filterInput,
  sortDirection,
  setExportTaskData,
  setExportModal,
  handleKeyPress,
  setFilterAttribute,
  setFilterInput,
  setSortDirection,
}) => {
  const navigate = useNavigate();

  return (
    <div
      style={{
        textAlign: "start",
        justifyContent: "space-between",
        display: "inline-flex",
        marginBottom: "1rem",
        width: "100%",
      }}
    >
      <div>
        <Space size="small">
          <Space.Compact>
            <Select
              size="large"
              className="filter-select"
              value={filterAttribute}
              onChange={(val) => setFilterAttribute(val)}
              options={[{ value: "taskName", label: "任务名" }]}
            />
            <Input
              size="large"
              className="filter-input"
              suffix={<SearchOutlined />}
              placeholder="请输入任务名称"
              value={filterInput}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                setFilterInput(e.target.value);
              }}
              onKeyUp={handleKeyPress}
            />
          </Space.Compact>
          <Select
            size="large"
            className="filter-select"
            value={sortDirection}
            onChange={(e) => setSortDirection(e)}
            style={{ width: "9.75rem" }}
            options={[
              { value: "desc", label: "按时间倒序" },
              { value: "asc", label: "按时间正序" },
            ]}
          />
        </Space>
      </div>
      <Space>
        {selectedRows.length === 0 ? null : (
          <Button
            size="large"
            className="upload-dataset-btn"
            onClick={() => {
              // const modal = confirm({
              //   centered: true,
              //   title: "删除提示",
              //   icon: <ExclamationCircleFilled />,
              //   width: 540,
              //   content: (
              //     <>
              //       <div
              //         className="default-info"
              //         style={{ color: "black" }}
              //       >
              //         确定要删除所选任务吗？
              //       </div>
              //     </>
              //   ),
              //   footer: [
              //     <div
              //       style={{
              //         display: "flex",
              //         alignItems: "center",
              //         justifyContent: "flex-end",
              //         padding: "2rem 0 0 0",
              //         gap: "8px",
              //       }}
              //     >
              //       <Button
              //         type="text"
              //         onClick={() => modal.destroy()}
              //         shape="round"
              //       >
              //         取消
              //       </Button>
              //       <Button
              //         type="primary"
              //         onClick={() => {
              //           const idList = selectedRows.map(
              //             (item) => item.taskId
              //           );
              //           setTaskData((pre) =>
              //             pre.filter(
              //               (item) => !idList.includes(item.taskId)
              //             )
              //           );
              //           deleteTask(idList).then((res: any) => {
              //             if (res.data.code === 200) {
              //               getTasksList();
              //             } else {
              //               message.error(res.data.message);
              //             }
              //           });
              //           modal.destroy();
              //         }}
              //         shape="round"
              //         className="primary-btn"
              //         style={{ width: "120px" }}
              //       >
              //         确认删除
              //       </Button>
              //     </div>,
              //   ],
              //   onOk() {
              //     const idList = selectedRows.map((item) => item.taskId);
              //     setTaskData((pre) =>
              //       pre.filter((item) => !idList.includes(item.taskId))
              //     );
              //     deleteTask(
              //       selectedRows.map((item) => item.taskId)
              //     ).then((res: any) => {
              //       if (res.data.code === 200) {
              //         getTasksList();
              //       } else {
              //         message.error(res.data.message);
              //       }
              //     });
              //     modal.destroy();
              //   },
              //   onCancel() {
              //     modal.destroy();
              //   },
              // });
            }}
            style={{
              display: "flex",
              alignItems: "center",
              gap: 12,
            }}
          >
            <img className="btn-icon" src={bxtrash} alt="删除任务" />
            删除任务
          </Button>
        )}
        {selectedRows.length === 0 ? null : (
          <Button
            size="large"
            className="upload-dataset-btn"
            onClick={() => {
              setExportTaskData(selectedRows);
              setExportModal(true);
            }}
            style={{
              display: "flex",
              alignItems: "center",
              gap: 12,
            }}
          >
            <img className="btn-icon" src={bxdownload} alt="导出结果" />
            导出结果
          </Button>
        )}
        <Button
          size="large"
          icon={<UploadOutlined />}
          style={{
            backgroundColor: "#EEF1F5",
            fontSize: "14px",
            fontWeight: "500",
            width: "154px",
            height: "40px",
            marginRight: "10px",
          }}
          onClick={() => {
            navigate("/main/task/uploadDataset");
          }}
        >
          上传本地数据
        </Button>
        <Button
          type="primary"
          size="large"
          shape="round"
          style={{
            backgroundColor: "black",
            fontSize: "14px",
            fontWeight: "700",
            width: "122px",
          }}
          className="boldText"
          onClick={() => {
            navigate("/main/task/create");
          }}
        >
          {/* 创建任务 */}
          生成训练数据
        </Button>
      </Space>
    </div>
  );
};

export default OperationSpace;

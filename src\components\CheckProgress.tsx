import avatar1 from "../assets/img/avatar-1.png";
import infoIcon from "../assets/img/info-icon.svg";
import { formattedTime } from "../utils/formatred";
import { copyToClipboard } from "../utils/copytext";
import {
  Avatar,
  Button,
  Cascader,
  Descriptions,
  DescriptionsProps,
  Divider,
  Dropdown,
  Empty,
  Modal,
  Progress,
  Select,
  Space,
  Tooltip,
  message,
} from "antd";
import { useEffect, useId, useRef, useState } from "react";
import {
  AlluserInfoType,
  DocumentContentPairType,
  FileContent,
  LoginParams,
  QADocument,
  TaskDetailType,
  TaskStatus,
  TaskType,
  ProgressAllInfo,
  DataSetTreeType,
} from "../types";
import {
  GetTaskParams,
  querygetProgress,
  getTaskDetail,
  getTasks,
  queryUserDetail,
} from "../api/task";
import { getDataSetTreeData } from "../api/dataset";
import { DataNode } from "antd/es/tree";
import { useParams } from "react-router-dom";
import Table, { ColumnsType } from "antd/es/table";
import { Scrollbar } from "react-scrollbars-custom";
import emptyLogo from "../assets/img/empty-logo.svg";
import { queryUserInfo } from "../views/login/service";
import { log } from "console";

interface DatasetExportModalProp {
  exportTaskData?: TaskType[];
  visible: boolean;
  OnClose: () => void;
  OnQADonload?: () => void;
}
interface Option {
  value: string | number;
  label: string;
  children?: Option[];
}
type userCounnt = {
  account?: string;
  verifyCode?: string;
};

const CheckProgress: React.FC<DatasetExportModalProp> = ({
  exportTaskData,
  visible,
  OnClose,
  OnQADonload,
}) => {
  // const fileInputRef = useRef<HTMLInputElement>(null);
  const [exportType, setExportType] = useState<string>("json");
  const [exportPath, setExportPath] = useState<string>();
  const handleButtonClick = () => {
    const input = document.getElementById("exportPathInput");
    if (input) {
      input.click();
    }
  };
  //任务信息
  const [taskDetail, setTaskDetail] = useState<TaskDetailType>();
  const [progressDetail, setProgressDetail] = useState<AlluserInfoType[]>();
  const [sortAttribute, setSortAttribute] = useState("createTime");
  const [sortDirection, setSortDirection] = useState("desc");
  const progressNoRef = useRef(progressDetail);
  const [avatar, setAvatar] = useState(avatar1);
  const [apiFlg, setApiFlg] = useState(false);
  const [pairs, setPairs] = useState<DocumentContentPairType[]>([]);
  const [qaList, setQaList] = useState<QADocument[]>([]);
  const [fileContent, setFileContent] = useState<FileContent[]>([]);
  const [filterVal, setFilterVal] = useState<string>();
  const [qaFileMap, setQaFileMap] = useState<Map<string, string>>(
    new Map<string, string>()
  );
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [taskData, setTaskData] = useState<TaskType[]>([]);
  const [tableIndex, setTableIndex] = useState(0);
  const [filterAttribute, setFilterAttribute] = useState("taskName");
  const [filterTaskName, setFilterTaskName] = useState<string>();
  const [progressInfo, setProgressInfo] = useState<ProgressAllInfo[]>([]);
  const options = [
    { value: "切换为总数量", label: "切换为总数量" },
    { value: "切换为已审核", lable: "切换为已审核" },
  ];
  const [selectedValue, setSelectedValue] = useState(options[0].value);
  const handleChange = (value: string) => {
    setSelectedValue(value);
  };
  const onOk = () => {
    OnClose();
  };
  const onCancel = () => {
    OnClose();
  };
  enum QAFilter {
    All,
    Reviewed,
    Unreviewed,
  }
  const [displayQaList, setDisplayQaList] = useState<QADocument[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    size: 10,
    total: 0,
  });
  const { task_id } = useParams<string>();
  const [reviewFilterType, setReviewFilterType] = useState(QAFilter.All);
  const intervalRef = useRef<NodeJS.Timeout>();
  async function fetchDatasetTrees(datasetIds: string[]) {
    const datasetTrees: DataSetTreeType[] = [];
    if (!datasetIds) return;
    for (const datasetId of datasetIds) {
      const res = await getDataSetTreeData(datasetId);
      if (res.data?.code === 200) {
        const fileTree = res.data.data;
        fileTree.childFileId = datasetId;
        datasetTrees.push(fileTree);
      }
    }
    // if (datasetTrees[0].childFileId)
    //   setSelectedKeys([datasetTrees[0].childFileId]);
    setTreeData(datasetTrees as any);
  }
  const updateDisplayQaList = (
    dataList: QADocument[],
    pagination: {
      page: number;
      size: number;
      total: number;
    },
    filter?: string,
    reviewFilter?: number
  ) => {
    let filterResult = JSON.parse(JSON.stringify(dataList));
    let _reviewFilter = reviewFilter;
    if (_reviewFilter === undefined) {
      _reviewFilter = reviewFilterType;
    }
    if (_reviewFilter !== QAFilter.All) {
      filterResult = filterResult.filter((item: QADocument) => {
        if (_reviewFilter === QAFilter.Reviewed) {
          return item.review;
        } else {
          return !item.review;
        }
      });
    }
    if (filter && filter.length > 0) {
      filterResult = filterResult.filter(
        (item: QADocument) =>
          item.question.indexOf(filter) > -1 || item.answer.indexOf(filter) > -1
      );
    }
    const startIdx = (pagination.page - 1) * pagination.size;
    const endIdx = pagination.page * pagination.size;
    const displayList = filterResult.slice(startIdx, endIdx);
    setPagination({ ...pagination, total: filterResult.length });
    setDisplayQaList(displayList);
  };
  const onGetTaskDetail = (taskId: string) => {
    if (!apiFlg) {
      setApiFlg(true);
      getTaskDetail(taskId).then((res) => {
        setApiFlg(false);
        if (res.data?.code === 200) {
          const data = res.data.data;
          setTaskDetail(data);
          fetchDatasetTrees(data.fileIdList);
        }
      });
    }
  };

  const getDetail = () => {
    if (task_id) {
      onGetTaskDetail(task_id);
    }
  };
  useEffect(() => {
    getDetail();
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current); // 在组件卸载时清除定时器
    };
  }, []);

  const taskStatusMap = (taskDetail?: TaskDetailType) => {
    if (taskDetail?.taskStatus === TaskStatus.error) {
      if (taskDetail.complete === taskDetail.total) {
        return <label style={{ color: "orange" }}>任务完成</label>;
      } else if (taskDetail.complete < taskDetail.total) {
        return <label style={{ color: "orange" }}>进行中</label>;
      }
    } else if (taskDetail?.taskStatus === TaskStatus.inProgress) {
      return "进行中";
    } else if (taskDetail?.taskStatus === TaskStatus.success) {
      return "任务完成";
    } else if (taskDetail?.taskStatus === TaskStatus.failed) {
      return <label style={{ color: "red" }}> 任务失败</label>;
    }
  };

  //获取用户名
  const UserName = () => {
    const [name, setName] = useState("");
    useEffect(() => {
      const fetchUserName = async () => {
        const res = await queryUserInfo();
        if (res && res.data) {
          setName(res.data.userName);
        }
      };

      fetchUserName();
    }, []);
    return <b className="namesytle">{name}</b>;
  };
  //获取进度信息
  function ProgressInfo(taskId: string) {
    try {
      if (!apiFlg) {
        setApiFlg(true);
        querygetProgress(taskId).then((res) => {
          setApiFlg(false);
          const data = res?.data;
          if (data) {
            const progressData = Array.isArray(data) ? data : [data];
            // 更新 progressInfo 状态
            setProgressInfo(progressData);
          }
        });
      }
    } catch (e) {}
  }

  const descItems: DescriptionsProps["items"] = [
    {
      key: "1",
      label: "任务名称",
      children: taskDetail?.taskName ? taskDetail?.taskName : "NULL",
    },
    {
      key: "2",
      label: "创建用户",
      children: (
        <>
          {UserName()}
          <Avatar className="ellipse-parent29-1" src={avatar} />
        </>
      ),
    },
    {
      key: "3",
      label: "任务ID",
      children: taskDetail?.id ? taskDetail.id : "NULL",
    },
    {
      key: "4",
      label: "创建时间",
      children: taskDetail?.createTime
        ? formattedTime(new Date(taskDetail?.createTime))
        : "NULL",
    },
    {
      key: "5",
      label: "任务状态",
      children: (
        <>
          {taskStatusMap(taskDetail)}（
          {taskDetail?.complete + " / " + taskDetail?.total}）
        </>
      ),

      // taskDetail?.taskStatusEnum === TaskStatus.success ? (
      // <div>任务完成（{taskDetail.complete + " / " + taskDetail.total}）</div>
      // ):( taskDetail?.taskStatusEnum === TaskStatus.failed ?(
      //   taskDetail.complete > 0 ? (
      //     <div>任务进行中 （{taskDetail.complete + " / " + taskDetail.total}）</div>
      //   ):(<div>任务失败</div>)
      // ):(taskDetail?.taskStatusEnum === TaskStatus.inProgress ? (
      //       <div>任务进行中 （{taskDetail.complete + " / " + taskDetail.total}）</div>
      //      ):(
      //      <div>任务失败</div>
      //      ))
      // )
      //      return taskDetail?.taskStatusEnum === TaskStatus.failed
      // ? taskDetail.complete > 0
      //   ? '任务进行中'
      //   : '任务失败'
      // : taskDetail?.taskStatusEnum === TaskStatus.inProgress
      // ? '任务进行中'
      // : taskDetail?.taskStatusEnum === TaskStatus.success
      // ? '任务成功'
      // : ''
    },
    {
      key: "6",
      label: "审核进度",
      children:
        taskDetail && taskDetail?.taskStatusEnum !== "FAILED" ? (
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <label>{taskDetail.reviewCount + " / " + taskDetail.qaCount}</label>
            <Tooltip title="为保证导出数据质量，请在导出前进行人工审核">
              <img src={infoIcon} style={{ width: "16px", height: "16px" }} />
            </Tooltip>
          </div>
        ) : (
          <label>-</label>
        ),
    },
  ];
  //复制
  const textToCopy = descItems[2].children;
  const textId: string = textToCopy as string;
  const [messageApi, contextHolder] = message.useMessage();
  const info = () => {
    messageApi.info("复制成功");
  };

  const columns: ColumnsType<ProgressAllInfo> = [
    {
      title: "成员",
      dataIndex: "userName",
      render: (_, record) => {
        const userNmae = record.user.userName;
        const userAvatar = record.user.userAvatar;
        return (
          <>
            <Avatar
              style={{ display: "flex", margin: "0 0 0 1.2rem" }}
              src={avatar}
            />
            <>
              <div
                style={{ display: "flex", margin: "-1.6rem 0px 0px 4.7rem" }}
              >
                {userNmae}
              </div>
            </>
          </>
        );
      },
      width: "20.6%",
      align: "center",
    },
    {
      title: "审核数量",
      dataIndex: "number",
      render: (_, { curAllocated, curReviewed }) => {
        return selectedValue === options[0].value ? curAllocated : curReviewed;
      },
      width: "21.6%",
      align: "center",
    },
    {
      title: "比例",
      dataIndex: "reviewCount",
      width: "10.6%",
      align: "center",
      render: (_, { totalProgress, curProgress }) => {
        return selectedValue === options[0].value
          ? (totalProgress * 100).toFixed(2) + "%"
          : (curProgress * 100).toFixed(2) + "%";
      },
    },
    {
      title: " ",
      dataIndex: "dataprocess",
      render: (_, { totalProgress, curProgress }) => {
        return selectedValue === options[0].value ? (
          <Progress
            strokeLinecap="butt"
            showInfo={false}
            size={[220, 15]}
            percent={totalProgress * 100}
          />
        ) : (
          <Progress
            strokeLinecap="butt"
            showInfo={false}
            size={[220, 15]}
            percent={curProgress * 100}
          />
        );
      },
      width: "27.3%",
    },
  ];

  function getProgress(taskId: string) {
    const fetchData = async () => {
      try {
        const res = await queryUserDetail(taskId);
        setApiFlg(false);
        const data = res.data;
        //setProgressDetail(([data] as unknown) as AlluserInfoType[]);
        setProgressDetail(
          Array.isArray(data) ? (data as AlluserInfoType[]) : undefined
        );
      } catch (e) {}
    };
    if (task_id) {
      fetchData();
    }
  }
  useEffect(() => {
    progressNoRef.current = progressDetail;
  });

  const getProgressDetial = async () => {
    if (task_id) {
      await getProgress(task_id);
      await ProgressInfo(task_id);
    }
  };
  useEffect(() => {
    getProgressDetial();
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current); // 在组件卸载时清除定时器
    };
  }, []);

  useEffect(() => {
    clearInterval(intervalRef.current);
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current); // 在组件卸载时清除定时器
    };
  }, [
    pagination.page,
    pagination.size,
    filterAttribute,
    filterTaskName,
    progressDetail,
  ]);

  return (
    <>
      <Modal
        centered
        className="preview-folder-modal"
        title="审核进度"
        keyboard={false}
        maskClosable={false}
        styles={{ body: { height: "528px" } }}
        width={"870px"}
        open={visible}
        onOk={onOk}
        onCancel={onCancel}
        footer={[]}
      >
        <div className="task-detail-info">
          <div
            style={{
              width: "100%",
              textAlign: "start",
              justifyContent: "space-between",
              display: "inline-flex",
              marginBottom: "1rem",
              alignItems: "center",
            }}
          ></div>

          <div
            style={{
              color: "#0FB698",
              position: "absolute",
              left: "18rem",
              top: "7.4rem",
            }}
          >
            {/* <div className="taskid-copy-img1-1" />
            <div className="taskid-copy-img2-1" />
            复制 */}
            {contextHolder}
            <Button
              type="link"
              size="small"
              onClick={() => {
                info();
                copyToClipboard(textId);
              }}
              style={{
                position: "relative",
                left: "-4rem",
                width: "5rem",
              }}
            ></Button>
          </div>
          <div className="line"></div>
          <Descriptions
            column={2}
            style={{
              padding: "2rem 1rem ",
              width: "50rem",
            }}
            size="small"
            items={descItems}
          />
        </div>
        <div style={{ color: "#6D7279" }} className="progress-process">
          进度进程明细
        </div>
        {/* <div>{userRole}</div> */}
        {/* <Scrollbar> */}
        <Table
          virtual
          locale={{
            emptyText: (
              <Empty
                image={emptyLogo}
                description={
                  <span className="dataset-table-empty-label">
                    空空如也，去上传本地文件吧~
                  </span>
                }
              />
            ),
          }}
          style={{ flex: 1 }}
          tableLayout={"fixed"}
          rowKey="taskId"
          className="dataset-table"
          columns={columns}
          dataSource={progressInfo}
          pagination={false}
          scroll={{ y: 250 }}
        />
        {/* </Scrollbar> */}
        {/* <Select
          defaultValue="切换为总数量"
          style={{
            width: 130,
            position: "absolute",
            top: "19rem",
            right: "11rem",
          }}
          bordered={false}
          onChange={handleChange}
          options={options}
        /> */}
      </Modal>
    </>
  );
};

export default CheckProgress;

/* eslint-disable react-hooks/rules-of-hooks */
import {
  Badge,
  Button,
  Card,
  Spin,
  Col,
  Collapse,
  CollapseProps,
  List,
  Modal,
  Row,
  Space,
  Switch,
  TreeSelect,
  message,
} from "antd";
import {  useLocation, useNavigate, useParams } from "react-router-dom";
import Icon, {
  LeftOutlined,
  
  ExclamationCircleFilled,
} from "@ant-design/icons";
import { useEffect, useRef, useState } from "react";
import TextArea from "antd/es/input/TextArea";
import {
  AllocatedQA,
  DocumentContentPairType,
  FileContent,
  HighlightIdx,
  QADocument,
  TaskDetailType,
} from "../../types";
import Scrollbar from "react-scrollbars-custom";
import fileaddition from "../../assets/img/fileaddition.svg";
import HighlightedText from "../../components/HighlightedText";
import {
  QaDeleteInfo,
  QaUpdateParams,
  deleteQA,
  updateQ<PERSON>,
  getQAList,
  getFileContent,
  getDeallocate,
} from "../../api/qa";
import {
  flattenFileTree,
  getQATaskFileTree,
  getReviewConfigInfo,
  getTaskDetail,
  getTaskIsSetReviewConfig,
} from "../../api/task";

import { DataNode } from "antd/es/tree";
import {
  NormalReviewParams,
  ScoreReviewParams,
  getPermissions,
  normalReview,
  queryQaAllocated,
  scoreReview,
} from "../../api/review";
import Scrollbars from "react-custom-scrollbars";
import DeleteQAConfirmModal from "../../components/DeleteQAConfirmModal";
import { ReviewConfigBtnType } from "../../components/ReviewConfigModal/components/ReviewConfigBtn/type";
import { svgMap } from "../../components/ReviewConfigModal/components/ReviewConfigBtn";

const DefaultReviewView: React.FC = () => {
  const [showQuestionList, setShowQuestionList] = useState(true);
  const [question, setQuestion] = useState<QADocument>(
    new Object() as QADocument
  );
  const { confirm } = Modal;
  const [currentQues, setCurrentQues] = useState<string>("");
  const [currentAnsw, setCurrentAnsw] = useState<string>("");
  const [reviewId, setReviewId] = useState("");
  // const [sourceData, setSourceData] = useState<FileContent>();
  const [highlightIdxList, setHighlightIdxList] = useState<HighlightIdx[]>([]);

  const navigate = useNavigate();
  const location = useLocation();
  const [displayQaList, setDisplayQaList] = useState<QADocument[]>([]);
  const [allQaList, setAllQaList] = useState(0);

  const [editQuestion, setEditQuestion] = useState<boolean>(false);
  const [editAnswer, setEditAnswer] = useState<boolean>(false);
  // const [editAnswer, setEditAnswer] = useState<boolean>(false);
  // const questionRef = useRef<HTMLDivElement>(null);
  // const answerRef = useRef<HTMLDivElement>(null);
  const [treeSelectValue, setTreeSelectValue] = useState<string>();
  const { task_id } = useParams<string>();
  const [pairs, setPairs] = useState<DocumentContentPairType[]>([]);
  const [fileContent, setFileContent] = useState<FileContent[]>([]);
  const [taskDetail, setTaskDetail] = useState<TaskDetailType>(
    new Object() as TaskDetailType
  );
  const [qaFileMap, setQaFileMap] = useState<Map<string, string>>(
    new Map<string, string>()
  );
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  // const [ignoreConfirm, setIgnoreConfirm] = useState<boolean>(false);
  const [ignoreConfirm, setIgnoreConfirm] = useState<boolean>(false);
  const [ignoreConfirmModal, setIgnoreConfirmModal] = useState<boolean>(false);

  const [score, setScore] = useState<string>("");
  const [activeKey, setActiveKey] = useState<string[] | string>("");
  const [autoSwitch, setAutoSwitch] = useState(false);
  const [isConfirm, setIsConfirm] = useState(false);
  const [confirmBtnLoading, setConfirmBtnLoading] = useState(false);
  const [fileList, setFileList] = useState<string[]>([]);
  const [qaContent, setQaContent] = useState<string>();
  const [fileTree, setFileTree] = useState<any[]>([]);
  const [flattenTree, setFlattenTree] = useState<any[]>([]);
  const [scoreButtonInfo, setScoreButtonInfo] = useState<ReviewConfigBtnType[]>(
    []
  );
  // 审核中
  const [isReviewing, setIsReviewing] = useState<boolean>(false);
  const [qaAllont, setQaAllont] = useState<AllocatedQA[]>([]);
  const collapseItems: CollapseProps["items"] = [
    {
      key: "1",
      label: (
        <label style={{ color: "#6D7279", fontSize: "14px" }}>评价标准</label>
      ),
      children: (
        <TextArea
          // value={question.question}
          placeholder="请将生成数据的字符总长度、语言自然度作为主要指标进行主观打分评价"
          style={{ resize: "none", height: 100, border: "none" }}
        />
      ),
    },
  ];

  const handleModalOk = () => {
    navigate(-1);
  };

  if (!task_id) {
    navigate(`/main/task`);
    return null;
  }

  useEffect(() => {
    getTaskIsSetReviewConfig(task_id).then((p1) => {
      if (p1.data?.code === 200) {
    // 配置过审核配置才允许审核，否则跳转到任务详情
    const userId = sessionStorage.getItem("id");
    if (task_id && userId) {
      dataQA(task_id, userId);
    }
    onGetTaskDetail(task_id);
    // setScoreButtonInfo(DefaultBtnConfig);
    getReviewConfigInfo(task_id).then((res) => {
      if (res?.data?.code === 200) {
        setScoreButtonInfo(res.data.data?.scoreButtonInfo);
      }
    });
    }     });
  }, []);

  const shouldShowReminder = () => {
    const ignoreConfirm = localStorage.getItem("ignoreConfirm");
    if (ignoreConfirm) {
      const lastRemindedDate = localStorage.getItem("lastRemindedDate");
      const today = new Date().toLocaleDateString();
      if (lastRemindedDate !== today) {
        localStorage.setItem("lastRemindedDate", today);
        return true;
      } else {
        return false;
      }
    }
    return true;
  };

  const getBorderStyle = (item: QADocument) => {
    let color = "white";
    scoreButtonInfo?.forEach((info) => {
      if (info.value === item.score) {
        color = info.color;
      }
    });
    return `3px solid ${color}`;
  };

  useEffect(() => {
    if (autoSwitch) {
      setNextQA();
    }
  }, [displayQaList]);

  useEffect(() => {
    setCurrentQues(question.question);
    setCurrentAnsw(question.answer);
    setIsConfirm(question.review);
    setScore(question.score);
    if (fileContent) {
      const file = fileContent.filter(
        (file: { id: string }) => file.id === question.fileContentId
      )[0];
      // setSourceData(file);
      if (task_id && question.id) {
        getFileContent(task_id, question.id).then((res) => {
          if (res.data?.code === 200) {
            // setSourceData(res.data.data.content);
            setQaContent(res.data.data.content);
          }
        });
        setHighlightIdxList(question.highlightIdxList);
      }
    }
  }, [question]);

  const setNextQA = () => {
    setNextQAWithList(displayQaList);
  };

  const setNextQAWithList = (qaList: QADocument[]) => {
    let qaIndex = 0;
    let qaItem: QADocument | undefined;

    if (!question?.id) {
      // 初始化时取第一个未审核的QA
      qaIndex = qaList.findIndex((qa: QADocument) => !qa.review);
      // 全审核完选取第一个QA
      qaItem = qaIndex === -1 ? qaList[0] : qaList[qaIndex];
    } else {
      // 刷新时，取上一个QA之后的第一个未审核的QA
      // 如果是最后一条，就从头查找
      const currentIndex = qaList.findIndex((qa) => qa.id === reviewId);

      if (currentIndex === -1) {
        // 如果当前reviewId在列表中找不到，可能是因为数据更新了
        // 直接找第一个未审核的QA
        qaIndex = qaList.findIndex((qa: QADocument) => !qa.review);
        qaItem = qaIndex === -1 ? qaList[0] : qaList[qaIndex];
      } else if (currentIndex !== qaList.length - 1) {
        // 从当前位置的下一个开始查找未审核的QA
        qaItem = qaList
          .slice(currentIndex, qaList.length)
          .find((qa: QADocument) => !qa.review);

        // 如果后面没有未审核的，从头开始找
        if (!qaItem) {
          qaItem = qaList.find((qa: QADocument) => !qa.review);
        }
      } else {
        // 当前是最后一条，从头查找
        qaItem = qaList.find((qa: QADocument) => !qa.review);
      }
    }

    if (qaItem) {
      setReviewId(qaItem.id);
      setQuestion(qaItem);
      setCurrentQues(qaItem.question);
      setCurrentAnsw(qaItem.answer);

      const file = fileContent.filter(
        (file: { id: string }) => file.id === qaItem?.fileContentId
      )[0];
      // setSourceData(file);
      if (task_id && qaItem.id) {
        getFileContent(task_id, qaItem.id).then((res) => {
          if (res.data?.code === 200) {
            setQaContent(res.data.data.content);
          }
        });
        setHighlightIdxList(qaItem.highlightIdxList);
      }
    } else {
      console.log('没有找到合适的QA');
    }
  };

  const onTreeSelectChange = (value: any, node: any) => {
    let updateQAList: QADocument[] = [];
    if (!node.children || node.children.length === 0) {
      // 选中子节点
      const docuList = flattenTree.filter((qa) => qa.fileId === node.fileId);
      setFileList(docuList.map((item) => item.fileId));
    } else {
      // 选中父节点
      const children = fileTree.filter(
        (item) => item.fileTreeNodeForTask.fileId === node.fileId
      );
      if (children && children.length > 0)
        setFileList(
          children[0].fileTreeNodeForTask.children.map(
            (item: { fileId: any }) => item.fileId
          )
        );
    }
  };

  function getAllChildren(node: any, result: any[] = []): any[] {
    // 递归遍历所有子节点
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        getAllChildren(child, result);
      }
    } else {
      // 将当前节点添加到结果数组
      result.push(node);
    }
    return result;
  }

  const getQAs = (taskId: string) => {
    const userId = sessionStorage.getItem("id");
    const qaParams = {
      taskId,
      page: 0,
      pageSize: 999,
      fileIdList: fileList && fileList.length > 0 ? fileList : undefined,
      allocateUserId: userId ?? "",
    };
    getQAList(qaParams).then((res) => {
      if (res.data?.code === 200) {
        setDisplayQaList(res.data.data.qaDocumentPage);
        setAllQaList(res.data.data.total)
      }
    });
  };

  const onRefresh = (taskId: string) => {
    getTaskDetail(taskId).then((res) => {
      if (res.data?.code === 200) {
        const data = res.data.data;
        setTaskDetail(data);

        // 先获取QA数据，然后在回调中处理自动切换
        const userId = sessionStorage.getItem("id");
        const qaParams = {
          taskId,
          page: 0,
          pageSize: 999,
          fileIdList: fileList && fileList.length > 0 ? fileList : undefined,
          allocateUserId: userId ?? "",
        };

        getQAList(qaParams).then((qaRes) => {
          if (qaRes.data?.code === 200) {
            const newDisplayQaList = qaRes.data.data.qaDocumentPage;
            setDisplayQaList(newDisplayQaList);
            setAllQaList(qaRes.data.data.total);

            // 在QA数据更新后，如果开启自动切换，则切换到下一个QA
            // if (autoSwitch) {
              // 使用新的QA列表来查找下一个QA
              setNextQAWithList(newDisplayQaList);
            // }
          }
        });
      }
    });
  };

  const onGetTaskDetail = (taskId: string) => {
    getTaskDetail(taskId).then((res) => {
      if (res.data?.code === 200) {
        const data = res.data.data;
        setTaskDetail(data);
        getQAs(taskId);

        getQATaskFileTree(taskId).then((res) => {
          if (res.data?.code === 200) {
            const data = res.data.data;
            if (data) {
              const fileTrees = data.map((item, index) => {
                item.fileTreeNodeForTask.fileId = index.toString();
                return item.fileTreeNodeForTask;
              });
              const flattenTree = flattenFileTree(fileTrees);
              setFileTree(
                data.map((item, index) => {
                  item.fileTreeNodeForTask.fileId = index.toString();
                  return item;
                })
              );
              setFlattenTree(flattenTree);
              console.log(flattenTree);
              setTreeData(fileTrees as any[]);
            }
          }
        });
      }
    });
  };

  // async function fetchDatasetTrees(datasetIds: string[]) {
  //   const datasetTrees = [];
  //   if (!datasetIds) return;
  //   for (const datasetId of datasetIds) {
  //     const res = await getDataSetTreeData(datasetId);
  //     if (res.data?.code === 200) {
  //       const fileTree = res.data.data;
  //       fileTree.childFileId = datasetId;
  //       datasetTrees.push(fileTree);
  //     }
  //   }
  //   if (!treeSelectValue && datasetTrees[0].childFileId) {
  //     setTreeSelectValue(datasetTrees[0].childFileId);
  //   }
  //   setTreeData(datasetTrees as any);
  // }
  useEffect(() => {
    if (task_id) {
      getQAs(task_id);
    }
  }, [fileList]);

  useEffect(() => {
    if (displayQaList.length > 0 && !question?.id) {
    // 组件初始化，设置第一个QA
      setNextQA();
    }
  }, [displayQaList]);
  async function dataQA(taskId: string, userId: string) {
    if (taskId && userId) {
      const res = await queryQaAllocated(taskId, userId);
      if (res?.data?.code === 2000) {
        const data = res.data?.code as unknown as AllocatedQA[];
        setQaAllont(data);
      }
    }
  }

  const [clickCount, setClickCount] = useState<number>(0);

  const RateButton: React.FC = () => {
    const [showWarning, setShowWarning] = useState(false);
    let clickTimeout: string | number | NodeJS.Timeout | undefined;

    useEffect(() => {
      console.log(clickCount);
      let timeout: string | number | NodeJS.Timeout | undefined;
      if (clickCount >= 3) {
        setShowWarning(true);

        timeout = setTimeout(() => {
          setShowWarning(false);
          setClickCount(0);
        }, 3000);
      }

      return () => clearTimeout(timeout);
    }, [clickCount]);

    const handleClick = (score: string) => {
      if (clickTimeout) {
        clearTimeout(clickTimeout);
      }
      setScore(score);
      setClickCount((preCount) => preCount + 1);
      // score api
      if (autoSwitch) {
        const review: ScoreReviewParams = {
          // childFileId: qaFileMap.get(question.id) ?? "",
          id: question.id,
          taskId: taskDetail.id,
          score: score,
        };
        scoreReview(review).then((res) => {
          setIsReviewing(true);
          setTimeout(() => {
            const userId = sessionStorage.getItem("id") ?? "";
            const taskId = taskDetail.id;
            queryQaAllocated(taskId, userId).then((res) => {
              onRefresh(taskDetail.id);
              setScore("");
              setIsReviewing(false);
            });
          }, 500);
        });
        clickTimeout = setTimeout(() => {
          setClickCount(0);
        }, 3000);
      }
    };

    return (
      <Space
        size={4}
        direction="vertical"
        style={{
          textAlign: "center",
          position: "relative",
          top: "-18px",
        }}
      >
        {/* <span style={{ color: 'red', visibility: showWarning ? 'visible' : 'hidden' }}>操作过快，请认真审核</span>, display: showWarning ? '' : 'none' }*/}
        <div style={{ height: 22 }}>
          <span
            style={{ color: "red", display: showWarning ? "" : "none" }}
            className="shake-text"
          >
            操作过快，请认真审核
          </span>
        </div>
        <Space>
          {scoreButtonInfo?.map((btn,index) => {
            return (
              <Button
              key={index}
                disabled={showWarning || !isConfirm}
                type="primary"
                style={
                  {
                    width: "120px",
                    background: `${score === btn.value ? btn.color : ""}`,
                    "--btn-hover-color": `${btn.value ? btn.color : "red"}`,
                  } as React.CSSProperties
                }
                className={"review-btn-default boldText"}
                onClick={() => {
                  handleClick(btn.value);
                }}
              >
                <Icon component={svgMap(btn.icon)} />
                <span className="text-spacing">{btn.value}</span>
              </Button>
            );
          })}
          {/* <Button
            disabled={showWarning || !isConfirm}
            type="primary"
            className={
              score === "Bad"
                ? "bad-active review-btn-default boldText"
                : "review-btn-default boldText"
            }
            style={{ width: 120 }}
            onClick={() => {
              handleClick("Bad");
            }}
          >
            <DislikeOutlined />
            较差
          </Button>
          <Button
            disabled={showWarning || !isConfirm}
            type="primary"
            className={
              score === "Normal"
                ? "normal-active review-btn-default boldText"
                : "review-btn-default boldText"
            }
            style={{ width: 120 }}
            onClick={() => {
              handleClick("Normal");
            }}
          >
            <CheckCircleOutlined />
            一般
          </Button>
          <Button
            disabled={showWarning || !isConfirm}
            type="primary"
            className={
              score === "Great"
                ? "great-active review-btn-default boldText"
                : "review-btn-default boldText"
            }
            style={{ width: 120 }}
            onClick={() => {
              handleClick("Great");
            }}
          >
            <LikeOutlined />
            良好
          </Button> */}
        </Space>
      </Space>
    );
  };

  const [deallocate, setDeallocate] = useState(false);
  const getIsDeallocate = () => {
    const userId = sessionStorage.getItem("id");
    if (task_id && userId) {
      getDeallocate(task_id, userId!).then(() => {
        setDeallocate(true);
      });
    }
  };

  return (
    <Scrollbar>
      <div className="createTaskContent" style={{ width: "90.3%" }}>
        <div
          style={{
            display: "inline-flex",
            alignItems: "center",
            justifyContent: "space-between",
            width: "100%",
            marginBottom: "24px",
          }}
        >
          <Space size={20}>
            <Button
              style={{ fontSize: "12px", width: "36px", height: "36px" }}
              shape="circle"
              icon={<LeftOutlined />}
              onClick={() => {
                const taskModal = confirm({
                  centered: true,
                  title: "保留提示",
                  icon: <ExclamationCircleFilled />,
                  width: 540,
                  content: (
                    <>
                      <div className="default-info" style={{ color: "black" }}>
                        当前任务未全部审核完成，是否保留？
                      </div>
                    </>
                  ),
                  footer: [
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "flex-end",
                        padding: "2rem 0 0 0",
                        gap: "8px",
                      }}
                    >
                      <Button
                        type="text"
                        // onClick={() => {
                        //   getIsDeallocate();

                        //   navigate(-1);
                        // }}
                        onClick={() => {
                          getIsDeallocate();
                          taskModal.destroy();
                          setTimeout(() => {
                            const navigateBack = location.state?.fromButton ? -2 : -1;
                            navigate(navigateBack);
                          }, 1000);
                        }}
                        shape="round"
                      >
                        否
                      </Button>
                      <Button
                        type="primary"
                        className="primary-btn"
                        style={{ width: "120px" }}
                        onClick={() => {
                          taskModal.destroy();
                          setTimeout(() => {
                            const navigateBack = location.state?.fromButton ? -2 : -1;
                            navigate(navigateBack);
                          }, 1000);
                        }}
                        shape="round"
                      >
                        是
                      </Button>
                    </div>,
                  ],
                  onOk() {
                    handleModalOk();
                  },
                  onCancel() {
                    handleModalOk();
                  },
                });
                //navigate(-1)
              }}
            />
            <div
              style={{
                fontSize: "28px",
                lineHeight: "36px",
                fontWeight: "500",
              }}
              className="mediumText"
            >
              {taskDetail.taskName}
            </div>
            <div className="review-tag boldText">人工审核</div>
          </Space>
        </div>
        <div
          className="createTaskArea"
          style={{ padding: "41px 37px", margin: 0 }}
        >
          <div
            style={{
              display: "inline-flex",
              height: "808px",
            }}
          >
            <div className="default-review-lf">
              <Card
                title={
                  <div className="boldText" style={{ fontSize: 14 }}>
                    原文对照
                  </div>
                }
                headStyle={{
                  height: "52px",
                  fontWeight: 500,
                  fontFamily: "HarmonyOS Sans SC Medium",
                  minHeight: "unset",
                }}
                bordered={false}
                style={{ width: "100%", height: "95%", textAlign: "start" }}
                bodyStyle={{ padding: "20px 24px" }}
              >
                {qaContent ? (
                  <>
                    {/* <div
                      className="mediumText"
                      style={{
                        fontWeight: 500,
                        display: "flex",
                        gap: 8,
                        alignItems: "center",
                      }}
                    >
                      <img src={fileaddition} width={16} height={16} />
                      {sourceData?.fileName}
                    </div> */}
                    <div
                      style={{
                        height: "680px",
                        textAlign: "start",
                        marginTop: "16px",
                        lineHeight: "26px",
                        color: "#6D7279",
                        overflowWrap: "break-word",
                        whiteSpace: "pre-line",
                        width: "100%",
                        padding: "5px 10px",
                      }}
                    >
                      <Scrollbars
                        autoHide
                        autoHideTimeout={1000}
                        autoHideDuration={200}
                      >
                        <HighlightedText
                          text={qaContent}
                          highlights={highlightIdxList}
                        />
                      </Scrollbars>
                    </div>
                  </>
                ) : null}
              </Card>
              <div className="taskDetailBottom">
                溯源置信度 : &nbsp;
                <span className="span1"> 示例</span>
                {"≥"}90%&nbsp;&nbsp;&nbsp;&nbsp;
                <span className="span2"> 示例</span>
                {"≥"}80%&nbsp;&nbsp;&nbsp;&nbsp;
                <span className="span3"> 示例</span>
                {"≥"}70%
              </div>
            </div>
            <div className="default-review-rg">
              <Card
                bordered={false}
                size="small"
                headStyle={{ height: "52px" }}
                title={
                  <div className="boldText" style={{ textAlign: "start" }}>
                    人工审核
                    {/* <Space>
                      <label
                        className="reqularText"
                        style={{ fontWeight: 400 }}
                      >
                        显示问题列表
                      </label>
                      <Switch
                        size="default"
                        checked={showQuestionList}
                        onChange={setShowQuestionList}
                      />
                    </Space> */}
                  </div>
                }
                extra={
                  <Space>
                    <Badge color="#0FB698" />
                    <label style={{ color: "#6D7279" }}>
                      已审核：
                      <label style={{ color: "#111111" }} className="boldText">
                        {taskDetail.reviewCount ?? 0 + " "}
                      </label>
                      个,
                    </label>
                    <label style={{ color: "#6D7279" }}>
                      剩余：
                      <label style={{ color: "#111111" }} className="boldText">
                        {allQaList -
                          (taskDetail.reviewCount ?? 0) +
                          " "}
                      </label>
                      个
                    </label>
                  </Space>
                }
                style={{ width: "100%", border: "unset" }}
                bodyStyle={{ height: 714 }}
              >
                <div style={{ display: "flex" }}>
                  <Space
                    direction="vertical"
                    style={{ width: "100%", flex: "1", padding: "0 1rem" }}
                    size={18}
                  >
                    <div style={{ color: "#6D7279" }}>
                      <span
                        className="boldText"
                        style={{ fontSize: 16, marginRight: 32 }}
                      >
                        Step 1
                      </span>
                    </div>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                      className="boldText"
                    >
                      问题：
                      {/* <Button
                          // disabled={question.review}
                          type="link"
                          onClick={() => {
                            if (!editQuestion) {
                              // 更新QA
                              const params: QaUpdateParams = {
                                answer: currentAnsw,
                                childFileId: qaFileMap.get(question.id) ?? '',
                                id: question.id,
                                question: currentQues,
                                taskId: taskDetail.id
                              }
                              updateQA(params).then(res => {
                                if (res.data.code === 200 && taskDetail) {
                                  onRefresh(taskDetail.id);
                                }
                              })
                            }
                            setEditQuestion(!editQuestion);
                          }}
                        >
                          {editQuestion ? '编辑' : '完成编辑'}
                        </Button> */}
                    </div>
                    <div style={{ color: "#6D7279" }}>
                      请审核生成的问题和答案内容是否正确，如不正确请对其进行编辑和修改，也可以将其删除
                    </div>
                    <div style={{ position: "relative" }}>
                      <TextArea
                        className="review-textarea"
                        // ref={questionRef}
                        // disabled={editQuestion}
                        // rows={3}
                        value={currentQues}
                        style={{ resize: "none", height: 60 }}
                        // onFocus={() => { setEditQuestion(true) }}
                        onChange={(e) => {
                          if (!editQuestion) {
                            setEditQuestion(true);
                          }
                          setIsConfirm(false);
                          setCurrentQues(e.target.value);
                        }}
                      />
                      <div
                        style={{
                          position: "absolute",
                          right: "0px",
                          visibility: editQuestion ? "inherit" : "hidden",
                        }}
                      >
                        <Space>
                          <Button
                            type="link"
                            style={{ padding: "4px 0" }}
                            onClick={() => {
                              // if (!editQuestion) {
                              // 更新QA
                              const params: QaUpdateParams = {
                                answer: currentAnsw,
                                // childFileId: qaFileMap.get(question.id) ?? "",
                                id: question.id,
                                question: currentQues,
                                taskId: taskDetail.id,
                              };
                              updateQA(params).then((res) => {
                                if (res.data?.code === 200 && taskDetail) {
                                  // onRefresh(taskDetail.id);
                                  onGetTaskDetail(taskDetail.id);
                                }
                              });
                              setEditQuestion(false);
                              // }
                              // setEditQuestion(!editQuestion);
                            }}
                          >
                            保存
                          </Button>
                          <Button
                            type="link"
                            style={{ padding: "4px 0" }}
                            onClick={() => {
                              setCurrentQues(question.question);
                              setEditQuestion(false);
                            }}
                          >
                            撤销
                          </Button>
                        </Space>
                      </div>
                    </div>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                      className="boldText"
                    >
                      <label>回答：</label>
                      {/* <Button
                        // disabled={question.review}
                        type="link"
                        onClick={() => {
                          if (!editAnswer) {
                            // const updateQaLit = sourceQaList;
                            // const updateQA = question;
                            // updateQA.answer = currentAnsw ?? '';
                            // const updateIndex = sourceQaList.findIndex(qa => qa.id === reviewId);
                            // Object.assign(updateQaLit[updateIndex], updateQA);
                            // setSourceQaList(updateQaLit);
                            const params: QaUpdateParams = {
                              answer: currentAnsw,
                              childFileId: qaFileMap.get(question.id) ?? '',
                              id: question.id,
                              question: currentQues,
                              taskId: taskDetail.id
                            }
                            updateQA(params).then(res => {
                              if (res.data.code === 200 && taskDetail) {
                                onRefresh(taskDetail.id);
                              }
                            })
                          }
                          setEditAnswer(!editAnswer);
                        }}
                      >
                        {editAnswer ? '编辑' : '完成编辑'}
                      </Button> */}
                    </div>
                    <div style={{ color: "#6D7279" }}>
                      请审核生成的问题和答案内容是否正确，如不正确请对其进行编辑和修改，也可以将其删除
                    </div>
                    <div style={{ position: "relative" }}>
                      <TextArea
                        className="review-textarea"
                        // ref={answerRef}
                        // disabled={editAnswer}
                        // rows={9}
                        value={currentAnsw}
                        style={{ resize: "none", height: 148 }}
                        // onFocus={() => { setEditQuestion(true) }}
                        onChange={(e) => {
                          if (!editAnswer) {
                            setEditAnswer(true);
                          }
                          setIsConfirm(false);
                          setCurrentAnsw(e.target.value);
                        }}
                      />
                      <div
                        style={{
                          position: "absolute",
                          right: "0px",
                          visibility: editAnswer ? "inherit" : "hidden",
                        }}
                      >
                        <Space>
                          <Button
                            type="link"
                            style={{ padding: "4px 0" }}
                            onClick={() => {
                              // if (!editQuestion) {
                              // 更新QA
                              const params: QaUpdateParams = {
                                answer: currentAnsw,
                                // childFileId: qaFileMap.get(question.id) ?? "",
                                id: question.id,
                                question: currentQues,
                                taskId: taskDetail.id,
                              };
                              updateQA(params).then((res) => {
                                if (res.data?.code === 200 && taskDetail) {
                                  onGetTaskDetail(taskDetail.id);
                                }
                              });
                              setEditAnswer(false);
                              // }
                              // setEditQuestion(!editQuestion);
                            }}
                          >
                            保存
                          </Button>
                          <Button
                            type="link"
                            style={{ padding: "4px 0" }}
                            onClick={() => {
                              setCurrentAnsw(question.answer);
                              setEditAnswer(false);
                            }}
                          >
                            撤销
                          </Button>
                        </Space>
                      </div>
                    </div>
                    <Space size={19}>
                      <Button
                        type="primary"
                        className={
                          isConfirm
                            ? "boldText review-btn-confirm"
                            : "boldText review-btn-default"
                        }
                        style={{ width: 120 }}
                        loading={confirmBtnLoading}
                        onClick={() => {
                          // setConfirmBtnLoading(true);
                          // const review: ScoreReviewParams = {
                          //   childFileId: qaFileMap.get(question.id) ?? "",
                          //   id: question.id,
                          //   taskId: taskDetail.id,
                          //   score: "1",
                          // };
                          // scoreReview(review).then((res) => {
                          // normalReview(review).then((res) => {
                          //   setConfirmBtnLoading(false);
                          //   if (res.data?.code === 200) {
                          //     setIsConfirm(true);
                          //   }
                          // });
                          setIsConfirm(true);
                        }}
                      >
                        确认无误
                      </Button>
                      <Button
                        type="primary"
                        className={"boldText review-btn-delete"}
                        style={{ width: 120 }}
                        onClick={() => {
                          if (shouldShowReminder()) {
                            setIgnoreConfirmModal(true);
                            // const modal = confirm({
                            //   centered: true,
                            //   title: "删除提示",
                            //   icon: <ExclamationCircleFilled />,
                            //   width: 540,
                            //   content: (
                            //     <>
                            //       <div
                            //         className="default-info"
                            //         style={{ color: "black" }}
                            //       >
                            //         确定要删除本条数据吗？
                            //       </div>
                            //       <div
                            //         className="upload-error-label"
                            //         style={{ marginTop: "8px" }}
                            //       >
                            //         请注意删除数据后无法恢复。
                            //       </div>
                            //     </>
                            //   ),
                            //   footer: (
                            //     <DeleteConfirmModalFooter
                            //       onClose={() => modal.destroy()}
                            //     />
                            //   ),
                            //   onOk() {
                            //     deleteQA([
                            //       {
                            //         childFileId:
                            //           qaFileMap.get(question.id) ?? "",
                            //         ids: [question.id],
                            //         taskId: taskDetail?.id ?? "",
                            //       },
                            //     ]).then((res) => {
                            //       const updateQA = question;
                            //       updateQA.id = null as any;
                            //       setQuestion(updateQA);
                            //       // onRefresh(taskDetail.id);
                            //       onGetTaskDetail(taskDetail.id);
                            //     });
                            //   },
                            //   onCancel() {
                            //     modal.destroy();
                            //   },
                            // });
                          } else {
                            // console.log(111);
                            const params: QaDeleteInfo = {
                              qaDeleteInfoList: [
                                {
                                  fileId: qaFileMap.get(question.id) ?? "",
                                  ids: [question.id],
                                },
                              ],
                              taskId: taskDetail?.id ?? "",
                            };
                            deleteQA(params).then((res) => {
                              const updateQA = question;
                              updateQA.id = null as any;
                              setQuestion(updateQA);
                              // onRefresh(taskDetail.id);
                              onGetTaskDetail(taskDetail.id);
                            });
                          }
                        }}
                      >
                        删除本条
                      </Button>
                    </Space>
                    {/* </div> */}
                    {/* <Row>
                      <Col flex="auto"> */}
                    <div
                      className="boldText"
                      style={{ color: "#6D7279", fontSize: 16 }}
                    >
                      Step 2
                    </div>
                    <div className="boldText">打分：</div>
                    <div style={{ color: "#6D7279" }}>
                      评价标准：请将生成数据的字符总长度、语言自然度作为主要指标进行主观打分评价
                    </div>
                    <RateButton />
                    {/* </Col>
                      <Col span={showQuestionList ? 8 : 14}>
                        <Collapse
                          style={{
                            width: activeKey.includes('1') ? '100%' : 110, float: 'right', borderRadius: '8px',
                            border: '1px solid #D7DDE7'
                          }}
                          ghost={true}
                          bordered={false}
                          expandIconPosition="end"
                          size="small"
                          items={collapseItems}
                          activeKey={activeKey}
                          onChange={(e) => {
                            setActiveKey(e);
                          }}
                        /> */}
                    {/* <TextArea
                          // value={question.question}
                          prefix={'评价标准'}
                          placeholder=""
                          style={{ resize: "none", height: 120 }}
                        /> */}
                    {/* </Col>
                    </Row> */}
                  </Space>
                  <div
                    style={{
                      width: "300px",
                      // marginRight: "1.5rem",
                      padding: "0 0 1.25rem 1rem",
                      borderLeft: "1px solid #EEF1F5",
                    }}
                  >
                    <TreeSelect
                      showSearch
                      style={{
                        width: "100%",
                        height: "40px",
                        marginBottom: "20px",
                      }}
                      value={treeSelectValue}
                      dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                      allowClear
                      labelInValue
                      treeDefaultExpandAll
                      onChange={(newValue) => {
                        setTreeSelectValue(newValue);
                      }}
                      onSelect={onTreeSelectChange}
                      treeData={treeData}
                      fieldNames={{
                        label: "name",
                        value: "fileId",
                        children: "children",
                      }}
                    />
                    <Scrollbar style={{ height: 588 }}>
                      <List
                        className="qa-list"
                        size="small"
                        dataSource={displayQaList}
                        bordered={false}
                        renderItem={(item, index) => (
                          <List.Item
                            key={item.id}
                            // style={{ textAlign: "start" }}
                            style={{
                              textAlign: "start",
                              borderLeft: getBorderStyle(item),
                            }}
                            onClick={() => {
                              setQuestion(item);
                              setReviewId(item.id);
                              // if (!editQuestion) {
                              //   setEditQuestion(true);
                              // }

                              // if (!editAnswer) {
                              //   setEditAnswer(true);
                              // }
                            }}
                          >
                            <div className="review-qa-list">
                              {/* <Badge color={item.review ? "#0FB698" : "white"} /> */}
                              <label
                                className={
                                  item.id === reviewId ? "current-qa" : ""
                                }
                              >
                                {item.question}
                              </label>
                            </div>
                          </List.Item>
                        )}
                      />
                    </Scrollbar>
                  </div>
                </div>

                <div
                  style={{
                    bottom: "0.2rem",
                    textAlign: "end",
                    position: "absolute",
                    right: "1rem",
                  }}
                >
                  <Space size={16}>
                    {question.review ? (
                      <Button
                        type="primary"
                        className="review-btn-default boldText"
                        disabled
                        onClick={() => {
                          setNextQA();
                        }}
                      >
                        已审核，下一组
                      </Button>
                    ) : (
                      <Button
                        type="primary"
                        className="review-btn-default boldText"
                        disabled={!score}
                        onClick={async () => {
                          //更新QA
                          if (question.modify || !editQuestion || !editAnswer) {
                            setEditAnswer(true);
                            setEditQuestion(true);
                            const params: QaUpdateParams = {
                              answer: currentAnsw,
                              // childFileId: qaFileMap.get(question.id) ?? "",
                              id: question.id,
                              question: currentQues,
                              taskId: taskDetail.id,
                            };
                            updateQA(params).then(async (res) => {
                              setIsReviewing(true);
                              if (res.data?.code === 200 && taskDetail) {
                                const review: ScoreReviewParams = {
                                  // childFileId: qaFileMap.get(question.id) ?? "",
                                  id: question.id,
                                  taskId: taskDetail.id,
                                  score: score,
                                };
                                // await normalReview(review);
                                await scoreReview(review);
                                onRefresh(taskDetail.id);
                                // setNextQA(); // 移除这里的调用，因为 onRefresh 中已经处理了自动切换
                                setIsReviewing(false);
                              } else {
                                message.error(res.data?.message);
                                setIsReviewing(false);
                              }
                            });
                          } else {
                            setIsReviewing(true);
                            const review: ScoreReviewParams = {
                              // childFileId: qaFileMap.get(question.id) ?? "",
                              id: question.id,
                              taskId: taskDetail.id,
                              score: score,
                            };
                            await scoreReview(review);
                            // await normalReview(review);
                            onRefresh(taskDetail.id);
                            setIsReviewing(false);
                            // setNextQA(); // 移除这里的调用，因为 onRefresh 中已经处理了自动切换
                          }
                        }}
                      >
                        完成审核，下一条
                        {isReviewing && <Spin />}
                      </Button>
                    )}
                  </Space>
                </div>
              </Card>
            </div>
          </div>
          <div
            style={{
              marginTop: -31,
              display: "flex",
              justifyContent: "flex-end",
            }}
          >
            <Space>
              <Switch
                size="default"
                checked={autoSwitch}
                onChange={setAutoSwitch}
              />
              <label style={{ color: "#6D7279" }}>
                审核完成后自动切换下一条
              </label>
            </Space>
          </div>
        </div>
      </div>
      <DeleteQAConfirmModal
        visible={ignoreConfirmModal}
        OnClose={(isDelete: boolean, ignore?: boolean | undefined) => {
          setIgnoreConfirmModal(false);
          if (ignore) {
            localStorage.setItem("ignoreConfirm", "true");
            const today = new Date().toLocaleDateString();
            localStorage.setItem("lastRemindedDate", today);
          }
          if (isDelete) {
            const params: QaDeleteInfo = {
              qaDeleteInfoList: [
                {
                  fileId: qaFileMap.get(question.id) ?? "",
                  ids: [question.id],
                },
              ],
              taskId: taskDetail?.id ?? "",
            };
            deleteQA(params).then((res) => {
              const updateQA = question;
              updateQA.id = null as any;
              setQuestion(updateQA);
              onGetTaskDetail(taskDetail.id);
            });
          }
        }}
      />
    </Scrollbar>
  );
};

export default DefaultReviewView;

import{k as e,p as s,r as l,j as a,f as r,B as t,l as i,I as n,v as o,n as d,t as m}from"./react-core-BrQk45h1.js";/* empty css                        */import{b as c,f as h}from"./modle-Dr2iwiy1.js";import{i as p}from"./info-icon2-BPNrwGFd.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";import"./request-v7NtcsCU.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const x=()=>{const x=e(),[j]=s.useForm(),[u,g]=l.useState(),[y,f]=l.useState(""),[v,w]=l.useState([]);return l.useEffect((()=>{c().then((e=>{var s;if(200===(null==(s=e.data)?void 0:s.code)){const s=e.data.data.server_config;Array.isArray(s)?w(s.map((e=>({label:e.server_name,value:e.server_name,id:e.id})))):w([])}}))}),[]),a.jsxs("div",{className:"uploadBaseModel",children:[a.jsxs(r,{size:20,style:{display:"inline-flex",alignItems:"center"},children:[a.jsx(t,{shape:"circle",icon:a.jsx(i,{}),onClick:()=>x(-1),style:{fontSize:"12px",width:"36px",height:"36px"}}),a.jsx("div",{className:"mediumText",style:{fontSize:"20px",lineHeight:"36px",fontWeight:"500"},children:"新增基座模型"})]}),a.jsx("div",{className:"uploadModelArea",children:a.jsxs(s,{form:j,onFinish:async()=>{try{const e={...await j.validateFields(),serverName:u},s=await h(e);200===s.code?(m.success("上传成功"),x(-1)):m.error(s.message)}catch(e){m.error("提交失败，请检查表单")}},labelCol:{span:6},wrapperCol:{span:18,offset:2},children:[a.jsx(s.Item,{name:"modelName",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:a.jsx(n,{placeholder:"请输入模型名称",size:"large",style:{width:511}})}),a.jsxs(s.Item,{name:"modelPath",label:"模型文件路径",className:"modelUrlItem",rules:[{required:!0,message:"请输入模型文件路径"}],children:[a.jsxs(s.Item,{noStyle:!0,children:["  ",a.jsx(o,{size:"large",showSearch:!0,placeholder:"请选择服务器",value:u,onChange:e=>{var s;g(e),f(String(null==(s=null==v?void 0:v.filter((s=>s.value===e))[0])?void 0:s.id))},style:{width:511,marginBottom:"10px"},options:v})]}),a.jsxs("div",{children:[a.jsx(s.Item,{name:"modelPath",noStyle:!0,children:a.jsx(n,{disabled:!u,placeholder:"/mnt/sdb/songshunming/llms/deepseek-llm-7b-chat",size:"large",style:{width:511}})}),a.jsx(d,{title:"请按照示例输入服务器上模型文件路径",overlayStyle:{width:200},children:a.jsx("img",{src:p,style:{width:"16px",height:"16px",marginLeft:"8px"}})})]})]}),a.jsx(s.Item,{name:"modelVersion",label:"模型版本",children:a.jsx(n,{placeholder:"请输入模型版本",size:"large",style:{width:511}})}),a.jsx(s.Item,{name:"parameter",label:"模型参数量",children:a.jsx(n,{placeholder:"请输入模型参数量",size:"large",style:{width:511}})}),a.jsx(s.Item,{name:"manufacturer",label:"模型厂家",children:a.jsx(n,{placeholder:"请输入模型厂家",size:"large",style:{width:511}})}),a.jsx(s.Item,{name:"introduction",label:"模型描述",children:a.jsx(n.TextArea,{rows:4,placeholder:"请输入模型描述",size:"large",style:{width:511}})}),a.jsxs(s.Item,{wrapperCol:{offset:8},style:{marginTop:40},children:[a.jsx(t,{shape:"round",className:"cancalBut",size:"large",onClick:()=>x(-1),style:{marginRight:60},children:"取消"}),a.jsx(t,{shape:"round",type:"primary",htmlType:"submit",className:"submBut",size:"large",style:{marginLeft:60},children:"确认"})]})]})})]})};export{x as default};

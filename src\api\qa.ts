import { AxiosResponse } from "axios";
import { CustomResponseType, QADocument } from "../types";
import request from "../utils/request";

export interface QaExportParams {
  all: boolean;
  qaExportInfoList?: QAInfoType[];
  taskId: string;
  excludeInfoList?: QAInfoType[];
  fileIdList?: string[];
}

export interface QaUpdateParams {
  answer: string;
  // childFileId: string;
  id: string;
  question: string;
  taskId: string;
  tags?: string[]; // 新增：支持标签更新
}

export interface QaDeleteInfo {
  excludeInfoList?: QAInfoType[];
  fileIdList?: string[];
  qaDeleteInfoList?: QAInfoType[];
  taskId: string;
}

export interface GetQaPageParams {
  tags?: any;
  allocateUserId?: string;
  fileIdList?: string[];
  isReview?: boolean | undefined;
  keyword?: string;
  page?: number;
  pageSize?: number;
  taskId: string;
  score?: string;
}

export interface QAInfoType {
  fileId: string;
  ids: string[];
}

export interface FileContentResp extends CustomResponseType {
  data: {
    content: string;
    id: string;
  };
}

interface GetQaPageResp extends CustomResponseType {
  data: {
    qaDocumentPage: QADocument[];
    total: number;
    tagStatistics: any[]
  };
}

/**
 * 新建任务
 * @param params QA导出信息的列表
 * @returns
 */
export function exportQA(params: QaExportParams) {
  const url = `/qa/export`;
  return request.post(url, params);
}

/**
 * 删除QA
 * @param params QA删除信息的列表
 * @returns
 */

export function deleteQA(params: QaDeleteInfo) {
  return request.delete("/qa", { data: params });
}

/**
 * 更新QA
 * @param params 更新QA信息
 * @returns
 */
export function updateQA(params: QaUpdateParams) {
  const url = `/qa`;
  return request.put(url, params);
}

/**
 * 获取QA列表
 * @param params 获取QA列表参数
 * @returns
 */
export function getQAList(
  params: GetQaPageParams
): Promise<AxiosResponse<GetQaPageResp>> {
  let url = `/qa/list?taskId=${params.taskId}`;
  url = params.allocateUserId
    ? url + `&allocateUserId=${params.allocateUserId}`
    : url;
  url =
    params.fileIdList && params.fileIdList.length > 0
      ? url + `&fileIdList=${params.fileIdList.join(",")}`
      : url;
  url = params.isReview !== undefined ? url + `&isReview=${params.isReview}` : url;
  url = params.keyword ? url + `&keyword=${params.keyword}` : url;
  url = params.page ? url + `&page=${params.page}` : url;
  url = params.pageSize ? url + `&pageSize=${params.pageSize}` : url;
  url = params.score ? url + `&score=${params.score}` : url;
  url = params.tags && params.tags.length > 0 ? url + `&tags=${params.tags}` : url;
  return request.post(url);
}

/**
 * 获取原文内容
 * @param taskId 任务Id
 * @param qaId QAId
 * @returns
 */
export function getFileContent(
  taskId: string,
  qaId: string
): Promise<AxiosResponse<FileContentResp>> {
  const url = `/qa/file-content?taskId=${taskId}&qaId=${qaId}`;
  return request.get(url);
}

/**
 * 是否保留审核
 * @param taskId 任务Id
 * @param userId 用户Id
 * @returns
 */
export function getDeallocate(
  taskId: string,
  userId: string
){
  const url = `/qa/deallocate?taskId=${taskId}&userId=${userId}`;
  return request.get(url);
}


/**
 * 人工审核权限分配
 * @param taskId 任务Id
 * @param userId 用户Id
 * @returns
 */
export function getAllocateQA(
  taskId: string,
  userId: string
){
  const url = `/qa/allocate?taskId=${taskId}&userId=${userId}`;
  return request.get(url);
}

// 获取标签
export function getTaglList(taskId:string):Promise<AxiosResponse>{
  const url = `/qa/tags/statistics?taskId=${taskId}`;
  return request.get(url)
}
import { LeftOutlined, SendOutlined } from "@ant-design/icons";
import {
  Ava<PERSON>,
  Button,
  Divider,
  Flex,
  Form,
  message,
  Segmented,
  Select,
  Slider,
  Space,
  Tooltip,
} from "antd";
import { useNavigate, useParams } from "react-router-dom";
import Scrollbar from "react-scrollbars-custom";
import "../../../css/AdjustmentView.css";
import group151 from "../../../assets/img/group-151.svg";
import group310 from "../../../assets/img/group-310.svg";
import { forwardRef, useEffect, useRef, useState } from "react";
import {
  maxInfo,
  maxlenght,
  repeatition,
  repeatitionInfo,
  split,
  temperature,
  topk,
  topkInfo,
  topp,
  toppInfo,
} from "../../../utils/conts";
import { SliderMarks } from "antd/es/slider";
import TextArea from "antd/es/input/TextArea";
import { ChatMessageType, ChatType, PlanType } from "./type";
import ChatMessage from "./ChatMessage";
import {
  getModelConfig,
  getModelSets,
  GetModlesParams,
} from "../../../api/modle";
import { use } from "echarts";
import { TrainSet } from "../../training/CreateTrainingView/type";
import Cookies from 'js-cookie';
// import { EventSourcePolyfill } from "event-source-polyfill";

interface TrainSelectProp {
  modelData?: TrainSet;
}
interface CreateTaskFormType {
  taskName: string;
  fileList: string[];
  splitLevel: number;
  topk: number;
  topp: number;
  maxlen: number;
  repeat: number;
  description: string;
  domain: string;
}
const iteratSetValueMap = (testSetStr: string) => {
  const index = split.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
const topkValueMap = (testSetStr: string) => {
  const index = topkInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
const toppValueMap = (testSetStr: string) => {
  const index = toppInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
const maxLenMap = (testSetStr: string) => {
  const index = maxInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
const repeatValueMap = (testSetStr: string) => {
  const index = repeatitionInfo.indexOf(testSetStr);
  return index > -1 ? index * 25 : 0;
};
const evaluate = (label: string, input: any) => {
  const mappings: Record<string, Record<any, number>> = {
    temperature: {
      0.5: 0,
      0.7: 25,
      1.0: 50,
      1.3: 75,
      1.5: 100,
    },
    topK: {
      5: 0,
      20: 25,
      50: 50,
      100: 75,
      200: 100,
    },
    topP: {
      0.6: 0,
      0.7: 25,
      0.8: 50,
      0.9: 75,
      0.95: 100,
    },
    maxTokens: {
      50: 0,
      100: 25,
      200: 50,
      300: 75,
      500: 100,
    },
    frequencyPenalty: {
      1.0: 0,
      1.2: 25,
      1.5: 50,
      1.8: 75,
      2.0: 100,
    },
  };
  return mappings[label]?.[input] ?? null;
};

const AdjustmentView = forwardRef((prop: TrainSelectProp,ref) => {
  const { task_id } = useParams<string>();
  const navigate = useNavigate();
  const [OPTIONS, setOPTIONS] = useState<any[]>([]);
  const [selectedItems, setSelectedItems] = useState("");
  const [placeholder, setPlaceholder] = useState("请选择");
  const [modelConfig, setModelConfig] = useState<string | number>("自动配置");
  const { splitLevel, topkValue, toppValue, maxLen, repeatValue } =
    prop.modelData || {};
  const [isShow, setIsShow] = useState(false);
  const configString = sessionStorage.getItem("config");
  const config = configString ? JSON.parse(configString) : {};
  const [trainingSettings, setTrainingSettings] = useState<any>({
    splitLevel: splitLevel
      ? iteratSetValueMap(splitLevel)
      : config?.temperature
      ? evaluate("temperature", config?.temperature)
      : 50,
    topkValue: topkValue
      ? topkValueMap(topkValue)
      : config?.topK
      ? evaluate("topK", config?.topK)
      : 50,
    toppValue: toppValue
      ? toppValueMap(toppValue)
      : config?.topP
      ? evaluate("topP", config?.topP)
      : 50,
    maxLen: maxLen
      ? maxLenMap(maxLen)
      : config?.maxTokens
      ? evaluate("maxTokens", config?.maxTokens)
      : 50,
    repeatValue: repeatValue
      ? repeatValueMap(repeatValue)
      : config?.frequencyPenalty
      ? evaluate("frequencyPenalty", config?.frequencyPenalty)
      : 50,
  });
  const [chatList, setChatList] = useState<ChatType[]>([]);
  const [chatType, setChatType] = useState<string>("chat");
  const [curChat, setCurChat] = useState<ChatType>();
  const [messageList, setMessageList] = useState<ChatMessageType[]>([]);
  const [displayPlanList, setDisplayPlanList] = useState<PlanType[]>();
  const [planList, setPlanList] = useState<PlanType[]>();
  const [messageInput, setMessageInput] = useState<string>("");
  const [textArray, setTextArray] = useState<string>("");
  const scrollbarRef = useRef(null);
  const [historyMessages, setHistoryMessages] = useState<ChatMessageType[]>([]);
  const [isLocked, setIsLocked] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  let typeWritterStr = "";
  const [typeWritterMessage, setTypeWritterMessage] = useState<string>("");
  let fetchSource;
  const getChatStream = (payload: any) => {
    const headers: any = {
      "Content-Type": "application/json",
      "ACCESS-KEY": "xxxx",
    };

    // try {
    //   fetchSource = fetchEventSource(
    //     `${process.env.BASE_API}/dialogue`,
    //     {
    //       method: 'POST',
    //       headers,
    //       // signal: this.ctrl.signal,
    //       openWhenHidden: true,
    //       body: JSON.stringify(payload),
    //       async onopen(response) {
    //         console.log(response);
    //       },

    //       async onmessage(event) {
    //         if (event) {
    //           if (event.data !== "") {
    //             const data = JSON.parse(event.data);
    //             if (data.flag == "add") {
    //               // console.log(data.data);
    //               // typeWritterMessage += data.data;
    //               setTypeWritterMessage(pre => pre + data.data)
    //               typeWritterStr += data.data;
    //               scrollChatToBottom();
    //             } else if (data.flag == "finish") {
    //               if (!curChat) {
    //                 getDialogueList({
    //                   user_id: '1',
    //                   page: 1,
    //                   size: 999,
    //                 }).then((res) => {
    //                   if (res?.data) {
    //                     setChatList(res.data.data);
    //                     setCurChat(res.data.data[0]);
    //                     setTextArray(typeWritterStr);
    //                     setTypeWritterMessage('');
    //                     typeWritterStr = '';
    //                   }
    //                 })
    //               } else {
    //                 setTextArray(typeWritterStr);
    //                 setTypeWritterMessage('');
    //                 typeWritterStr = '';
    //               }
    //             }
    //           }
    //         }
    //       },

    //       async onerror(error) {
    //         console.error('Error:', error);
    //       },

    //       async onclose() {
    //         console.log('onclose');
    //       },
    //     }
    //   );
    // } catch (err: any) {
    //   console.log(err);
    // }
  };

  const scrollChatToBottom = () => {
    if (scrollbarRef.current) {
      (scrollbarRef.current as any).scrollToBottom();
    }
  };

  const onSendMessage = async () => {
    if (isLocked) {
      console.log("等待回答完成后再发送新消息！");
      return;
    }
    if (!messageInput.trim()) return;

    setIsLocked(true); // 锁定发送功能

    const token = Cookies.get('token');
    const params = {
      modelId: selectedItems,
      temperature: split[trainingSettings.splitLevel / 25],
      topK: topkInfo[trainingSettings.topkValue / 25],
      topP: toppInfo[trainingSettings.toppValue / 25],
      maxTokens: maxInfo[trainingSettings.maxLen / 25],
      frequencyPenalty: repeatitionInfo[trainingSettings.repeatValue / 25],
      question: messageInput,
    };

    // 添加用户消息到消息列表
    setMessageList((prev) => [
      ...prev,
      { role: "user", content: messageInput },
    ]);

    const response = await fetch(`/api/fine-tuning/model/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "ACCESS-KEY": token || "",
      },
      body: JSON.stringify(params),
    });

    if (!response.body) {
      console.error("Response body is null");
      setIsLocked(false);
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      // 解码接收到的字节流
      buffer += decoder.decode(value, { stream: true });

      // 按行分割，处理每一行
      const lines = buffer.split("\n");
      // 保留最后一个未完成的行片段
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (line.startsWith("data:")) {
          const content = line.replace("data:", "").trim();

          // 更新消息列表
          setMessageList((prev) => {
            const lastMessage = prev[prev.length - 1];
            if (lastMessage?.role === "ai") {
              lastMessage.content += content; // 拼接消息
              return [...prev];
            } else {
              return [...prev, { role: "ai", content: content }];
            }
          });
        }
      }
    }

    // 清空输入框
    setIsLocked(false);
    setMessageInput("");
  };

  const handleNewConversation = () => {
    if (messageList.length > 0) {
      // 将当前对话添加到历史对话列表
      setHistoryMessages([...historyMessages, ...messageList]);
      // 清空当前对话
      setMessageList([]);
    }
  };
  const saveSet = () => {
    if (selectedItems) {
      setIsShow(false);
      const params = {
        modelId: selectedItems,
        splitLevel: split[trainingSettings.splitLevel / 25],
        topkValue: topkInfo[trainingSettings.topkValue / 25],
        toppValue: toppInfo[trainingSettings.toppValue / 25],
        maxLen: maxInfo[trainingSettings.maxLen / 25],
        repeatValue: repeatitionInfo[trainingSettings.repeatValue / 25],
      };
      message.success("保存配置成功");
    } else {
      setIsShow(true);
    }
  };
  const getModel = () => {
    const params: GetModlesParams = {
      page: 1,
      size: 100,
      category: 1,
      sortAttribute: "modelName",
      sortDirection: "asc",
      modelName: "",
      status: "7",
      modelCategory: "FINE_TUNING",
    };
    getModelSets(params).then((res) => {
      if (res.data?.code === 200) {
        const options = res.data.data.map((item) => ({
          value: item.id,
          label: item.modelName,
        }));
        setOPTIONS(options);
        const defaultItem = options.find(
          (item) => String(item.value) === task_id
        );
        if (defaultItem) {
          setPlaceholder(defaultItem.label);
          setSelectedItems(defaultItem.value); // 默认存储匹配的 id
        }
      }
    });
  };
  useEffect(() => {
    scrollChatToBottom();
  }, [messageList]);
  useEffect(() => {
    getModel();
  }, []);
  const temperatureMarks: SliderMarks = {
    0: (
      <>
        <div style={{ color: "#8E98A7" }}>{split[0]}</div>
        <Tooltip title={temperature[0]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    25: (
      <>
        <div style={{ color: "#8E98A7" }}>{split[1]}</div>
        <Tooltip title={temperature[1]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    50: (
      <>
        <div style={{ color: "#8E98A7" }}>{split[2]}</div>
        <Tooltip title={temperature[2]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    75: (
      <>
        <div style={{ color: "#8E98A7" }}>{split[3]}</div>
        <Tooltip title={temperature[3]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    100: (
      <>
        <div style={{ color: "#8E98A7" }}>{split[4]}</div>
        <Tooltip title={temperature[4]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
  };
  const topkMarks: SliderMarks = {
    0: (
      <>
        <div style={{ color: "#8E98A7" }}>{topkInfo[0]}</div>
        <Tooltip title={topk[0]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    25: (
      <>
        <div style={{ color: "#8E98A7" }}>{topkInfo[1]}</div>
        <Tooltip title={topk[1]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    50: (
      <>
        <div style={{ color: "#8E98A7" }}>{topkInfo[2]}</div>
        <Tooltip title={topk[2]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    75: (
      <>
        <div style={{ color: "#8E98A7" }}>{topkInfo[3]}</div>
        <Tooltip title={topk[3]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    100: (
      <>
        <div style={{ color: "#8E98A7" }}>{topkInfo[4]}</div>
        <Tooltip title={topk[4]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
  };
  const toppMarks: SliderMarks = {
    0: (
      <>
        <div style={{ color: "#8E98A7" }}>{toppInfo[0]}</div>
        <Tooltip title={topk[0]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    25: (
      <>
        <div style={{ color: "#8E98A7" }}>{toppInfo[1]}</div>
        <Tooltip title={topk[1]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    50: (
      <>
        <div style={{ color: "#8E98A7" }}>{toppInfo[2]}</div>
        <Tooltip title={topk[2]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    75: (
      <>
        <div style={{ color: "#8E98A7" }}>{toppInfo[3]}</div>
        <Tooltip title={topk[3]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    100: (
      <>
        <div style={{ color: "#8E98A7" }}>{toppInfo[4]}</div>
        <Tooltip title={topk[4]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
  };
  const maxLenMarks: SliderMarks = {
    0: (
      <>
        <div style={{ color: "#8E98A7" }}>{maxInfo[0]}</div>
        <Tooltip title={maxlenght[0]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    25: (
      <>
        <div style={{ color: "#8E98A7" }}>{maxInfo[1]}</div>
        <Tooltip title={maxlenght[1]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    50: (
      <>
        <div style={{ color: "#8E98A7" }}>{maxInfo[2]}</div>
        <Tooltip title={maxlenght[2]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    75: (
      <>
        <div style={{ color: "#8E98A7" }}>{maxInfo[3]}</div>
        <Tooltip title={maxlenght[3]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    100: (
      <>
        <div style={{ color: "#8E98A7" }}>{maxInfo[4]}</div>
        <Tooltip title={maxlenght[4]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
  };
  const repeatitionMark: SliderMarks = {
    0: (
      <>
        <div style={{ color: "#8E98A7" }}>{repeatitionInfo[0]}</div>
        <Tooltip title={maxlenght[0]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    25: (
      <>
        <div style={{ color: "#8E98A7" }}>{repeatitionInfo[1]}</div>
        <Tooltip title={maxlenght[1]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    50: (
      <>
        <div style={{ color: "#8E98A7" }}>{repeatitionInfo[2]}</div>
        <Tooltip title={maxlenght[2]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    75: (
      <>
        <div style={{ color: "#8E98A7" }}>{repeatitionInfo[3]}</div>
        <Tooltip title={maxlenght[3]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
    100: (
      <>
        <div style={{ color: "#8E98A7" }}>{repeatitionInfo[4]}</div>
        <Tooltip title={maxlenght[4]}>
          <div
            style={{ width: 10, height: 10, marginTop: -10, zIndex: 10 }}
          ></div>
        </Tooltip>
      </>
    ),
  };

  return (
    <Scrollbar>
      <div className="adjustmentDetail">
        <Space
          size={20}
          style={{ display: "inline-flex", alignItems: "center" }}
        >
      
         <Button
            style={{ fontSize: "12px", width: "36px", height: "36px" }}
            shape="circle"
            icon={<LeftOutlined />}
            onClick={() => navigate("/main/finetune")}
          />
          <div
            className="mediumText"
            style={{ fontSize: "28px", lineHeight: "36px", fontWeight: "500" }}
          >
            {/* {taskDetail?.taskName} */}
            调试模型
         
         </div>
          <Button
                type="primary"
                size="large"
                shape="round"
               
                className="publishButton"
                onClick={() => saveSet()}
              >
                保存
              </Button>
        </Space>
        <div className="adjustment-detail-info">
          <Button
            className="newtalk"
            type="link"
            onClick={handleNewConversation}
          >
            新的对话
          </Button>
          <div className="middle"></div>
          <div className="head">
            <div
              style={{
                fontSize: "18px",
                fontWeight: "bold",
                lineHeight: "21px",
                width: "80px",
                fontFamily: "HarmonyOS Sans SC, HarmonyOS Sans SC",
              }}
            >
              参数配置
            </div>
            <div
              style={{
                fontSize: "18px",
                fontWeight: "bold",
                lineHeight: "21px",
                fontFamily: "HarmonyOS Sans SC, HarmonyOS Sans SC",
              }}
            >
              调试预览
            </div>
          </div>

          <div
            style={{
              width: "100%",
              textAlign: "start",
              justifyContent: "space-between",
              display: "inline-flex",
              marginBottom: "1rem",
              alignItems: "center",
            }}
          >
            <div className="left">
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: "20px",
                }}
              >
                {/* <Form.Item<CreateTaskFormType>>
                  <Segmented
                    className="createtask-segmented"
                    size="large"
                    options={[
                      {
                        label: (
                          <Tooltip title="自动配置">
                            <a
                              onClick={() => setModelConfig("自动配置")}
                              className={
                                modelConfig === "自动配置"
                                  ? "model-config-active"
                                  : "model-config"
                              }
                            >
                              自动配置
                            </a>
                          </Tooltip>
                        ),
                        value: "自动配置",
                      },
                      {
                        label: (
                          <Tooltip title="手动配置">
                            <a
                              onClick={() => setModelConfig("手动配置")}
                              className={
                                modelConfig === "手动配置"
                                  ? "model-config-active"
                                  : "model-config"
                              }
                            >
                              手动配置
                            </a>
                          </Tooltip>
                        ),
                        value: "手动配置",
                      },
                    ]}
                    value={modelConfig}
                    onChange={setModelConfig}
                  />
                </Form.Item> */}
              </div>
              <div className="adjiustModel">
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    width: "100%",
                  }}
                >
                  <div
                    style={{
                      fontSize: "16px",
                      fontWeight: "500",
                      lineHeight: "16px",
                      color: "#000000",
                      fontFamily: "HarmonyOS Sans SC, HarmonyOS Sans SC",
                      marginBottom: "24px",
                    }}
                  >
                    参数配置:
                  </div>
                  <Flex
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      width: "60%",
                      marginLeft: "70px",
                    }}
                  >
                 <Form.Item<CreateTaskFormType>>
                  <Segmented
                    className="createtask-segmented"
                    size="large"
                    options={[
                      {
                        label: (
                          <Tooltip title="自动配置">
                            <a
                              onClick={() => setModelConfig("自动配置")}
                              className={
                                modelConfig === "自动配置"
                                  ? "model-config-active"
                                  : "model-config"
                              }
                            >
                              自动配置
                            </a>
                          </Tooltip>
                        ),
                        value: "自动配置",
                      },
                      {
                        label: (
                          <Tooltip title="手动配置">
                            <a
                              onClick={() => setModelConfig("手动配置")}
                              className={
                                modelConfig === "手动配置"
                                  ? "model-config-active"
                                  : "model-config"
                              }
                            >
                              手动配置
                            </a>
                          </Tooltip>
                        ),
                        value: "手动配置",
                      },
                    ]}
                    value={modelConfig}
                    onChange={setModelConfig}
                  />
                </Form.Item>
                  </Flex>
                </div>
             
              </div>
              {/* <div className="adjiustModel">
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    width: "100%",
                  }}
                >
                  <div
                    style={{
                      fontSize: "16px",
                      fontWeight: "500",
                      lineHeight: "16px",
                      color: "#000000",
                      fontFamily: "HarmonyOS Sans SC, HarmonyOS Sans SC",
                    }}
                  >
                    调试模型：
                  </div>
                  <Flex
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      width: "60%",
                      marginLeft: "70px",
                    }}
                  >
                    <Select
                      placeholder={placeholder}
                      value={selectedItems}
                      onChange={(value) => setSelectedItems(value ? value : "")}
                      style={{ flex: 1 }}
                      options={OPTIONS}
                    />
                  </Flex>
                </div>
                {isShow && (
                  <div style={{ color: "red", marginRight: "40%" }}>
                    请选择模型
                  </div>
                )}
              </div> */}
              <div className="creative">
                <div
                  style={{
                    fontSize: "16px",
                    fontWeight: "500",
                    lineHeight: "16px",
                    color: "#000000",
                    fontFamily: "HarmonyOS Sans SC, HarmonyOS Sans SC",
                  }}
                >
                  创意性：
                </div>
                {modelConfig === "自动配置" ? (
                  <div>
                    <div className="createtempratrueItem">
                      <div className="temperatruelabel">
                        <label style={{ color: "#000000", lineHeight: "19px" }}>
                          温度
                        </label>
                        <Tooltip title="影响输出的随机性和创新性。较高的温度值促进创造性。">
                          <img className="frame-child179" src={group151} />
                        </Tooltip>
                      </div>
                      <Form.Item<CreateTaskFormType> name="splitLevel" noStyle>
                        <Slider
                          defaultValue={trainingSettings.splitLevel}
                          className="create-tasks-slider"
                          dots
                          tooltip={{
                            formatter: (val) => {
                              const q = val ? val : 0;
                              return temperature[q / 25];
                            },
                          }}
                          step={25}
                          marks={temperatureMarks}
                          // defaultValue={0}
                          value={trainingSettings.splitLevel}
                          onChange={(val) => {
                            setTrainingSettings((prevSettings: any) => ({
                              ...prevSettings,
                              splitLevel: val, // 假设newValue是您要设置的新值
                            }));
                            // setSplitLevel(val);
                            setModelConfig("手动配置");
                          }}
                          railStyle={{
                            height: "6px",
                            background: "#F1F6F9",
                            borderTop: "1px solid #E1EAEF",
                            borderBottom: "1px solid #E1EAEF",
                          }}
                          trackStyle={{
                            height: "6px",
                            background: "#0FB698",
                            borderTop: "1px solid #0CA287",
                            borderBottom: "1px solid #0CA287",
                          }}
                          handleStyle={{}}
                          style={{
                            //width: "67.8%",
                            width: "67.8%",
                            display: "inline-flex",
                            margin: "unset",
                          }}
                        />
                      </Form.Item>
                      {/* <label
                                                className="info-label"
                                                style={{ display: "inline-block", marginLeft: "1rem" }}
                                            >
                                                <div className="slider-label reqularText">
                                                    {temperature[splitLevel / 25]}
                                                </div>
                                            </label> */}
                    </div>
                    <div className="createtempratrueItem">
                      <div className="temperatruelabel">
                        <label style={{ color: "#000000", lineHeight: "19px" }}>
                          Top-k采样
                        </label>
                        <Tooltip title="控制候选词的范围，影响文本的多样性和新颖性。">
                          <img className="frame-child179" src={group151} />
                        </Tooltip>
                      </div>
                      <Form.Item<CreateTaskFormType> name="topk" noStyle>
                        <Slider
                          defaultValue={trainingSettings.topkValue}
                          className="create-tasks-slider"
                          dots
                          tooltip={{
                            formatter: (val) => {
                              const q = val ? val : 0;
                              return topk[q / 25];
                            },
                          }}
                          step={25}
                          marks={topkMarks}
                          value={trainingSettings.topkValue}
                          onChange={(val) => {
                            // setTopkDensity(val);
                            setTrainingSettings((prevSettings: any) => ({
                              ...prevSettings,
                              topkValue: val, // 假设newValue是您要设置的新值
                            }));
                            setModelConfig("手动配置");
                          }}
                          railStyle={{
                            height: "6px",
                            background: "#F1F6F9",
                            borderTop: "1px solid #E1EAEF",
                            borderBottom: "1px solid #E1EAEF",
                          }}
                          trackStyle={{
                            height: "6px",
                            background: "#0FB698",
                            borderTop: "1px solid #0CA287",
                            borderBottom: "1px solid #0CA287",
                          }}
                          handleStyle={{}}
                          style={{
                            width: "67.8%",
                            display: "inline-flex",
                            margin: "unset",
                          }}
                        />
                      </Form.Item>
                      {/* <label
                                                className="info-label"
                                                style={{ display: "inline-block", marginLeft: "1rem" }}
                                            >
                                                <div className="slider-label reqularText">
                                                    {topk[questionDensityValue / 25]}
                                                </div>
                                            </label> */}
                    </div>
                    {/* <label className="reqularText">
                                            预计生成xxxx对问答数据，预计耗时xxxxh
                                            </label> */}
                    <div className="createtempratrueItem">
                      <div className="temperatruelabel">
                        <label style={{ color: "#000000", lineHeight: "19px" }}>
                          Top-p采样
                        </label>
                        <Tooltip title="通过动态调整候选词池的大小，影响文本的创新性和多样性。">
                          <img className="frame-child179" src={group151} />
                        </Tooltip>
                      </div>
                      <Form.Item<CreateTaskFormType> name="topp" noStyle>
                        <Slider
                          defaultValue={trainingSettings.toppValue}
                          className="create-tasks-slider"
                          dots
                          tooltip={{
                            formatter: (val) => {
                              const q = val ? val : 0;
                              return topp[q / 25];
                            },
                          }}
                          step={25}
                          marks={toppMarks}
                          value={trainingSettings.toppValue}
                          onChange={(val) => {
                            setTrainingSettings((prevSettings: any) => ({
                              ...prevSettings,
                              toppValue: val, // 假设newValue是您要设置的新值
                            }));
                            // setTopDensity(val);
                            setModelConfig("手动配置");
                          }}
                          railStyle={{
                            height: "6px",
                            background: "#F1F6F9",
                            borderTop: "1px solid #E1EAEF",
                            borderBottom: "1px solid #E1EAEF",
                          }}
                          trackStyle={{
                            height: "6px",
                            background: "#0FB698",
                            borderTop: "1px solid #0CA287",
                            borderBottom: "1px solid #0CA287",
                          }}
                          handleStyle={{}}
                          style={{
                            width: "67.8%",
                            display: "inline-flex",
                            margin: "unset",
                          }}
                        />
                      </Form.Item>
                      {/* <label
                                                className="info-label"
                                                style={{ display: "inline-block", marginLeft: "1rem" }}
                                            >
                                                <div className="slider-label reqularText">
                                                    {topp[questionDensityValue / 25]}
                                                </div>
                                            </label> */}
                    </div>
                    {/* <Divider /> */}
                  </div>
                ) : (
                  <div>
                    <div className="createtempratrueItem">
                      <div className="temperatruelabel">
                        <label style={{ color: "#000000", lineHeight: "19px" }}>
                          温度
                        </label>
                        <Tooltip title="影响输出的随机性和创新性。较高的温度值促进创造性。">
                          <img className="frame-child179" src={group151} />
                        </Tooltip>
                      </div>
                      <Form.Item<CreateTaskFormType> name="splitLevel" noStyle>
                        <Slider
                          defaultValue={trainingSettings.splitLevel}
                          className="create-tasks-slider"
                          dots
                          tooltip={{
                            formatter: (val) => {
                              const q = val ? val : 0;
                              return temperature[q / 25];
                            },
                          }}
                          step={25}
                          marks={temperatureMarks}
                          // defaultValue={0}
                          value={trainingSettings.splitLevel}
                          onChange={(val) => {
                            setTrainingSettings((prevSettings: any) => ({
                              ...prevSettings,
                              splitLevel: val, // 假设newValue是您要设置的新值
                            }));
                            // setSplitLevel(val);
                          }}
                          railStyle={{
                            height: "6px",
                            background: "#F1F6F9",
                            borderTop: "1px solid #E1EAEF",
                            borderBottom: "1px solid #E1EAEF",
                          }}
                          trackStyle={{
                            height: "6px",
                            background: "#0FB698",
                            borderTop: "1px solid #0CA287",
                            borderBottom: "1px solid #0CA287",
                          }}
                          handleStyle={{}}
                          style={{
                            width: "67.8%",
                            display: "inline-flex",
                            margin: "unset",
                          }}
                        />
                      </Form.Item>
                      {/* <label
                                                className="info-label"
                                                style={{ display: "inline-block", marginLeft: "1rem" }}
                                            >
                                                <div className="slider-label reqularText">
                                                    {temperature[splitLevel / 25]}
                                                </div>
                                            </label> */}
                    </div>
                    <div className="createtempratrueItem">
                      <div className="temperatruelabel">
                        <label style={{ color: "#000000", lineHeight: "19px" }}>
                          Top-k采样
                        </label>
                        <Tooltip title="控制候选词的范围，影响文本的多样性和新颖性。">
                          <img className="frame-child179" src={group151} />
                        </Tooltip>
                      </div>
                      <Form.Item<CreateTaskFormType> name="topk" noStyle>
                        <Slider
                          defaultValue={trainingSettings.topkValue}
                          className="create-tasks-slider"
                          dots
                          tooltip={{
                            formatter: (val) => {
                              const q = val ? val : 0;
                              return topk[q / 25];
                            },
                          }}
                          step={25}
                          marks={topkMarks}
                          value={trainingSettings.topkValue}
                          onChange={(val) => {
                            setTrainingSettings((prevSettings: any) => ({
                              ...prevSettings,
                              topkValue: val, // 假设newValue是您要设置的新值
                            }));
                            // setTopkDensity(val);
                            setModelConfig("手动配置");
                          }}
                          railStyle={{
                            height: "6px",
                            background: "#F1F6F9",
                            borderTop: "1px solid #E1EAEF",
                            borderBottom: "1px solid #E1EAEF",
                          }}
                          trackStyle={{
                            height: "6px",
                            background: "#0FB698",
                            borderTop: "1px solid #0CA287",
                            borderBottom: "1px solid #0CA287",
                          }}
                          handleStyle={{}}
                          style={{
                            width: "67.8%",
                            display: "inline-flex",
                            margin: "unset",
                          }}
                        />
                      </Form.Item>
                      {/* <label
                                                className="info-label"
                                                style={{ display: "inline-block", marginLeft: "1rem" }}
                                            >
                                                <div className="slider-label reqularText">
                                                    {topk[questionDensityValue / 25]}
                                                </div>
                                            </label> */}
                    </div>
                    {/* <label className="reqularText">
                                            预计生成xxxx对问答数据，预计耗时xxxxh
                                            </label> */}
                    <div className="createtempratrueItem">
                      <div className="temperatruelabel">
                        <label style={{ color: "#000000", lineHeight: "19px" }}>
                          Top-p采样
                        </label>
                        <Tooltip title="通过动态调整候选词池的大小，影响文本的创新性和多样性。">
                          <img className="frame-child179" src={group151} />
                        </Tooltip>
                      </div>
                      <Form.Item<CreateTaskFormType> name="topp" noStyle>
                        <Slider
                          defaultValue={trainingSettings.toppValue}
                          className="create-tasks-slider"
                          dots
                          tooltip={{
                            formatter: (val) => {
                              const q = val ? val : 0;
                              return topp[q / 25];
                            },
                          }}
                          step={25}
                          marks={toppMarks}
                          value={trainingSettings.toppValue}
                          onChange={(val) => {
                            // setTopDensity(val);
                            setTrainingSettings((prevSettings: any) => ({
                              ...prevSettings,
                              toppValue: val, // 假设newValue是您要设置的新值
                            }));
                          }}
                          railStyle={{
                            height: "6px",
                            background: "#F1F6F9",
                            borderTop: "1px solid #E1EAEF",
                            borderBottom: "1px solid #E1EAEF",
                          }}
                          trackStyle={{
                            height: "6px",
                            background: "#0FB698",
                            borderTop: "1px solid #0CA287",
                            borderBottom: "1px solid #0CA287",
                          }}
                          handleStyle={{}}
                          style={{
                            width: "67.8%",
                            display: "inline-flex",
                            margin: "unset",
                          }}
                        />
                      </Form.Item>
                      {/* <label
                                                className="info-label"
                                                style={{ display: "inline-block", marginLeft: "1rem" }}
                                            >
                                                <div className="slider-label reqularText">
                                                    {topp[questionDensityValue / 25]}
                                                </div>
                                            </label> */}
                    </div>
                    {/* <Divider /> */}
                  </div>
                )}
              </div>
              <div className="correlation">
                <div
                  style={{
                    fontSize: "16px",
                    fontWeight: "500",
                    lineHeight: "16px",
                    marginTop: "42px",
                    color: "#000000",
                    fontFamily: "HarmonyOS Sans SC, HarmonyOS Sans SC",
                  }}
                >
                  相关性：
                </div>
                {modelConfig === "自动配置" ? (
                  <div>
                    <div className="createtempratrueItem">
                      <div className="temperatruelabel">
                        <label style={{ color: "#000000", lineHeight: "19px" }}>
                          最大长度
                        </label>
                        <Tooltip title="虽然不直接影响相关性，但适当的长度可以确保回答不偏离主题。">
                          <img className="frame-child179" src={group151} />
                        </Tooltip>
                      </div>
                      <Form.Item<CreateTaskFormType> name="maxlen" noStyle>
                        <Slider
                          defaultValue={trainingSettings.maxLen}
                          className="create-tasks-slider"
                          dots
                          tooltip={{
                            formatter: (val) => {
                              const q = val ? val : 0;
                              return maxlenght[q / 25];
                            },
                          }}
                          step={25}
                          marks={maxLenMarks}
                          // defaultValue={0}
                          value={trainingSettings.maxLen}
                          onChange={(val) => {
                            // setMaxLen(val);
                            setTrainingSettings((prevSettings: any) => ({
                              ...prevSettings,
                              maxLen: val, // 假设newValue是您要设置的新值
                            }));
                            setModelConfig("手动配置");
                          }}
                          railStyle={{
                            height: "6px",
                            background: "#F1F6F9",
                            borderTop: "1px solid #E1EAEF",
                            borderBottom: "1px solid #E1EAEF",
                          }}
                          trackStyle={{
                            height: "6px",
                            background: "#0FB698",
                            borderTop: "1px solid #0CA287",
                            borderBottom: "1px solid #0CA287",
                          }}
                          handleStyle={{}}
                          style={{
                            width: "67.8%",
                            display: "inline-flex",
                            margin: "unset",
                          }}
                        />
                      </Form.Item>
                      {/* <label
                                                className="info-label"
                                                style={{ display: "inline-block", marginLeft: "1rem" }}
                                            >
                                                <div className="slider-label reqularText">
                                                    {temperature[splitLevel / 25]}
                                                </div>
                                            </label> */}
                    </div>
                    <div className="createtempratrueItem">
                      <div className="temperatruelabel">
                        <label style={{ color: "#000000", lineHeight: "19px" }}>
                          重复率惩罚
                        </label>
                        <Tooltip title="通过减少重复，帮助模型保持话题的聚焦和相关性。">
                          <img className="frame-child179" src={group151} />
                        </Tooltip>
                      </div>
                      <Form.Item<CreateTaskFormType> name="repeat" noStyle>
                        <Slider
                          defaultValue={trainingSettings.repeatValue}
                          className="create-tasks-slider"
                          dots
                          tooltip={{
                            formatter: (val) => {
                              const q = val ? val : 0;
                              return repeatition[q / 25];
                            },
                          }}
                          step={25}
                          marks={repeatitionMark}
                          value={trainingSettings.repeatValue}
                          onChange={(val) => {
                            // setRepeatValue(val);
                            setTrainingSettings((prevSettings: any) => ({
                              ...prevSettings,
                              repeatValue: val, // 假设newValue是您要设置的新值
                            }));
                            setModelConfig("手动配置");
                          }}
                          railStyle={{
                            height: "6px",
                            background: "#F1F6F9",
                            borderTop: "1px solid #E1EAEF",
                            borderBottom: "1px solid #E1EAEF",
                          }}
                          trackStyle={{
                            height: "6px",
                            background: "#0FB698",
                            borderTop: "1px solid #0CA287",
                            borderBottom: "1px solid #0CA287",
                          }}
                          handleStyle={{}}
                          style={{
                            width: "67.8%",
                            display: "inline-flex",
                            margin: "unset",
                          }}
                        />
                      </Form.Item>
                      {/* <label
                                                className="info-label"
                                                style={{ display: "inline-block", marginLeft: "1rem" }}
                                            >
                                                <div className="slider-label reqularText">
                                                    {repeat[questionDensityValue / 25]}
                                                </div>
                                            </label> */}
                    </div>
                    {/* <label className="reqularText">
                                            预计生成xxxx对问答数据，预计耗时xxxxh
                                            </label> */}
                    {/* <Divider /> */}
                  </div>
                ) : (
                  <div>
                    <div className="createtempratrueItem">
                      <div className="temperatruelabel">
                        <label style={{ color: "#000000", lineHeight: "19px" }}>
                          最大长度
                        </label>
                        <Tooltip title="虽然不直接影响相关性，但适当的长度可以确保回答不偏离主题。">
                          <img className="frame-child179" src={group151} />
                        </Tooltip>
                      </div>
                      <Form.Item<CreateTaskFormType> name="maxlen" noStyle>
                        <Slider
                          defaultValue={trainingSettings.maxLen}
                          className="create-tasks-slider"
                          dots
                          tooltip={{
                            formatter: (val) => {
                              const q = val ? val : 0;
                              return maxlenght[q / 25];
                            },
                          }}
                          step={25}
                          marks={maxLenMarks}
                          // defaultValue={0}
                          value={trainingSettings.maxLen}
                          onChange={(val) => {
                            // setMaxLen(val);
                            setTrainingSettings((prevSettings: any) => ({
                              ...prevSettings,
                              maxLen: val, // 假设newValue是您要设置的新值
                            }));
                          }}
                          railStyle={{
                            height: "6px",
                            background: "#F1F6F9",
                            borderTop: "1px solid #E1EAEF",
                            borderBottom: "1px solid #E1EAEF",
                          }}
                          trackStyle={{
                            height: "6px",
                            background: "#0FB698",
                            borderTop: "1px solid #0CA287",
                            borderBottom: "1px solid #0CA287",
                          }}
                          handleStyle={{}}
                          style={{
                            width: "67.8%",
                            display: "inline-flex",
                            margin: "unset",
                          }}
                        />
                      </Form.Item>
                      {/* <label
                                                className="info-label"
                                                style={{ display: "inline-block", marginLeft: "1rem" }}
                                            >
                                                <div className="slider-label reqularText">
                                                    {temperature[splitLevel / 25]}
                                                </div>
                                            </label> */}
                    </div>
                    <div className="createtempratrueItem">
                      <div className="temperatruelabel">
                        <label style={{ color: "#000000", lineHeight: "19px" }}>
                          重复率惩罚
                        </label>
                        <Tooltip title="通过减少重复，帮助模型保持话题的聚焦和相关性。">
                          <img className="frame-child179" src={group151} />
                        </Tooltip>
                      </div>
                      <Form.Item<CreateTaskFormType> name="repeat" noStyle>
                        <Slider
                          defaultValue={trainingSettings.repeatValue}
                          className="create-tasks-slider"
                          dots
                          tooltip={{
                            formatter: (val) => {
                              const q = val ? val : 0;
                              return repeatition[q / 25];
                            },
                          }}
                          step={25}
                          marks={repeatitionMark}
                          value={trainingSettings.repeatValue}
                          onChange={(val) => {
                            // setRepeatValue(val);
                            setTrainingSettings((prevSettings: any) => ({
                              ...prevSettings,
                              repeatValue: val, // 假设newValue是您要设置的新值
                            }));
                          }}
                          railStyle={{
                            height: "6px",
                            background: "#F1F6F9",
                            borderTop: "1px solid #E1EAEF",
                            borderBottom: "1px solid #E1EAEF",
                          }}
                          trackStyle={{
                            height: "6px",
                            background: "#0FB698",
                            borderTop: "1px solid #0CA287",
                            borderBottom: "1px solid #0CA287",
                          }}
                          handleStyle={{}}
                          style={{
                            width: "67.8%",
                            display: "inline-flex",
                            margin: "unset",
                          }}
                        />
                      </Form.Item>
                    </div>
                  </div>
                )}
              </div>
            
            </div>
            <div className="right">
              <div
                style={{
                  position: "absolute",
                  top: "166px",
                  width: "50%",
                  height: "0px",
                  border: "1px solid #D9D9D9",
                }}
              ></div>
              <div className="talkcontent">
                <Scrollbar ref={scrollbarRef}>
                  <Space size={16} style={{paddingBottom: "80px"}} direction="vertical">
                    <div className="chat-message-container">
                      <img className="avatar-icon" src={group310} />
                      {/* {[selectedItems].map((item, index) => (
                                                <div style={{ marginLeft: "20px" }} key={index}>{item}</div>
                                            ))} */}
                      <div style={{ marginLeft: "20px" }}>军事智能专家</div>
                    </div>
                    <div className="chat-message-ai">
                      您好，我是军事智能助手有什么可以帮助您
                    </div>
                    {messageList?.map((mes) => (
                      <>
                        {mes.role === "user" ? (
                          <ChatMessage fromUser={true} text={mes.content} />
                        ) : (
                          <>
                            <div className="chat-message-container">
                              <img className="avatar-icon" src={group310} />
                              {/* {[selectedItems].map((item, index) => (
                                                                <div style={{ marginLeft: "20px" }} key={index}>{item}</div>
                                                            ))} */}
                              <div style={{ marginLeft: "20px" }}>
                                军事智能专家
                              </div>
                            </div>
                            <div className="chat-message-ai">{mes.content}</div>
                          </>
                        )}
                      </>
                    ))}
                  </Space>
                </Scrollbar>
                <div className="chatTalk">
                  <TextArea
                    style={{
                      border: "none",
                      boxShadow: "none",
                      maxHeight: "20px",
                    }}
                    placeholder={isLocked ? "正在回答..." : "请输入问题..."}
                    autoSize
                    onPressEnter={(e) => {
                      if (isLocked) {
                        e.preventDefault(); // 阻止默认行为
                        console.log("请等待回答完成后再发送消息！");
                        return;
                      }
                      // 检查是否按下了Enter键
                      if (e.key === "Enter") {
                        // 判断Shift键是否被同时按下
                        if (e.shiftKey) {
                          // Shift + Enter 被按下，执行换行操作
                          // 默认行为是换行，所以这里不需要编写额外的代码
                        } else {
                          // 阻止默认行为（换行）
                          e.preventDefault();

                          // 只按下了Enter键，调用发送消息的函数
                          onSendMessage();
                          setMessageInput("");
                        }
                      }
                    }}
                    onChange={(e) => setMessageInput(e.target.value)}
                    value={messageInput}
                  />
                </div>
                <Button
                  className="sendButton"
                  type="text"
                  disabled={isLocked}
                  onClick={() => {
                    onSendMessage();
                  }}
                >
                  <SendOutlined />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Scrollbar>
  );
});

export default AdjustmentView;
// 弃用  功能代码转移到 adjustmentView 文件夹下index.tsx

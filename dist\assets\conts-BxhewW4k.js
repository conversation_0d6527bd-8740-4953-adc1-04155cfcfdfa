const s=["笼统提问","较笼统提问","正常提问","较集中提问","集中提问"],a=["最大长度分段","较大长度分段","中等长度分段","较小长度分段","最小长度分段"],e=["一致性高","略增多样性","平衡稳定","创新性强","极致创新"],t=["严格限制","较严格","平衡选择","宽松选择","极度宽松"],c=["保守选择","略增多样性","平衡多样","多样性高","极高多样性"],o=["简洁回答","中等详细","较详细","详尽回答","极度详尽"],p=["允许重复","轻微抑制重复","中等抑制","强力抑制","极端抑制"],r=["快速训练，适合初步模型探索","速度优先，适度性能考虑","平衡选择，兼顾效率与效果","性能优先，适应深入分析需求","深度训练，追求高精度结果"],b=["0.0","0.25","0.5","0.75","1.0"],d=["5","10","20","50","100"],f=["0.6","0.7","0.8","0.9","0.95"],g=["400","800","1200","1600","2000"],h=["1.0","1.2","1.5","1.8","2.0"],i=["1","2","3","4","5"],j=["1e-5","5e-5","1e-4","5e-4","1e-3"],k=["1","2","4","8","16"],l=["10%","20%","30%","40%","50%"],m=["并发优先","并发优先","速度优先","速度优先","速度优先"];export{d as a,e as b,t as c,c as d,o as e,p as f,a as g,l as h,k as i,i as j,r as k,j as l,g as m,m as p,s as q,h as r,b as s,f as t};

import { paragraph, questionDensity } from '@/utils/conts';

// 工具提示常量
export const TOOLTIP_MESSAGES = {
  DESCRIPTION: '需求描述内的文本均可更改，字数限制600字',
  SPLIT_LEVEL:
    '该参数决定推理过程中每次输入的上下文长度，精细度越高，上下文长度越小，每篇文档分割出的文本段落就越多。',
  QUESTION_DENSITY:
    '该参数决定每段文本推理的问答对数量，密度越大，模型会尽量从每个段落中推理越多的问题和答案。',
};

// 默认值常量
export const DEFAULT_VALUES = {
  TASK_NAME: '',
  MODEL_CONFIG: '自动配置' as const,
  SPLIT_LEVEL: 50,
  QUESTION_DENSITY: 50,
  LABEL_TEXT: '装备维修，运用参考，性能参考',
  AUTO_LABEL: false,
  SELECTED_INDEX: -1,
  CURRENT_EXAMPLE: 0,
  SCROLLING: true,
  MAX_TASK_NAME_LENGTH: 32,
  MAX_DESCRIPTION_LENGTH: 600,
  MAX_LABEL_COUNT: 20,
  SCROLL_INTERVAL: 2000,
};

// 表单验证规则
export const VALIDATION_RULES = {
  TASK_NAME: {
    MAX_LENGTH: 32,
    PATTERN: /^[\u4e00-\u9fa5a-zA-Z0-9,——()""_]+$/,
    MESSAGE: '仅允许中文、英文、数字及符号 ,——()""_',
  },
  LABEL: {
    PATTERN: /^(?!.*(?:^|[,，])\d+(?:[,，]|$))[\u4e00-\u9fa5a-zA-Z0-9,，]+$/,
    MESSAGE: '标签格式不正确，请勿输入特殊符号或纯数字标签',
  },
};

// 滑块样式配置
export const SLIDER_STYLES = {
  RAIL: {
    height: '6px',
    background: '#F1F6F9',
    borderTop: '1px solid #E1EAEF',
    borderBottom: '1px solid #E1EAEF',
  },
  TRACK: {
    height: '6px',
    background: '#0FB698',
    borderTop: '1px solid #0CA287',
    borderBottom: '1px solid #0CA287',
  },
  CONTAINER: {
    width: '39.25rem',
    display: 'inline-flex',
    margin: 'unset',
  },
};

// 段落精细度滑块标记文本
export const PARAGRAPH_MARKS_TEXT = {
  0: paragraph[0],
  25: paragraph[1],
  50: paragraph[2],
  75: paragraph[3],
  100: paragraph[4],
};

// 问题密度滑块标记文本
export const QUESTION_DENSITY_MARKS_TEXT = {
  0: questionDensity[0],
  25: questionDensity[1],
  50: questionDensity[2],
  75: questionDensity[3],
  100: questionDensity[4],
};

// 模型配置选项
export const MODEL_CONFIG_OPTIONS = [
  {
    label: '自动配置',
    value: '自动配置' as const,
    tooltip: '自动配置',
  },
  {
    label: '手动配置',
    value: '手动配置' as const,
    tooltip: '手动配置',
  },
];

// 任务配置映射
export const TASK_CONFIG_MAPPING = {
  DEFAULT_SPLIT_LEVEL: 3,
  DEFAULT_QUESTION_DENSITY: 3,
  STEP_SIZE: 25,
  OFFSET: 1,
};

// 样式类名常量
export const CSS_CLASSES = {
  CREATE_TASK_CONTENT: 'createTaskContent',
  CREATE_TASK_AREA: 'createTaskArea',
  CREATE_TASK_ITEM: 'createTaskItem',
  CREATE_TASK_SELECT_BTN: 'createTaskSelectBtn reqularText',
  CREATE_TASK_SLIDER: 'create-task-slider',
  CREATE_TASK_SEGMENTED: 'createtask-segmented',
  CREATE_TASK_FILE_LIST: 'create-task-file-list',
  MEDIUM_TEXT: 'mediumText',
  REGULAR_TEXT: 'reqularText',
  LIST_STYLE: 'listStyle',
  CONTEXT: 'context',
  AUTO_LABEL: 'auto-label',
  LABEL_TEXTAREA: 'label-textarea',
  LABEL_TEXTAREA_FOOTER: 'label-textarea-footer',
  CREATE_BTN: 'createBtn',
  CREATE_AI_BTN: 'createAiBtn',
  SHOW_BTN: 'show-btn',
  MODEL_CONFIG: 'model-config',
  MODEL_CONFIG_ACTIVE: 'model-config-active',
  CUSTOM_CONFIG_LABEL: 'customConfigLabel',
  CREATE_TASK_DRAGGER_LABEL: 'crateTaskDraggerLabel reqularText',
  INFO_LABEL: 'info-label',
  SLIDER_LABEL: 'slider-label reqularText',
  FRAME_CHILD179: 'frame-child179',
};

// 消息常量
export const MESSAGES = {
  SUCCESS: {
    LABEL_GENERATED: '标签生成成功！',
    TASK_CREATED: '任务创建成功！',
  },
  WARNING: {
    SELECT_DATASET: '请先选择源数据集',
    MAX_LABELS: '生成的标签数量不能超过20个',
    NO_RECOMMENDED_LABELS: '暂无推荐标签数据',
  },
  ERROR: {
    TASK_NAME_INVALID: '任务名称不合法',
    LABEL_FORMAT_ERROR: '标签格式不正确，请勿输入特殊符号或纯数字标签',
    LABEL_GENERATION_FAILED: '标签生成失败，请重试',
    TASK_CREATION_FAILED: '任务创建失败',
  },
  PLACEHOLDER: {
    TASK_NAME: '请输入任务名称',
    DESCRIPTION: '请输入需求描述',
    LABEL: '请输入分类标签，以逗号分隔。示例:飞机，电磁，坦克..……，请勿使用特殊符号',
  },
};

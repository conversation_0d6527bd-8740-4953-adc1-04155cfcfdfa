import{r as e}from"./request-v7NtcsCU.js";const n=navigator.userAgent;const r=function(){let e="";return/Chrome/.test(n)?e="Chrome":/Safari/.test(n)?e="Safari":/Firefox/.test(n)?e="Firefox":/Edge/.test(n)&&(e="Edge"),e}(),o=function(){let e="";return e=-1!==n.indexOf("Windows")?"Windows":-1!==n.indexOf("Macintosh")?"MacOS":-1!==n.indexOf("Linux")?"Linux":"Unknown",e}();function t(n){return n.fingerPrint={browser:r,os:o,ip:"",device:""},e.post("/user/register",n)}function i(n){return n.fingerPrint={browser:r,os:o,ip:"",device:""},e.post("/user/login",n)}function s(n){return n.fingerPrint={browser:r,device:"",ip:"",os:o},e.post("/user/login/send-code",n)}function a(n){return n.fingerPrint={browser:r,os:o,ip:"",device:""},e.post("/user/register/send-code",n)}function u(){return e.get("/logo/info")}function f(n){const r=new FormData;return r.append("logoName",n.logoName),r.append("file",n.file),e.post("/logo/upload",r,{headers:{"Content-Type":"multipart/form-data"}})}export{t as a,i as b,u as g,s as l,a as r,f as u};

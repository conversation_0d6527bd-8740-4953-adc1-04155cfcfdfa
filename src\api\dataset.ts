import { AxiosResponse } from "axios";
import { CustomResponseType, DatasetFilter, DataSetTreeType, DataSetType, ParseDatasetType } from "../types";
import request from "../utils/request";

export interface GetDataSetsParams {
  page?: number;
  size?: number;
  sortAttribute?: string;
  sortDirection?: string;
  filterAttribute?: string;
  // filter?: DatasetFilter;
  datasetName: string;
  tag: string;
  startTime: string;
  endTime: string;
}

interface UploadDatasetParams {
  importDataset: any;
  tags?: string[];
}

interface RenameDataSetParams {
  datasetId: string;
  datasetName: string;
}
interface ChangeDataSetsTagsParams {
  dataSetId: string;
  tags: string[];
}

interface DataSetsResp extends CustomResponseType {
  data: DataSetType[];
  totalCount: number;
}

interface DataSetsFileTreeResp extends CustomResponseType {
  data: DataSetTreeType;
}

interface DataSetsFilePreview extends CustomResponseType {
  data: ParseDatasetType
}
/**
 * 获取数据集
 * @param getDataSets
 * @returns
 */
export function getDataSets(
  params: GetDataSetsParams
): Promise<AxiosResponse<DataSetsResp>> {
  let url = `/dataset/list?page=${params.page}&size=${params.size}`;
  url = params.sortAttribute ? url + `&sortAttribute=${params.sortAttribute}` : url;
  url = params.sortDirection ? url + `&sortDirection=${params.sortDirection}` : url;
  url = params.tag ? url + `&tag=${params.tag}` : url;
  url = params.startTime ? url + `&startTime=${params.startTime}` : url;
  url = params.endTime ? url + `&endTime=${params.endTime}` : url;
  url = params.datasetName ? url + `&datasetName=${params.datasetName}` : url;
  // if (params.filter) {
  //   url += `&${JSON.stringify(params.filter)}`;
  // }
  return request.get(url);
}

/**
 * 上传数据集
 * @param params 上传文件
 * @returns
 */

export function uploadDataset(params: UploadDatasetParams) {
  const formData = new FormData();
  formData.append("importDataset", params.importDataset);
  if (params.tags && params.tags.length > 0) formData.append("tags", JSON.stringify(params.tags));
  // return request.post("/data/import", {
  //   data: formData,
  //   headers: {
  //     "Content-Type": "multipart/form-data",
  //   },
  // });
  return request.post("/dataset", formData);
}

/**
 * 删除数据集
 * @param deleteIds 需要删除的数据集ID
 * @returns
 */

export function deleteDataset(deleteIds: string[]) {
  return request.delete("/dataset", { data: { deleteIds } });
}


/**
 * 重命名数据集
 * @param params
 * @returns
 */
export function renameDataSets(
  params: RenameDataSetParams
): Promise<AxiosResponse<CustomResponseType>> {
  const url = `/dataset?datasetId=${params.datasetId}&datasetName=${params.datasetName}`
  return request.put(url);
}

/**
 * 修改数据集标签
 * @param params
 * @returns
 */
export function changeDataSetsTags(
  params: ChangeDataSetsTagsParams
): Promise<AxiosResponse<CustomResponseType>> {
  const url = `/dataset/tags`;
  return request.put(url, { tags: params.tags, datasetId: params.dataSetId });
}

/**
 * 获取数据集文件结构
 * @param datasetId
 * @returns
 */
export function getDataSetTreeData(datasetId: string): Promise<AxiosResponse<DataSetsFileTreeResp>> {
  const url = `/dataset/tree?datasetId=${datasetId}`;
  return request.get(url);
}

/**
 * 获取数据集文件结构
 * @param fileId
 * @returns
 */
export function getDataFilePreview(fileId: string): Promise<AxiosResponse<DataSetsFilePreview>> {
  const url = `/dataset/preview?fileId=${fileId}`;
  return request.get(url);
}

// /**
//  * 获取任务文件结构
//  * @param fileId 文件ID
//  * @param taskId 任务ID
//  * @returns
//  */
// export function getDataSetTreeDataInTask(fileId: string, taskId: string): Promise<AxiosResponse<any>> {
//   const url = `/data/fileTree/${fileId}/${taskId}`;
//   return request.get(url);
// }

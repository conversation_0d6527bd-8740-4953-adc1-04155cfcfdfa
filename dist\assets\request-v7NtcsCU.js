import{a as t}from"./http-CC8KrKfC.js";import{N as s,a as e}from"./utils-DuyTwmHT.js";import{t as o}from"./react-core-BrQk45h1.js";s.settings.showSpinner=!1;const a=t.create({baseURL:"/api",timeout:12e5});a.interceptors.request.use((t=>{let o=e.get("token");return s.start(),o&&(t.headers["ACCESS-KEY"]=o),t})),a.interceptors.response.use((t=>(s.done(),200!==t.status?401===t.status?o.info("没有权限"):500===t.status||505===t.status?o.info("服务器错误"):404===t.status?o.info("404找不到请求地址"):403===t.status?o.info("登录状态已失效,请重新登录"):o.info("请求错误"):200===t.status&&t.data.code&&200!==t.data.code&&403===t.data.code&&e.remove("token"),t)),(t=>(s.done(),o.info("请求错误"),t)));export{a as r};

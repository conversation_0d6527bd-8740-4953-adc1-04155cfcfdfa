import{r as e}from"./request-v7NtcsCU.js";function t(t){let n=`/fine-tuning/model/list?page=${t.page}&size=${t.size}&category=${t.category}`;return n=t.sortAttribute?n+`&sortAttribute=${t.sortAttribute}`:n,n=t.sortDirection?n+`&sortDirection=${t.sortDirection}`:n,n=t.modelName?n+`&modelName=${t.modelName}`:n,n=t.status?n+`&status=${t.status}`:n,n=t.modelCategory?n+`&modelCategory=${t.modelCategory}`:n,e.get(n)}function n(t){return e.get(`/fine-tuning/model?modelId=${t}`)}async function a(){try{const t=await e.get("/fine-tuning/model/list");if(200===t.data.code){return t.data.data.filter((e=>0===e.category))}return[]}catch(t){return[]}}function o(t){return e.post(`/fine-tuning/model/model/${t}`)}function r(t){return e.post("/fine-tuning/model",t)}function s(t){return e.delete(`/fine-tuning/model/delete/${t}`)}function i(t){return e.post(`/fine-tuning/model/train/stop/${t}`)}function d(t){return e.post(`/fine-tuning/model/train/${t}`)}function u(t){return e.post(`/fine-tuning/model/model/${t}`)}function c(t){return e.get(`/fine-tuning/model/config/${t}`)}function g(){return e.get("/fine-tuning/model/server/all")}function m(t){return e.get(`/fine-tuning/model/server/${t}`)}function f(t){return e.get("/fine-tuning/model/configInfo",{params:{modelId:t}})}function l(t){return e.get(`/fine-tuning/model/info/${t}`)}function $(t,n){return e.post(`/fine-tuning/model/config?modelId=${t}`,n)}async function p(t){try{const n="/fine-tuning/model/upload",a=await e.post(n,t);return 200===a.data.code?{code:200,message:"上传成功",data:!0}:{code:400,message:a.data.message,data:!1}}catch(n){return{code:400,message:"未知错误",data:!1}}}async function y(t){try{const n="/fine-tuning/model/base-model",a=await e.post(n,t);return 200===a.data.code?{code:200,message:"添加成功",data:!0}:{code:400,message:a.data.message,data:!1}}catch(n){return{code:400,message:"未知错误",data:!1}}}async function b(t){try{const n=`/fine-tuning/model/delete/${t}`,a=await e.delete(n);return 200===a.data.code?{code:200,message:"删除成功",data:!0}:{code:400,message:a.data.message,data:!1}}catch(n){return{code:400,message:"未知错误",data:!1}}}function h(t){return e.get(`/server/by-model?modelId=${t}`)}export{f as a,g as b,o as c,$ as d,m as e,y as f,t as g,s as h,n as i,i as j,c as k,b as l,l as m,h as n,u as o,r as p,a as q,d as s,p as u};

.preview-file-modal {
    display: flex;
    flex-direction: row;
    gap: 32px;
    width: 100%;
    align-items: flex-start;
    padding-top: 24px;
}

.preview-file-main {
    flex: 1;
    font-size: 16px;
    /* max-height: 100%; */
}

.preview-tree {
    width: 180px;
    height: 100%;
}

.preview-switch-btn {
    width: 88px;
    height: 80px;
    flex-shrink: 0;
    border-radius: 8px;
    border: 1px solid #111;
    background: #FFF;
}

.preview-switch-btn-item {
    height: 40px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;
}

.preview-switch-btn-item.active {
    background: #111;
    color: white;
}

.modal-title {
    display: flex;
    gap: 12px;
    align-items: center;
}

.back-btn {
    font-size: 14px;
    width: 88px;
    height: 36px;
    gap: 2px;
    color: #7886AA;
}
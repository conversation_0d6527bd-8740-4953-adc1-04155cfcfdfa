import{r as e,k as a,u as s,j as t,a2 as i,f as r,a9 as l,aa as c}from"./react-core-BrQk45h1.js";import{w as n}from"./white-logo-C5vvyXfj.js";import{a as o}from"./avatar-1-Bswa0fNs.js";import"./file-utils-DYD-epjE.js";import"./vendor-5xQGrmEQ.js";import"./antd-icons-CJI3ned2.js";const d=()=>{const[r,l]=e.useState(0),[c,d]=e.useState(),[m,h]=e.useState(o),j=a(),v=s(),x=["/overview","/main/sourcedata","/main/task"],[f,p]=e.useState(!1);return e.useEffect((()=>{const e=sessionStorage.getItem("userInfo");e&&d(JSON.parse(e))}),[]),e.useEffect((()=>{c&&h(c.avatar)}),[c]),e.useEffect((()=>{x.forEach(((e,a)=>{v.pathname.indexOf(e)>-1&&l(a+1)}))}),[v]),t.jsxs("div",{className:"/overview"===v.pathname?"hello-parent14 headerFirst":"hello-parent14 headerDefault",children:[t.jsx("img",{src:n,className:"logo37"}),t.jsx("div",{className:"everreachai-pollux20 enText",children:"Pollux 产品后台"}),t.jsxs("div",{className:"parent131",children:[t.jsx("div",{className:1===r?"div691 active-tab":"div691",onClick:()=>{f&&p(!1),l(1),j("/admin/dashboard")},children:"数据看板"}),t.jsx("div",{className:2===r?"div691 active-tab":"div691",onClick:()=>{f&&p(!1),l(2),j("/admin/feedback")},children:"用户反馈"})]}),t.jsx("b",{className:"hello16",children:"Hello,"}),t.jsx(i,{className:"ellipse-parent29",src:m})]})},{Content:m}=l,h={textAlign:"center",minHeight:"calc(100vh - 52px)",color:"#000",backgroundColor:"#fff"},j=()=>t.jsx(t.Fragment,{children:t.jsx(r,{direction:"vertical",style:{width:"100%"},size:[0,48],children:t.jsxs(l,{className:"pageLayout",children:[t.jsx(d,{}),t.jsx(m,{style:h,children:t.jsx("div",{className:"main-content",children:t.jsx(c,{})})})]})})});export{j as default};

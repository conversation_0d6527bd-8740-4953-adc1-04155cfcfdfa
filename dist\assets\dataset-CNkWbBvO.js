import{r as t}from"./request-v7NtcsCU.js";function a(a){let e=`/dataset/list?page=${a.page}&size=${a.size}`;return e=a.sortAttribute?e+`&sortAttribute=${a.sortAttribute}`:e,e=a.sortDirection?e+`&sortDirection=${a.sortDirection}`:e,e=a.tag?e+`&tag=${a.tag}`:e,e=a.startTime?e+`&startTime=${a.startTime}`:e,e=a.endTime?e+`&endTime=${a.endTime}`:e,e=a.datasetName?e+`&datasetName=${a.datasetName}`:e,t.get(e)}function e(a){const e=new FormData;return e.append("importDataset",a.importDataset),a.tags&&a.tags.length>0&&e.append("tags",JSON.stringify(a.tags)),t.post("/dataset",e)}function s(a){return t.delete("/dataset",{data:{deleteIds:a}})}function r(a){const e=`/dataset?datasetId=${a.datasetId}&datasetName=${a.datasetName}`;return t.put(e)}function n(a){return t.put("/dataset/tags",{tags:a.tags,datasetId:a.dataSetId})}function d(a){const e=`/dataset/tree?datasetId=${a}`;return t.get(e)}function i(a){const e=`/dataset/preview?fileId=${a}`;return t.get(e)}export{d as a,a as b,n as c,s as d,i as g,r,e as u};

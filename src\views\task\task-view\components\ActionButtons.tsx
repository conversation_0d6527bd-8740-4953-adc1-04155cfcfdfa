import React from 'react';
import { Button, Space } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { ActionButtonsProps } from '../types';
import bxtrash from '@/assets/img/bxtrash.svg';
import bxdownload from '@/assets/img/bxdownload.svg';

const ActionButtons: React.FC<ActionButtonsProps> = ({
  selectedRows,
  onDelete,
  onExport,
  onUpload,
  onCreate,
}) => {
  const hasSelectedRows = selectedRows.length > 0;

  return (
    <Space>
      {hasSelectedRows && (
        <Button
          size="large"
          className="upload-dataset-btn"
          onClick={onDelete}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: 12,
          }}
        >
          <img className="btn-icon" src={bxtrash} alt="删除任务" />
          删除任务
        </Button>
      )}
      {hasSelectedRows && (
        <Button
          size="large"
          className="upload-dataset-btn"
          onClick={onExport}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: 12,
          }}
        >
          <img className="btn-icon" src={bxdownload} alt="导出结果" />
          导出结果
        </Button>
      )}
      <Button
        size="large"
        icon={<UploadOutlined />}
        style={{
          backgroundColor: '#EEF1F5',
          fontSize: '14px',
          fontWeight: '500',
          width: '154px',
          height: '40px',
          marginRight: '10px',
        }}
        onClick={onUpload}
      >
        上传本地数据
      </Button>
      <Button
        type="primary"
        size="large"
        shape="round"
        style={{
          backgroundColor: 'black',
          fontSize: '14px',
          fontWeight: '700',
          width: '122px',
        }}
        className="boldText"
        onClick={onCreate}
      >
        生成训练数据
      </Button>
    </Space>
  );
};

export default ActionButtons;

import{r as e,j as t,S as s,c as l,d as a,D as r,B as i,e as n,G as o,f as d,g as c,i as u}from"./react-core-BrQk45h1.js";import{i as x}from"./charts-C9p0W68L.js";import{r as f}from"./request-v7NtcsCU.js";import{V as h}from"./vendor-5xQGrmEQ.js";import"./file-utils-DYD-epjE.js";import"./antd-icons-CJI3ned2.js";import"./http-CC8KrKfC.js";import"./utils-DuyTwmHT.js";const y={color:"#0FB698",fontFamily:"HarmonyOS Sans SC Bold",fontSize:"16px",fontStyle:"normal",fontWeight:700,lineHeight:"normal"},m=[{key:"day",label:"日活跃度"},{key:"week",label:"周活跃度"},{key:"month",label:"月活跃度"},{key:"quarter",label:"季活跃度"},{key:"year",label:"年活跃度"}],p=e=>{switch(e){case"day":default:return"日活跃度";case"week":return"周活跃度";case"month":return"月活跃度";case"quarter":return"季活跃度";case"year":return"年活跃度"}},j=()=>{const j=e.useRef(null),v=e.useRef(null),g=e.useRef(null),[S,b]=e.useState(),[k,N]=e.useState("day"),[z,w]=e.useState(),[A,F]=e.useState("day"),[B,T]=e.useState(),[R,L]=e.useState("day"),[C,E]=e.useState([]);e.useEffect((()=>{f.get("/analyse/userinfo").then((e=>{var t;(null==(t=null==e?void 0:e.data)?void 0:t.data)&&b(e.data.data.resultMap)})),f.get("/analyse/data/review").then((e=>{var t;(null==(t=null==e?void 0:e.data)?void 0:t.data)&&w(e.data.data.resultMap)})),f.get("/analyse/data/generate").then((e=>{var t;(null==(t=null==e?void 0:e.data)?void 0:t.data)&&T(e.data.data.resultMap)})),f.get("/analyse/domain").then((e=>{var t;(null==(t=null==e?void 0:e.data)?void 0:t.data)&&E(function(e){const t=[],s=Object.entries(e).map((([e,t])=>({text:e,value:t}))).sort(((e,t)=>t.value-e.value));let l=0;for(const a of s)l+=a.value;for(const a of s){const e=a.value/l*100;t.push({text:a.text,value:Math.round(e)})}return t}(e.data.data))}))}),[]),e.useEffect((()=>{O()}),[S,k]),e.useEffect((()=>{H()}),[z,A]),e.useEffect((()=>{M()}),[B,R]);const O=()=>{if(j.current){let e=[],t=[];if(S&&S[k]){const s=S[k];e=s.dateList.map((e=>e)),t=s.numList.map((e=>e))}const s=x(j.current),l={tooltip:{trigger:"axis"},xAxis:{type:"category",data:e},yAxis:{type:"value"},series:[{data:t,type:"line",itemStyle:{color:"#0FB698"},lineStyle:{color:"#0FB698"},areaStyle:{color:new h(0,0,0,1,[{offset:0,color:"#0FB698"},{offset:1,color:"rgba(15, 182, 152, 0.20)"}])}}]};return s.setOption(l),()=>{s.dispose()}}},H=()=>{if(g.current){let e=[],t=[];if(z&&z[A]){const s=z[A];e=s.dateList.map((e=>e)),t=s.numList.map((e=>e))}const s=x(g.current),l={tooltip:{trigger:"axis"},xAxis:{type:"category",data:e},yAxis:{type:"value"},series:[{data:t,type:"line",itemStyle:{color:"#0FB698"},lineStyle:{color:"#0FB698"},areaStyle:{color:new h(0,0,0,1,[{offset:0,color:"#0FB698"},{offset:1,color:"rgba(15, 182, 152, 0.20)"}])}}]};return s.setOption(l),()=>{s.dispose()}}},M=()=>{if(v.current){let e=[],t=[];if(z&&z[A]){const s=z[A];e=s.dateList.map((e=>e)),t=s.numList.map((e=>e))}const s=x(v.current),l={tooltip:{trigger:"axis"},xAxis:{type:"category",data:e},yAxis:{type:"value"},series:[{data:t,type:"line",itemStyle:{color:"#0FB698"},lineStyle:{color:"#0FB698"},areaStyle:{color:new h(0,0,0,1,[{offset:0,color:"#0FB698"},{offset:1,color:"rgba(15, 182, 152, 0.20)"}])}}]};return s.setOption(l),()=>{s.dispose()}}},q=()=>{const s=e.useRef(),[l,a]=e.useState(),r=()=>{f.get("/analyse/overview").then((e=>{var t;200===(null==(t=e.data)?void 0:t.code)&&a(e.data.data)}))};return e.useEffect((()=>(clearInterval(s.current),r(),s.current=setInterval(r,5e3),()=>{s.current&&clearInterval(s.current)})),[]),t.jsx("div",{className:"createTaskArea",style:{height:"90px",minHeight:"unset",display:"flex",flexDirection:"row"},children:t.jsxs(d,{size:102,children:[t.jsx("label",{className:"mediumText",style:{fontSize:16,color:"#111"},children:"数据总览"}),t.jsxs(d,{split:t.jsx(u,{type:"vertical",style:{height:40,backgroundColor:"#E1F3FB"}}),size:65,children:[t.jsxs(d,{size:50,children:[t.jsx("label",{className:"boldText",style:{fontSize:16,color:"#000"},children:"用户总数"}),t.jsx(c,{value:(null==l?void 0:l.totalUserNum)||0,valueStyle:y})]}),t.jsxs(d,{size:50,children:[t.jsx("label",{className:"boldText",style:{fontSize:16,color:"#000"},children:"在线人数"}),t.jsx(c,{value:(null==l?void 0:l.onlineUserNum)||0,valueStyle:y})]}),t.jsxs(d,{size:50,children:[t.jsx("label",{className:"boldText",style:{fontSize:16,color:"#000"},children:"数据生成总数"}),t.jsx(c,{value:(null==l?void 0:l.dataGenerateNum)||0,valueStyle:y})]}),t.jsxs(d,{size:50,children:[t.jsx("label",{className:"boldText",style:{fontSize:16,color:"#000"},children:"数据审核总数"}),t.jsx(c,{value:(null==l?void 0:l.dataReviewNum)||0,valueStyle:y})]})]})]})})};return t.jsx(t.Fragment,{children:t.jsx(s,{children:t.jsxs("div",{className:"createTaskContent",style:{width:"76.3%"},children:[t.jsx("div",{style:{fontSize:"28px",lineHeight:"36px",fontWeight:"500",marginLeft:"2rem"},className:"mediumText",children:"数据看板"}),t.jsx(q,{}),t.jsxs(l,{gutter:24,children:[t.jsx(a,{span:12,children:t.jsxs("div",{style:{borderRadius:24,background:"#fff",height:430,padding:"20px 48px",textAlign:"center"},children:[t.jsx("label",{className:"mediumText",style:{fontSize:16,color:"#111"},children:"用户活跃度"}),t.jsx("div",{style:{textAlign:"end"},children:t.jsx(r,{menu:{items:m,onClick:({key:e})=>{N(e)}},children:t.jsxs(i,{className:"default-btn",children:[p(k),t.jsx(n,{})]})})}),t.jsx("div",{ref:j,style:{width:"100%",height:"400px"}})]})}),t.jsx(a,{span:12,children:t.jsxs("div",{style:{borderRadius:24,background:"#fff",height:430,padding:"20px 48px",textAlign:"center"},children:[t.jsx("label",{className:"mediumText",style:{fontSize:16,color:"#111"},children:"领域词云"}),t.jsx(o,{words:C,options:{fontSizes:[20,60],rotations:0,rotationAngles:[0,90]}})]})})]}),t.jsxs(l,{gutter:24,style:{marginTop:"2rem"},children:[t.jsx(a,{span:12,children:t.jsxs("div",{style:{borderRadius:24,background:"#fff",height:430,padding:"20px 48px",textAlign:"center"},children:[t.jsx("label",{className:"mediumText",style:{fontSize:16,color:"#111"},children:"生成数据统计"}),t.jsx("div",{style:{textAlign:"end"},children:t.jsx(r,{menu:{items:m,onClick:({key:e})=>{L(e)}},children:t.jsxs(i,{className:"default-btn",children:[p(R),t.jsx(n,{})]})})}),t.jsx("div",{ref:v,style:{width:"100%",height:"400px"}})]})}),t.jsx(a,{span:12,children:t.jsxs("div",{style:{borderRadius:24,background:"#fff",height:430,padding:"20px 48px",textAlign:"center"},children:[t.jsx("label",{className:"mediumText",style:{fontSize:16,color:"#111"},children:"审核数据统计"}),t.jsx("div",{style:{textAlign:"end"},children:t.jsx(r,{menu:{items:m,onClick:({key:e})=>{F(e)}},children:t.jsxs(i,{className:"default-btn",children:[p(A),t.jsx(n,{})]})})}),t.jsx("div",{ref:g,style:{width:"100%",height:"400px"}})]})})]})]})})})};export{j as default};
